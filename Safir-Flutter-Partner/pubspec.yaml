name: safir_driver
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  animated_splash_screen: ^1.3.0
  audioplayers: ^5.1.0
  change_app_package_name: ^1.3.0
  connectivity_plus: ^4.0.1
  flutter_contacts: ^1.1.9+2
  cupertino_icons: ^1.0.8
  device_info_plus: ^9.0.3
  external_app_launcher: ^4.0.3
  firebase_auth: ^4.7.1
  firebase_core: ^2.15.0
  firebase_database: ^10.2.4
  firebase_messaging: ^14.6.5
  flutter:
    sdk: flutter
  flutter_google_places_sdk: ^0.3.7
  flutter_inappwebview: ^6.1.5
  flutter_local_notifications: ^17.2.2
  flutter_map: ^6.1.0
  flutter_overlay_window: ^0.4.5
  geocoding: ^3.0.0
  geolocator: ^14.0.1
  google_fonts: ^4.0.4
  google_maps_flutter: ^2.1.12
  http: ^1.1.0
  image_picker: ^1.0.1
  intl: ^0.18.1
  location: ^5.0.0
  package_info_plus: ^4.0.2
  page_transition: ^2.1.0
  path_provider: ^2.0.11
  permission_handler: ^12.0.0+1
  pinput: ^2.2.14
  share_plus: ^7.0.2
  shared_preferences: ^2.0.12
  url_launcher: ^6.0.18
  uuid: ^4.5.1
  vector_math: ^2.1.1
  webview_flutter: ^4.2.4
  workmanager: ^0.6.0

dev_dependencies:

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  assets:
    - assets/images/
    - assets/audio/
    - assets/map_style.json
    - assets/dark.json
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
