import 'package:flutter/material.dart';
import 'package:safir_driver/styles/styles.dart';

class CustomCloseButton extends StatelessWidget {
  const CustomCloseButton({super.key, required this.media, this.onTap});

  final Size media;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: media.width * 0.9,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            height: media.height * 0.1,
            width: media.width * 0.1,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: page,
            ),
            child: InkWell(
              onTap: onTap,
              child: Icon(Icons.cancel_outlined, color: textColor),
            ),
          ),
        ],
      ),
    );
  }
}
