abstract class AppAssets {
  static const String logo = 'assets/images/logo.jpeg';
  static const String logoWithoutBackground = 'assets/images/logo_without.png';
  static const String background4 = 'assets/images/stars_pattern3.png';
  static const String car = 'assets/images/car.png';
  static const String carRtl = 'assets/images/car_rtl.png';
  static const String loading = 'assets/images/loading.json';
  static const String privacy = 'assets/images/privacyimage.png';
  static const String send = 'assets/images/send.png';
  static const String faq = 'assets/images/faqPage.png';
  static const String noLocation = 'assets/images/nolocation.png';
  static const String briefcase = 'assets/images/briefcase.png';
  static const String navigation = 'assets/images/navigation.png';
  static const String referral = 'assets/images/referralpage.png';
  static const String tune = 'assets/images/Tune.png';
  static const String noDataFoundDark = 'assets/images/nodatafounddark.gif';
  static const String noDataFound = 'assets/images/nodatafound.gif';
  static const String history = 'assets/images/history.png';
  static const String referralIcon = 'assets/images/referral.png';
  static const String chevronDown = 'assets/images/chevron-down.png';
  static const String selectLanguage = 'assets/images/selectLanguage.png';
  static const String noInternet = 'assets/images/noInternet.png';
  static const String cash = 'assets/images/cash.png';
  static const String wallet = 'assets/images/wallet.png';
  static const String card = 'assets/images/card.png';
  static const String upi = 'assets/images/upi.png';
  static const String locationPerm = 'assets/images/location_perm.png';
  static const String pickIcon = 'assets/images/pick_icon.png';
  static const String dropMarker = 'assets/images/dropmarker.png';
  static const String customerCare = 'assets/images/customercare.png';
  static const String rectangle = 'assets/images/Rectangle.png';
  static const String taxia = 'assets/images/taxia.png';
  static const String noOrder = 'assets/images/noorder.png';
  static const String defaultProfilePicture =
      'assets/images/default-profile-picture.jpeg';
  static const String earing = 'assets/images/earing.png';
  static const String manageDriver = 'assets/images/managedriver.png';
  static const String updateVehicleInfo = 'assets/images/updateVehicleInfo.png';
  static const String myRoute = 'assets/images/myroute.png';
  static const String allowLocationPermission =
      'assets/images/allow_location_permission.png';
  static const String blueCar = 'assets/images/bluecar.png';
  static const String locationFind = 'assets/images/locationFind.png';
  static const String waze = 'assets/images/waze.png';
  static const String googleMaps = 'assets/images/googlemaps.png';
  static const String offline = 'assets/images/offline.png';
  static const String landImage = 'assets/images/landimage.png';
  static const String doneIcon = 'assets/images/done_icon.png';
  static const String personIcon = 'assets/images/person_icon.png';
  static const String carInformation = 'assets/images/car_information.png';
  static const String cancelIcon = 'assets/images/cancel_icon.png';
  static const String documentIcon = 'assets/images/document_icon.png';
  static const String proposalApproval = 'assets/images/proposal-approval.png';
  static const String wait = 'assets/images/wait.png';
  static const String sosDark = 'assets/images/sosd.gif';
  static const String sosLight = 'assets/images/sosl.gif';
  static const String walletLight = 'assets/images/walletl.gif';
  static const String walletDark = 'assets/images/walletd.gif';
  static const String noTransactionDark =
      'assets/images/no_transactiondark.gif';
  static const String noTransaction = 'assets/images/no_transaction.gif';
  static const String paymentSuccess = 'assets/images/paymentsuccess.png';
  // assets/images/disablecar.png
  static const String disableCar = 'assets/images/disablecar.png';
  //  'assets/images/approved.png'
  static const String approved = 'assets/images/approved.png';
  // 'assets/images/wait.png'
  static const String waitIcon = 'assets/images/wait.png';
}
