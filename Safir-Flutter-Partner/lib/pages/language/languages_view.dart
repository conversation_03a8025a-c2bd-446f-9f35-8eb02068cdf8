import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../login/landing_view.dart';

class LanguagesView extends StatefulWidget {
  const LanguagesView({Key? key}) : super(key: key);

  @override
  State<LanguagesView> createState() => _LanguagesViewState();
}

class _LanguagesViewState extends State<LanguagesView> {
  @override
  void initState() {
    choosenLanguage = 'ar';
    languageDirection = 'rtl';
    super.initState();
  }

  navigate() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const LandingView()),
    );
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Container(
            padding: EdgeInsets.all(media.width * 0.05),
            height: media.height * 1,
            width: media.width * 1,
            color: page,
            child: Column(
              children: [
                MyText(
                  text: (choosenLanguage.isEmpty)
                      ? 'اختار اللغة'
                      : languages[choosenLanguage]['text_choose_language'],
                  size: media.width * sixteen,
                  fontweight: FontWeight.bold,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: media.width * 0.9,
                  height: media.height * 0.16,
                  child: Image.asset(
                    AppAssets.selectLanguage,
                    fit: BoxFit.contain,
                  ),
                ),
                const SizedBox(height: 24),
                chooseLanguageOptions(media),
                const SizedBox(height: 20),
                if (choosenLanguage != '') chooseLanguageButton(media),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget chooseLanguageOptions(Size media) {
    return Expanded(
      child: Column(
        children: languages
            .map(
              (i, value) => MapEntry(
                i,
                InkWell(
                  onTap: () {
                    setState(() {
                      choosenLanguage = i;
                      if (choosenLanguage == 'ar' ||
                          choosenLanguage == 'ur' ||
                          choosenLanguage == 'iw') {
                        languageDirection = 'rtl';
                      } else {
                        languageDirection = 'ltr';
                      }
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(media.width * 0.025),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MyText(
                          text: languagesCode
                              .firstWhere((e) => e['code'] == i)['name']
                              .toString(),
                          size: media.width * sixteen,
                        ),
                        Container(
                          height: media.width * 0.05,
                          width: media.width * 0.05,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isDarkTheme
                                  ? Colors.white
                                  : const Color(0xff222222),
                              width: 1.2,
                            ),
                          ),
                          alignment: Alignment.center,
                          child: (choosenLanguage == i)
                              ? Container(
                                  height: media.width * 0.03,
                                  width: media.width * 0.03,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: isDarkTheme
                                        ? Colors.white
                                        : const Color(0xff222222),
                                  ),
                                )
                              : Container(),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            )
            .values
            .toList(),
      ),
    );
  }

  Widget chooseLanguageButton(Size media) {
    return Button(
      onTap: () async {
        await getlangid();
        pref.setString('languageDirection', languageDirection);
        pref.setString('choosenLanguage', choosenLanguage);
        navigate();
      },
      text: languages[choosenLanguage]['text_confirm'],
    );
  }
}
