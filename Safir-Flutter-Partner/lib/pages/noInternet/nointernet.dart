import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';

// ignore: must_be_immutable
class NoInternet extends StatefulWidget {
  // const NoInternet({ Key? key }) : super(key: key);
  final dynamic onTap;
  // ignore: use_key_in_widget_constructors
  const NoInternet({required this.onTap});

  @override
  State<NoInternet> createState() => _NoInternetState();
}

class _NoInternetState extends State<NoInternet> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Container(
      height: media.height * 1,
      width: media.width * 1,
      color: page,
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              SizedBox(
                width: media.width * 0.6,
                child: Image.asset(
                  AppAssets.noInternet,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 24),
              MyText(
                text: (languages.isNotEmpty && choosenLanguage != '')
                    ? languages[choosenLanguage]['text_nointernet']
                    : 'No Internet Connection\nPlease check your Internet connection and try again later',
                size: media.width * twentyfour,
                fontweight: FontWeight.w600,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              MyText(
                text: (languages.isNotEmpty && choosenLanguage != '')
                    ? languages[choosenLanguage]['text_nointernetdesc']
                    : 'Please check your Internet connection, try enabling wifi or tey again later',
                size: media.width * fourteen,
                color: hintColor,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              Button(
                onTap: widget.onTap,
                text: languages[choosenLanguage]['text_back_home'],
              )
            ],
          )
        ],
      ),
    );
  }
}
