import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import 'login_view.dart';

class LandingView extends StatefulWidget {
  const LandingView({super.key});

  @override
  State<LandingView> createState() => _LandingViewState();
}

class _LandingViewState extends State<LandingView> {
  bool colorbutton = false;

  @override
  void initState() {
    checkmodule();
    super.initState();
  }

  checkmodule() {
    if (ownermodule == '0') {
      ischeckownerordriver == 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Container(
          color: page,
          child: Column(
            children: [
              Expanded(child: landingImage(media)),
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 60),
                    child: Column(
                      children: [
                        loginDriverButton(media),
                        const SizedBox(height: 30),
                        loginOwnerButton(media),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget landingImage(Size media) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppAssets.logoWithoutBackground),
        ),
      ),
    );
  }

  Widget loginDriverButton(Size media) {
    return InkWell(
      onTap: () {
        ischeckownerordriver = 'driver';
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
        );
      },
      child: Container(
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(media.width * thirty),
          color: buttonColor,
          boxShadow: [
            BoxShadow(
              color: buttonColor,
              spreadRadius: 1,
            ),
          ],
        ),
        child: MyText(
          text: languages[choosenLanguage]['text_login_driver'],
          size: media.width * fourteen,
          fontweight: FontWeight.bold,
          color: buttonText,
        ),
      ),
    );
  }

  Widget loginOwnerButton(Size media) {
    return InkWell(
      onTap: () {
        ischeckownerordriver = 'owner';
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
        );
      },
      child: Container(
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(media.width * thirty),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: buttonColor,
              spreadRadius: 1,
            ),
          ],
        ),
        child: MyText(
          text: languages[choosenLanguage]['text_login_owner'],
          size: media.width * fourteen,
          color: Colors.black,
          fontweight: FontWeight.bold,
        ),
      ),
    );
  }
}
