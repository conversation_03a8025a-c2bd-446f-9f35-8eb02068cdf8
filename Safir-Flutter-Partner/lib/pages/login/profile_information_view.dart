import 'package:flutter/material.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import 'fill_car_info_view.dart';
import 'login_view.dart';
import 'fill_user_info_view.dart';
import 'required_information_view.dart';

// ignore: must_be_immutable
class ProfileInformationView extends StatefulWidget {
  dynamic from;
  ProfileInformationView({Key? key, this.from}) : super(key: key);

  @override
  State<ProfileInformationView> createState() => _ProfileInformationViewState();
}

class _ProfileInformationViewState extends State<ProfileInformationView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  TextEditingController firstname = TextEditingController();
  TextEditingController emailText = TextEditingController();
  TextEditingController lastname = TextEditingController();
  TextEditingController mobile = TextEditingController();
  TextEditingController pinText = TextEditingController();
  bool _isLoading = true;
  bool chooseWorkArea = false;
  String _error = '';

  @override
  void initState() {
    countryCode();
    // getServiceLoc();
    super.initState();
    _controller = AnimationController(vsync: this);
  }

  countryCode() async {
    if (widget.from == null) {
      firstname.text = name.toString().split(' ')[0];
      lastname.text = name.toString().split(' ')[1];
      mobile.text = phnumber;
      emailText.text = email;
    } else {
      firstname.text = userDetails['name'].toString().split(' ')[0];
      lastname.text = (userDetails['name'].toString().split(' ').length > 1)
          ? userDetails['name'].toString().split(' ')[1]
          : '';
      mobile.text = userDetails['mobile'];
      emailText.text = userDetails['email'];
    }
    _isLoading = false;
    setState(() {});
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                child: Column(
                  children: [
                    profileInformationAppBar(media),
                    const SizedBox(height: 20),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MyText(
                              text: languages[choosenLanguage]
                                      ['text_profile_info']
                                  .toString()
                                  .toUpperCase(),
                              size: media.width * fourteen,
                              fontweight: FontWeight.bold,
                            ),
                            const SizedBox(height: 20),
                            profileInformationNameSection(media),
                            const SizedBox(height: 20),
                            profileInformationPhoneSection(media),
                            const SizedBox(height: 20),
                            profileInformationEmailSection(media),
                          ],
                        ),
                      ),
                    ),
                    if (_error != '') profileInformationError(media),
                  ],
                ),
              ),
              if (_isLoading == true) const Positioned(child: Loading())
            ],
          ),
        ),
      ),
    );
  }

  Widget profileInformationAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_reqinfo'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget profileInformationNameSection(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MyText(
          text: languages[choosenLanguage]['text_name'],
          size: media.width * sixteen,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Container(
                height: media.width * 0.13,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: hintColor),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: MyTextField(
                  textController: firstname,
                  hinttext: languages[choosenLanguage]['text_first_name'],
                  onTap: (val) => setState(() {}),
                  readonly: (widget.from == null) ? true : false,
                  color: textColor,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Container(
                height: media.width * 0.13,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: hintColor),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: MyTextField(
                  textController: lastname,
                  hinttext: languages[choosenLanguage]['text_last_name'],
                  onTap: (val) => setState(() {}),
                  readonly: (widget.from == null) ? true : false,
                  color: textColor,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget profileInformationPhoneSection(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MyText(
          text: languages[choosenLanguage]['text_mob_num'],
          size: media.width * sixteen,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 12),
        Container(
          height: media.width * 0.13,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: hintColor),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: MyTextField(
            readonly: true,
            textController: mobile,
            hinttext: languages[choosenLanguage]['text_enter_phone_number'],
            onTap: (val) => setState(() {}),
            color: textColor,
          ),
        ),
      ],
    );
  }

  Widget profileInformationEmailSection(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MyText(
          text: languages[choosenLanguage]['text_email'],
          size: media.width * sixteen,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 12),
        Container(
          height: media.width * 0.13,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: hintColor),
          ),
          padding: const EdgeInsets.only(left: 5, right: 5),
          child: MyTextField(
            textController: emailText,
            hinttext: languages[choosenLanguage]['text_enter_email'],
            onTap: (val) => setState(() {}),
            color: textColor,
          ),
        ),
      ],
    );
  }

  Widget profileInformationButton(Size media) {
    return Button(
      onTap: () async {
        setState(() => _error = '');
        String pattern =
            r"^[A-Za-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[A-Za-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])?\.)+[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])*$";
        var remail = emailText.text.replaceAll(' ', '');
        RegExp regex = RegExp(pattern);
        if (regex.hasMatch(remail)) {
          if (widget.from == null) {
            if (myServiceId != '' && myServiceId != null) {
              profileCompleted = true;
              Navigator.pop(context, true);
            }
          } else {
            setState(() => _isLoading = true);
            // ignore: prefer_typing_uninitialized_variables
            var nav;
            if (userDetails['email'] == remail) {
              // print('started');
              nav = await updateProfile(
                '${firstname.text} ${lastname.text}',
                remail,
                // userDetails['mobile']
              );
              if (nav != 'success') {
                _error = nav.toString();
              } else {
                // ignore: use_build_context_synchronously
                Navigator.pop(context, true);
              }
            } else {
              var result = await validateEmail(remail);
              if (result == 'success') {
                nav = await updateProfile(
                  '${firstname.text} ${lastname.text}',
                  remail,
                  // userDetails['mobile']
                );
                if (nav != 'success') {
                  _error = nav.toString();
                } else {
                  // ignore: use_build_context_synchronously
                  Navigator.pop(context, true);
                }
              } else {
                setState(() => _error = result);
              }
            }

            setState(() => _isLoading = false);
          }
        } else {
          setState(() {
            _error = languages[choosenLanguage]['text_email_validation'];
          });
        }
      },
      text: languages[choosenLanguage]['text_confirm'],
    );
  }

  Widget profileInformationError(Size media) {
    return Container(
      width: media.width * 0.9,
      padding: EdgeInsets.only(
        top: media.width * 0.02,
        bottom: media.width * 0.02,
      ),
      child: MyText(
        text: _error,
        size: media.width * twelve,
        color: Colors.red,
        textAlign: TextAlign.center,
      ),
    );
  }
}
