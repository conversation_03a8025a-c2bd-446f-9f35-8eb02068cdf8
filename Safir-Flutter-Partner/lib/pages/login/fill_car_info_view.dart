import 'package:flutter/material.dart';
import 'package:safir_driver/pages/NavigatorPages/manage_vehicles_view.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import 'login_view.dart';
import '../noInternet/nointernet.dart';
import 'required_information_view.dart';

// ignore: must_be_immutable
class FillCarInfoView extends StatefulWidget {
  int? frompage;
  FillCarInfoView({this.frompage, super.key});

  @override
  State<FillCarInfoView> createState() => _FillCarInfoViewState();
}

bool isowner = false;
dynamic myVehicalType;
dynamic myVehicleIconFor = '';
List vehicletypelist = [];
dynamic vehicleColor;
dynamic myServiceLocation;
dynamic myServiceId;
String vehicleModelId = '';
dynamic vehicleModelName;
dynamic modelYear;
String vehicleMakeId = '';
dynamic vehicleNumber;
dynamic vehicleMakeName;
String myVehicleId = '';
String mycustommake = '';
String mycustommodel = '';
List choosevehicletypelist = [];
List choosevehicletypelistlocal = [];

class _FillCarInfoViewState extends State<FillCarInfoView> {
  bool loaded = false;
  bool chooseWorkArea = false;
  bool _isLoading = false;
  String _error = '';
  bool chooseVehicleMake = false;
  bool chooseVehicleModel = false;
  bool chooseServiceCategory = false;
  bool chooseVehicleType = false;
  String dateError = '';
  bool vehicleAdded = false;
  String uploadError = '';
  bool iscustommake = false;
  TextEditingController modelcontroller = TextEditingController();
  TextEditingController colorcontroller = TextEditingController();
  TextEditingController numbercontroller = TextEditingController();
  TextEditingController referralcontroller = TextEditingController();
  TextEditingController custommakecontroller = TextEditingController();
  TextEditingController custommodelcontroller = TextEditingController();
  double isbottom = -1000;
  dynamic tempVehicleModelId;
  dynamic tempServiceLocationId;
  dynamic tempServiceCategory;
  dynamic tempVehicleMakeId;
  //navigate
  navigate() {
    Navigator.pop(context, true);
    serviceLocations.clear();
    vehicleMake.clear();
    vehicleModel.clear();
    vehicleType.clear();
    serviceCategory.clear();
  }

  navigateref() {
    Navigator.pop(context, true);
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  @override
  void initState() {
    getServiceLoc();
    super.initState();
  }

//get service loc data
  getServiceLoc() async {
    choosevehicletypelist.clear();
    vehicletypelist.clear();
    myServiceId = '';
    myServiceLocation = '';
    vehicleMakeId = '';
    vehicleModelId = '';
    myVehicleId = '';
    await getServiceCategory();  // TODO: check this line
    // ignore: unused_local_variable, prefer_typing_uninitialized_variables
    var result;
    if (widget.frompage == 2 || isowner == true) {
      myVehicleId = '';
      result = await getvehicleType();
    } else {
      vehicletypelist = [];
      result = await getServiceLocation();
    }

    if (mounted) {
      setState(() {
        loaded = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            body: Stack(
              children: [
                Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      carInfoAppBar(media),
                      const SizedBox(height: 20),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (widget.frompage == 1 && isowner == false)
                                vehicleServiceLocationText(media),
                              const SizedBox(height: 8),
                              if (widget.frompage == 1 && isowner == false)
                                vehicleServiceLocationField(media),
                              const SizedBox(height: 8),
                              serviceCategoryText(media),
                              const SizedBox(height: 8),
                              serviceCategoryField(media),
                              const SizedBox(height: 20),
                              vehicleTypeText(media),
                              const SizedBox(height: 8),
                              vehicleTypeField(media),
                              const SizedBox(height: 20),
                              vehicleMakeText(media),
                              const SizedBox(height: 8),
                              vehicleMakeField(media),
                              const SizedBox(height: 20),
                              vehicleModelText(media),
                              const SizedBox(height: 8),
                              vehicleModelField(media),
                              const SizedBox(height: 20),
                              vehicleModelYearText(media),
                              const SizedBox(height: 8),
                              vehicleModelYearField(media),
                              if (dateError != '') vehicleDateError(media),
                              const SizedBox(height: 20),
                              vehicleNumberText(media),
                              const SizedBox(height: 8),
                              vehicleNumberField(media),
                              const SizedBox(height: 20),
                              vehicleColorText(media),
                              const SizedBox(height: 8),
                              vehicleColorField(media),
                              if (widget.frompage == 1 && isowner != true)
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 20),
                                    vehicleReferralText(media),
                                    const SizedBox(height: 8),
                                    vehicleReferralField(media),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                      if (_error != '') carInfoError(media),
                      carInfoButton(media),
                    ],
                  ),
                ),
                carInfoBottomSheet(media),
                if (vehicleAdded == true) vehicleAddedPopup(media),
                if (internet == false) carInfoNoInternet(media),
                if (_isLoading == true) const Positioned(child: Loading()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget vehicleServiceLocationText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_service_location'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleServiceLocationField(Size media) {
    return InkWell(
      onTap: () async {
        setState(() {
          if (chooseWorkArea == true) {
            chooseWorkArea = false;
            isbottom = -1000;
          } else {
            chooseWorkArea = true;
            chooseVehicleMake = false;
            chooseVehicleModel = false;
            chooseVehicleType = false;
            isbottom = 0;
          }
        });
      },
      child: Container(
        height: media.width * 0.13,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: hintColor),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: media.width * 0.7,
              child: Text(
                (widget.frompage == 1 && myServiceId == '')
                    ? languages[choosenLanguage]['text_service_loc']
                    : (myServiceId != null && myServiceId != '')
                        ? serviceLocations.isNotEmpty
                            ? serviceLocations
                                .firstWhere((element) =>
                                    element['id'] == myServiceId)['name']
                                .toString()
                            : ''
                        : userDetails['service_location_name'],
                style: GoogleFonts.notoKufiArabic(
                  fontSize: (myServiceId != null && myServiceId != '')
                      ? media.width * sixteen
                      : media.width * fourteen,
                  color: (myServiceId != null && myServiceId != '') ||
                          widget.frompage == 1
                      ? textColor
                      : hintColor,
                ),
              ),
            ),
            Icon(
              Icons.place,
              color: textColor,
            )
          ],
        ),
      ),
    );
  }

  Widget serviceCategoryText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_service_category'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget serviceCategoryField(Size media) {
    return InkWell(
      onTap: () {
        setState(() {
          if (chooseServiceCategory == true) {
            tempServiceCategory = selectedServiceCategory;
            chooseServiceCategory = false;
            isbottom = -1000;
          } else {
            chooseServiceCategory = true;
            isbottom = 0;
          }
        });
      },
      child: Container(
        height: media.width * 0.13,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: hintColor),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(
              text: (selectedServiceCategory.isEmpty)
                  ? languages[choosenLanguage]['text_service_category']
                  : selectedServiceCategory['title'],
              size: (selectedServiceCategory.isNotEmpty)
                  ? media.width * sixteen
                  : media.width * fourteen,
              color: (selectedServiceCategory.isEmpty)
                  ? hintColor
                  : (selectedServiceCategory.isNotEmpty &&
                          serviceCategory.isNotEmpty)
                      ? textColor
                      : selectedServiceCategory.isEmpty
                          ? hintColor
                          : textColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget vehicleTypeText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_vehicle_type'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleTypeField(Size media) {
    return (userDetails['vehicle_type_name'] == null &&
            userDetails['role'] != 'owner')
        ? InkWell(
            onTap: () async {
              if (chooseVehicleType == true) {
                setState(() {
                  chooseVehicleType = false;
                  isbottom = 0;
                });
              } else {
                if ((myServiceId != '') || (isowner == true)) {
                  chooseVehicleType = true;
                } else {
                  chooseVehicleType = false;
                }
                chooseWorkArea = false;
                chooseVehicleMake = false;
                chooseVehicleModel = false;
                isbottom = 0;
              }
              setState(() {});
            },
            child: Container(
              height: media.width * 0.13,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: hintColor),
                color: page,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: (myVehicleId == '')
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: MyText(
                            text: languages[choosenLanguage]
                                    ['text_vehicle_type']
                                .toString(),
                            size: media.width * fourteen,
                            color: hintColor,
                          ),
                        ),
                        if ((myServiceId == null || myServiceId == '') &&
                            widget.frompage == 1 &&
                            isowner == false)
                          Icon(
                            Icons.lock_outline_rounded,
                            size: media.width * 0.05,
                            color: textColor,
                          )
                      ],
                    )
                  : Row(
                      children: [
                        SizedBox(
                          width: media.width * 0.74,
                          child: (choosevehicletypelist.isNotEmpty)
                              ? SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    children: choosevehicletypelist
                                        .asMap()
                                        .map((i, value) {
                                          return MapEntry(
                                            i,
                                            Container(
                                              padding: EdgeInsets.fromLTRB(
                                                  media.width * 0.03,
                                                  media.width * 0.01,
                                                  media.width * 0.03,
                                                  media.width * 0.01),
                                              margin: EdgeInsets.all(
                                                  media.width * 0.01),
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                color: buttonColor,
                                              ),
                                              child: Row(
                                                children: [
                                                  MyText(
                                                    text:
                                                        choosevehicletypelist[i]
                                                                ['name']
                                                            .toString(),
                                                    size: media.width * sixteen,
                                                    color: textColor,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        })
                                        .values
                                        .toList(),
                                  ),
                                )
                              : MyText(
                                  text: languages[choosenLanguage]
                                          ['text_vehicle_type']
                                      .toString(),
                                  size: sixteen,
                                  color: textColor,
                                ),
                        ),
                      ],
                    ),
            ),
          )
        : InkWell(
            onTap: () async {
              if (chooseVehicleType == true) {
                setState(() {
                  chooseVehicleType = false;
                });
              } else {
                if (myServiceId != '' || isowner == true) {
                  isbottom = 0.0;
                  chooseVehicleType = true;
                } else {
                  chooseVehicleType = false;
                }
                chooseWorkArea = false;
                chooseVehicleMake = false;
                chooseVehicleModel = false;
              }
              setState(() {});
            },
            child: Container(
              height: media.width * 0.13,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(media.width * 0.01),
                  border: Border.all(color: textColor),
                  color: ((myServiceId == null || myServiceId == '') &&
                          widget.frompage == 1 &&
                          isowner == false)
                      ? const Color(0xFFE8EAE9)
                      : topBar),
              padding: EdgeInsets.only(
                  left: media.width * 0.05, right: media.width * 0.05),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: media.width * 0.7,
                    child: Text(
                      (myVehicleId == '')
                          ? languages[choosenLanguage]['text_vehicle_type']
                              .toString()
                          : (myVehicleId != '' && myVehicleId != '')
                              ? vehicleType.isNotEmpty
                                  ? vehicleType
                                      .firstWhere((element) =>
                                          element['id'] == myVehicleId)['name']
                                      .toString()
                                  : ''
                              : myVehicalType.toString(),
                      style: GoogleFonts.notoKufiArabic(
                          fontSize: (myVehicleId != '')
                              ? media.width * sixteen
                              : media.width * fourteen,
                          // fontWeight: FontWeight.w600,
                          color: (widget.frompage == 1 && (myVehicleId == ''))
                              ? (isDarkTheme)
                                  ? Colors.black.withOpacity(0.5)
                                  : textColor
                              : (isDarkTheme)
                                  ? Colors.black
                                  : textColor),
                    ),
                  ),
                ],
              ),
            ),
          );
  }

  Widget vehicleMakeText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_vehicle_make'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleMakeField(Size media) {
    return InkWell(
      onTap: () {
        setState(() {
          if (chooseVehicleMake == true) {
            chooseVehicleMake = false;
          } else {
            if (myVehicleId != '') {
              chooseVehicleMake = true;
              isbottom = 0;
            } else {
              chooseVehicleMake = false;
            }
            chooseWorkArea = false;
            chooseVehicleModel = false;
            chooseVehicleType = false;
          }
        });
      },
      child: (iscustommake == false)
          ? Container(
              height: media.width * 0.13,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: hintColor),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MyText(
                    text: (vehicleMakeId == '')
                        ? languages[choosenLanguage]['text_sel_make']
                        : (vehicleMakeId != '')
                            ? vehicleMake.isNotEmpty
                                ? vehicleMake
                                    .firstWhere((element) =>
                                        element['id'].toString() ==
                                        vehicleMakeId)['name']
                                    .toString()
                                : ''
                            : vehicleMakeName == ''
                                ? languages[choosenLanguage]
                                    ['text_vehicle_make']
                                : vehicleMakeName,
                    size: (vehicleMakeId != '')
                        ? media.width * sixteen
                        : media.width * fourteen,
                    color: (vehicleMakeId == '')
                        ? hintColor
                        : (vehicleMakeId != '')
                            ? textColor
                            : vehicleMakeName == ''
                                ? hintColor
                                : textColor,
                  ),
                  if (myVehicleId == '')
                    Icon(
                      Icons.lock_outline_rounded,
                      size: media.width * 0.05,
                      color: textColor,
                    )
                ],
              ),
            )
          : Container(
              height: media.width * 0.13,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: hintColor),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: InputField(
                underline: false,
                autofocus: true,
                text: languages[choosenLanguage]['text_sel_make'],
                textController: custommakecontroller,
                onTap: (val) => setState(() => mycustommake = val),
                color: textColor,
              ),
            ),
    );
  }

  Widget vehicleModelText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_vehicle_model'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleModelField(Size media) {
    return (iscustommake)
        ? Container(
            height: media.width * 0.13,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: hintColor),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: InputField(
              underline: false,
              autofocus: true,
              text: languages[choosenLanguage]['text_sel_model'],
              textController: custommodelcontroller,
              onTap: (val) => setState(() => mycustommodel = val),
              color: textColor,
            ),
          )
        : InkWell(
            onTap: () {
              setState(() {
                if (chooseVehicleModel == true) {
                  chooseVehicleModel = false;
                } else {
                  if (vehicleMakeId != '') {
                    chooseVehicleModel = true;
                    isbottom = 0;
                  } else {
                    chooseVehicleModel = false;
                  }
                  chooseVehicleMake = false;
                  chooseWorkArea = false;
                  chooseVehicleType = false;
                }
              });
            },
            child: Container(
              height: media.width * 0.13,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: hintColor),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MyText(
                    text: (vehicleModelId == '')
                        ? languages[choosenLanguage]['text_sel_model']
                        : (vehicleModelId != '' &&
                                vehicleModelId != '' &&
                                vehicleModel.isNotEmpty)
                            ? vehicleModel
                                .firstWhere((element) =>
                                    element['id'].toString() ==
                                    vehicleModelId)['name']
                                .toString()
                            : vehicleModelName == ''
                                ? languages[choosenLanguage]
                                    ['text_vehicle_model']
                                : vehicleModelName,
                    size: (vehicleModelId != '' && vehicleModelId != '')
                        ? media.width * sixteen
                        : media.width * fourteen,
                    color: (vehicleModelId == '')
                        ? hintColor
                        : (vehicleModelId != '' &&
                                vehicleModelId != '' &&
                                vehicleModel.isNotEmpty)
                            ? textColor
                            : vehicleModelName == ''
                                ? hintColor
                                : textColor,
                  ),
                  if (vehicleMakeId == '')
                    Icon(
                      Icons.lock_outline_rounded,
                      size: media.width * 0.05,
                      color: textColor,
                    )
                ],
              ),
            ),
          );
  }

  Widget vehicleModelYearText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_vehicle_model_year'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleModelYearField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: hintColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: InputField(
              readonly:
                  ((iscustommake) ? mycustommodel == '' : vehicleModelId == '')
                      ? true
                      : false,
              underline: false,
              text: languages[choosenLanguage]['text_enter_vehicle_model_year'],
              textController: modelcontroller,
              onTap: (val) {
                setState(() => modelYear = modelcontroller.text);
                if (modelcontroller.text.length == 4 &&
                    int.parse(modelYear) <=
                        int.parse(DateTime.now().year.toString())) {
                  setState(() => dateError = '');
                  FocusManager.instance.primaryFocus?.unfocus();
                } else if (modelcontroller.text.length == 4 &&
                    int.parse(modelYear) >
                        int.parse(DateTime.now().year.toString())) {
                  setState(() => dateError = 'Please Enter Valid Date');
                }
              },
              color: (dateError == '') ? textColor : Colors.red,
              inputType: TextInputType.number,
              maxLength: 4,
            ),
          ),
          if ((iscustommake) ? mycustommodel == '' : vehicleModelId == '')
            Icon(
              Icons.lock_outline_rounded,
              size: media.width * 0.05,
              color: textColor,
            )
        ],
      ),
    );
  }

  Widget vehicleDateError(Size media) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: MyText(
        text: dateError,
        size: media.width * sixteen,
        color: Colors.red,
      ),
    );
  }

  Widget vehicleNumberText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_enter_vehicle'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleNumberField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: hintColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: InputField(
              color: textColor,
              readonly:
                  ((iscustommake) ? mycustommodel == '' : vehicleModelId == '')
                      ? true
                      : false,
              underline: false,
              text: languages[choosenLanguage]['text_enter_vehicle'],
              textController: numbercontroller,
              onTap: (val) {
                setState(() {
                  vehicleNumber = numbercontroller.text;
                });
              },
              maxLength: 20,
            ),
          ),
          if ((iscustommake) ? mycustommodel == '' : vehicleModelId == '')
            Icon(
              Icons.lock_outline_rounded,
              size: media.width * 0.05,
              color: textColor,
            )
        ],
      ),
    );
  }

  Widget vehicleColorText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_vehicle_color'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleColorField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: hintColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: InputField(
              color: textColor,
              readonly:
                  ((iscustommake) ? mycustommodel == '' : vehicleModelId == '')
                      ? true
                      : false,
              underline: false,
              text: languages[choosenLanguage]['Text_enter_vehicle_color'],
              textController: colorcontroller,
              onTap: (val) {
                setState(() {
                  vehicleColor = colorcontroller.text;
                });
              },
            ),
          ),
          if ((iscustommake) ? mycustommodel == '' : vehicleModelId == '')
            Icon(
              Icons.lock_outline_rounded,
              size: media.width * 0.05,
              color: textColor,
            )
        ],
      ),
    );
  }

  Widget vehicleReferralText(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_referral_optional'],
      size: media.width * fifteen,
      fontweight: FontWeight.w500,
    );
  }

  Widget vehicleReferralField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: hintColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: InputField(
        color: textColor,
        text: languages[choosenLanguage]['text_enter_referral'],
        textController: referralcontroller,
        onTap: (val) => setState(() {}),
      ),
    );
  }

  Widget carInfoButton(Size media) {
    return (numbercontroller.text != '' && numbercontroller.text.length < 21) &&
            (myVehicleId != '' || choosevehicletypelist.isNotEmpty) &&
            ((iscustommake) ? mycustommake != '' : vehicleMakeId != '') &&
            ((iscustommake) ? mycustommodel != '' : vehicleModelId != '') &&
            (modelcontroller.text.length == 4 &&
                (int.parse(modelYear) <=
                    int.parse(DateTime.now().year.toString()))) &&
            (colorcontroller.text.isNotEmpty) && selectedServiceCategory.isNotEmpty
        ? Container(
            padding: EdgeInsets.all(media.width * 0.05),
            child: Button(
              onTap: () async {
                FocusManager.instance.primaryFocus?.unfocus();
                setState(() {
                  _error = '';
                  _isLoading = true;
                });
                if (widget.frompage == 1 &&
                    userDetails.isNotEmpty &&
                    isowner != true) {
                  if (referralcontroller.text.isNotEmpty) {
                    var val = await updateReferral(referralcontroller.text);
                    if (val == 'true') {
                      carInformationCompleted = true;
                      navigateref();
                    } else {
                      setState(() {
                        referralcontroller.clear();
                        _error =
                            languages[choosenLanguage]['text_referral_code'];
                        _isLoading = false;
                      });
                    }
                  } else {
                    carInformationCompleted = true;
                    navigateref();
                  }
                } else if (userDetails.isEmpty) {
                  vehicletypelist.clear();
                  for (Map<String, dynamic> json in choosevehicletypelist) {
                    // Get the value of the key.
                    vehicletypelist.add(json['id']);
                  }

                  var reg = await registerDriver();

                  if (reg == 'true') {
                    if (referralcontroller.text.isNotEmpty) {
                      var val = await updateReferral(referralcontroller.text);
                      if (val == 'true') {
                        carInformationCompleted = true;
                        navigateref();
                      } else {
                        setState(() {
                          referralcontroller.clear();
                          _error =
                              languages[choosenLanguage]['text_referral_code'];
                          _isLoading = false;
                        });
                      }
                    } else {
                      carInformationCompleted = true;
                      navigateref();
                    }
                  } else {
                    setState(() {
                      uploadError = reg.toString();
                    });
                  }
                  setState(() {
                    _isLoading = false;
                  });
                } else if (userDetails['role'] == 'owner') {
                  vehicletypelist.add(choosevehicletypelist[0]['id']);
                  var reg = await addDriver();
                  setState(() {
                    _isLoading = false;
                  });
                  if (reg == 'true') {
                    // ignore: use_build_context_synchronously
                    // setState(() {
                    //   vehicleAdded = true;
                    // });
                    showModalBottomSheet(
                        // ignore: use_build_context_synchronously
                        context: context,
                        isScrollControlled: false,
                        isDismissible: false,
                        builder: (context) {
                          return Container(
                            padding: EdgeInsets.all(media.width * 0.05),
                            width: media.width * 1,
                            height: media.width * 0.4,
                            decoration: BoxDecoration(
                                color: page,
                                borderRadius: BorderRadius.only(
                                    topLeft:
                                        Radius.circular(media.width * 0.05),
                                    topRight:
                                        Radius.circular(media.width * 0.05))),
                            child: Column(
                              children: [
                                SizedBox(
                                    width: media.width * 0.7,
                                    child: MyText(
                                      text: languages[choosenLanguage]
                                          ['text_vehicle_added'],
                                      size: media.width * sixteen,
                                      fontweight: FontWeight.bold,
                                    )),
                                SizedBox(
                                  height: media.width * 0.1,
                                ),
                                Button(
                                    onTap: () {
                                      Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  const ManageVehiclesView()));
                                    },
                                    text: languages[choosenLanguage]['text_ok'])
                              ],
                            ),
                          );
                        });
                  } else if (reg == 'logout') {
                    navigateLogout();
                  } else {
                    setState(() {
                      uploadError = reg.toString();
                    });
                  }
                } else {
                  vehicletypelist.clear();
                  for (Map<String, dynamic> json in choosevehicletypelist) {
                    // Get the value of the key.
                    vehicletypelist.add(json['id']);
                  }

                  var update = await updateVehicle();
                  if (update == 'success') {
                    navigate();
                  } else if (update == 'logout') {
                    navigateLogout();
                  }
                  setState(() {
                    _isLoading = false;
                  });
                }
              },
              text: widget.frompage == 1 &&
                      userDetails.isNotEmpty &&
                      referralcontroller.text.isEmpty &&
                      isowner != true
                  ? languages[choosenLanguage]['text_skip_referral']
                  : widget.frompage != 2
                      ? languages[choosenLanguage]['text_confirm']
                      : languages[choosenLanguage]['text_updateVehicle'],
            ),
          )
        : Container();
  }

  Widget carInfoAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, true),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_car_info'],
          size: 16,
        ),
        const Spacer(),
      ],
    );
  }

  Widget carInfoBottomSheet(Size media) {
    return AnimatedPositioned(
      bottom: isbottom,
      duration: const Duration(milliseconds: 500),
      child: InkWell(
        onTap: () async {
          if (chooseWorkArea == true && serviceLocations.isNotEmpty) {
            setState(() {
              // chooseWorkArea = false;
              isbottom = -1000;
            });
          } else if (chooseServiceCategory == true &&
              serviceCategory.isNotEmpty) {
            setState(() {
              isbottom = -1000;
            });
          } else if (chooseVehicleType == true && vehicleType.isNotEmpty) {
            if (choosevehicletypelist.isEmpty) {
              setState(() {
                vehicleMake.clear();
                myVehicleId = '';
                vehicleModelId = '';
                vehicleMakeId = '';
                vehicleModel.clear();
                iscustommake = false;
                chooseVehicleMake = false;
              });
            }
            setState(() {
              chooseVehicleType = false;
              isbottom = -1000;
            });
          } else if (chooseVehicleMake == true && iscustommake == false) {
            setState(() {
              if (chooseVehicleMake == true) {
                chooseVehicleMake = false;
              } else {
                if (myVehicleId != '') {
                  chooseVehicleMake = true;
                  isbottom = 0;
                } else {
                  chooseVehicleMake = false;
                }
                chooseWorkArea = false;
                chooseVehicleModel = false;
                chooseVehicleType = false;
              }
              chooseVehicleMake = false;
              isbottom = -1000;
            });
          } else if (chooseVehicleModel == true && vehicleModel.isNotEmpty) {
            print('hereee8888');
            setState(() {
              if (chooseVehicleModel == true) {
                chooseVehicleModel = false;
              } else {
                if (vehicleMakeId != '') {
                  chooseVehicleModel = true;
                  isbottom = 0;
                } else {
                  chooseVehicleModel = false;
                }
                chooseVehicleMake = false;
                chooseWorkArea = false;
                chooseVehicleType = false;
              }
              isbottom = -1000;
            });
          }
        },
        child: Container(
          height: media.height * 1,
          width: media.width * 1,
          // color: Colors.black.withOpacity(0.2),
          alignment: Alignment.bottomCenter,
          child: (chooseWorkArea == true && serviceLocations.isNotEmpty)
              ? Container(
                  width: media.width * 1,
                  height: media.width * 0.7,
                  padding: EdgeInsets.all(media.width * 0.03),
                  decoration: BoxDecoration(
                      color: page,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(media.width * 0.04),
                          topRight: Radius.circular(media.width * 0.04)),
                      border: Border.all(color: underline)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_service_loc'],
                        size: media.width * sixteen,
                        fontweight: FontWeight.w600,
                      ),
                      SizedBox(
                        height: media.width * 0.02,
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          child: Column(
                            children: serviceLocations
                                .asMap()
                                .map((i, value) {
                                  return MapEntry(
                                      i,
                                      InkWell(
                                        onTap: () async {
                                          setState(() {
                                            tempServiceLocationId =
                                                serviceLocations[i]['id'];
                                          });
                                        },
                                        child: Row(
                                          children: [
                                            Container(
                                              width: media.width * 0.8,
                                              padding: EdgeInsets.only(
                                                  top: media.width * 0.025,
                                                  bottom: media.width * 0.025),
                                              child: Text(
                                                serviceLocations[i]['name'],
                                                style:
                                                    GoogleFonts.notoKufiArabic(
                                                        fontSize: media.width *
                                                            fourteen,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        color: textColor),
                                              ),
                                            ),
                                            (tempServiceLocationId ==
                                                    serviceLocations[i]['id'])
                                                ? Icon(
                                                    Icons.done,
                                                    color: online,
                                                  )
                                                : Container()
                                          ],
                                        ),
                                      ));
                                })
                                .values
                                .toList(),
                          ),
                        ),
                      ),
                      Button(
                          onTap: () async {
                            setState(() {
                              myVehicleId = '';
                              vehicleMakeId = '';
                              vehicleModelId = '';
                              myServiceId = tempServiceLocationId;
                              chooseWorkArea = false;
                              _isLoading = true;
                              isbottom = -1000;
                              choosevehicletypelistlocal.clear();
                              choosevehicletypelist.clear();
                            });
                            modelcontroller.text = '';
                            colorcontroller.text = '';
                            numbercontroller.text = '';
                            var result = await getvehicleType();
                            if (result == 'success') {
                              setState(() {
                                _isLoading = false;
                              });
                            } else {
                              setState(() {
                                _isLoading = false;
                              });
                            }
                          },
                          text: languages[choosenLanguage]['text_confirm'])
                    ],
                  ),
                )
              : (chooseServiceCategory == true && serviceCategory.isNotEmpty)
                  ? Container(
                      width: media.width * 1,
                      height: media.width * 0.8,

                      // height: media.width * 0.5,
                      padding: EdgeInsets.all(media.width * 0.03),
                      decoration: BoxDecoration(
                          color: page,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(media.width * 0.04),
                              topRight: Radius.circular(media.width * 0.04)),
                          border: Border.all(color: underline)),
                      child: Column(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              physics: const BouncingScrollPhysics(),
                              child: Column(
                                children: serviceCategory
                                    .asMap()
                                    .map((i, value) {
                                      return MapEntry(
                                          i,
                                          InkWell(
                                            onTap: () async {
                                              setState(() {
                                                tempServiceCategory =
                                                    serviceCategory[i];
                                              });
                                            },
                                            child: Row(
                                              children: [
                                                Container(
                                                    width: media.width * 0.8,
                                                    padding: EdgeInsets.only(
                                                        top:
                                                            media.width * 0.025,
                                                        bottom: media.width *
                                                            0.025),
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          serviceCategory[i]
                                                                  ['title']
                                                              .toString()
                                                              .toUpperCase(),
                                                          style: GoogleFonts
                                                              .notoKufiArabic(
                                                                  fontSize: media
                                                                          .width *
                                                                      fourteen,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color:
                                                                      textColor),
                                                        ),
                                                      ],
                                                    )),
                                                (tempServiceCategory ==
                                                        serviceCategory[i])
                                                    ? Icon(
                                                        Icons.done,
                                                        color: online,
                                                      )
                                                    : Container()
                                              ],
                                            ),
                                          ));
                                    })
                                    .values
                                    .toList(),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: media.width * 0.03,
                          ),
                          Button(
                            onTap: () async {
                              setState(() {
                                selectedServiceCategory = tempServiceCategory;
                                chooseServiceCategory = false;
                                isbottom = -1000;
                                print(selectedServiceCategory);
                              });
                              setState(() {});
                            },
                            text: languages[choosenLanguage]['text_confirm'],
                          )
                        ],
                      ),
                    )
                  : (chooseVehicleType == true && vehicleType.isNotEmpty)
                      ? Container(
                          width: media.width * 1,
                          height: media.width * 0.8,

                          // height: media.width * 0.5,
                          padding: EdgeInsets.all(media.width * 0.03),
                          decoration: BoxDecoration(
                              color: page,
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(media.width * 0.04),
                                  topRight:
                                      Radius.circular(media.width * 0.04)),
                              border: Border.all(color: underline)),
                          child: Column(
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  physics: const BouncingScrollPhysics(),
                                  child: Column(
                                    children: vehicleType
                                        .asMap()
                                        .map((i, value) {
                                          return MapEntry(
                                              i,
                                              InkWell(
                                                onTap: () async {
                                                  setState(() {
                                                    vehicleMakeId = '';
                                                    vehicleModelId = '';
                                                    vehicleMakeName = '';
                                                    vehicleModelName = '';
                                                    // chooseVehicleType = false;
                                                    iscustommake = false;
                                                  });
                                                  if (widget.frompage == 3) {
                                                    if (choosevehicletypelistlocal
                                                        .isEmpty) {
                                                      choosevehicletypelistlocal
                                                          .add(vehicleType[i]);
                                                    } else {
                                                      choosevehicletypelistlocal
                                                          .clear();
                                                      choosevehicletypelistlocal
                                                          .add(vehicleType[i]);
                                                    }
                                                  } else {
                                                    if (choosevehicletypelistlocal
                                                        .where((element) =>
                                                            element['name'] ==
                                                            vehicleType[i]
                                                                ['name'])
                                                        .isEmpty) {
                                                      choosevehicletypelistlocal
                                                          .add(vehicleType[i]);
                                                    } else {
                                                      choosevehicletypelistlocal
                                                          .removeWhere(
                                                              (element) =>
                                                                  element[
                                                                      'name'] ==
                                                                  vehicleType[i]
                                                                      ['name']);
                                                    }
                                                  }
                                                },
                                                child: Row(
                                                  children: [
                                                    Container(
                                                        width:
                                                            media.width * 0.8,
                                                        padding:
                                                            EdgeInsets.only(
                                                                top: media
                                                                        .width *
                                                                    0.025,
                                                                bottom: media
                                                                        .width *
                                                                    0.025),
                                                        child: Row(
                                                          children: [
                                                            Image.network(
                                                              vehicleType[i]
                                                                      ['icon']
                                                                  .toString(),
                                                              fit: BoxFit
                                                                  .contain,
                                                              width:
                                                                  media.width *
                                                                      0.1,
                                                              height:
                                                                  media.width *
                                                                      0.08,
                                                            ),
                                                            const SizedBox(
                                                              width: 10,
                                                            ),
                                                            Text(
                                                              vehicleType[i]
                                                                      ['name']
                                                                  .toString()
                                                                  .toUpperCase(),
                                                              style: GoogleFonts.notoKufiArabic(
                                                                  fontSize: media
                                                                          .width *
                                                                      fourteen,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color:
                                                                      textColor),
                                                            ),
                                                          ],
                                                        )),
                                                    Icon(
                                                      Icons.done,
                                                      color: (choosevehicletypelistlocal
                                                              .where((element) =>
                                                                  element[
                                                                      'name'] ==
                                                                  vehicleType[i]
                                                                      ['name'])
                                                              .isNotEmpty)
                                                          ? online
                                                          : Colors.grey,
                                                    ),
                                                  ],
                                                ),
                                              ));
                                        })
                                        .values
                                        .toList(),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: media.width * 0.03,
                              ),
                              Button(
                                  onTap: () async {
                                    choosevehicletypelist.clear();
                                    for (var i = 0;
                                        i < choosevehicletypelistlocal.length;
                                        i++) {
                                      choosevehicletypelist
                                          .add(choosevehicletypelistlocal[i]);
                                    }
                                    if (choosevehicletypelistlocal.isNotEmpty) {
                                      await getVehicleMake(
                                        myVehicleIconFor:
                                            choosevehicletypelistlocal[0]
                                                    ['icon_types_for']
                                                .toString(),
                                      );
                                    }

                                    isbottom = -1000;

                                    if (choosevehicletypelist.isEmpty) {
                                      setState(() {
                                        vehicleMake.clear();
                                        myVehicleId = '';
                                        vehicleModelId = '';
                                        vehicleMakeId = '';
                                        vehicleModel.clear();
                                        iscustommake = false;
                                        chooseVehicleMake = false;
                                      });
                                    }
                                    chooseVehicleType = false;
                                    if (choosevehicletypelistlocal.isNotEmpty) {
                                      myVehicleId =
                                          choosevehicletypelistlocal[0]['id'];
                                    }
                                    modelcontroller.text = '';
                                    colorcontroller.text = '';
                                    numbercontroller.text = '';
                                    setState(() {});
                                  },
                                  text: languages[choosenLanguage]
                                      ['text_confirm'])
                            ],
                          ),
                        )
                      : (chooseVehicleMake == true && iscustommake == false)
                          ? Container(
                              width: media.width * 1,
                              height: media.width * 0.8,
                              padding: EdgeInsets.all(media.width * 0.03),
                              decoration: BoxDecoration(
                                  color: page,
                                  borderRadius: BorderRadius.only(
                                      topLeft:
                                          Radius.circular(media.width * 0.04),
                                      topRight:
                                          Radius.circular(media.width * 0.04)),
                                  border: Border.all(color: underline)),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  MyText(
                                    text: languages[choosenLanguage]
                                        ['text_vehicle_make'],
                                    size: media.width * sixteen,
                                    fontweight: FontWeight.w600,
                                  ),
                                  SizedBox(
                                    height: media.width * 0.02,
                                  ),
                                  Expanded(
                                    child: SingleChildScrollView(
                                      physics: const BouncingScrollPhysics(),
                                      child: Column(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(
                                                right: media.width * 0.05),
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  iscustommake = true;
                                                  custommakecontroller.text =
                                                      '';
                                                  custommodelcontroller.text =
                                                      '';
                                                  chooseVehicleMake = false;
                                                  isbottom = -1000;
                                                  modelcontroller.text = '';
                                                  colorcontroller.text = '';
                                                  numbercontroller.text = '';
                                                });
                                              },
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: Container(
                                                        padding:
                                                            EdgeInsets.only(
                                                                top: media
                                                                        .width *
                                                                    0.025,
                                                                bottom: media
                                                                        .width *
                                                                    0.025),
                                                        child: MyText(
                                                          text: languages[
                                                                  choosenLanguage]
                                                              [
                                                              'text_custom_make'],
                                                          size: media.width *
                                                              fourteen,
                                                          fontweight:
                                                              FontWeight.w600,
                                                          color: buttonColor,
                                                        )),
                                                  ),
                                                  Icon(
                                                    Icons.edit_note_sharp,
                                                    color: buttonColor,
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                          Column(
                                            children: vehicleMake
                                                .asMap()
                                                .map((i, value) {
                                                  return MapEntry(
                                                      i,
                                                      InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            tempVehicleMakeId =
                                                                vehicleMake[i]
                                                                        ['id']
                                                                    .toString();
                                                          });
                                                        },
                                                        child: Row(
                                                          children: [
                                                            Container(
                                                                width: media
                                                                        .width *
                                                                    0.8,
                                                                padding: EdgeInsets.only(
                                                                    top: media
                                                                            .width *
                                                                        0.025,
                                                                    bottom: media
                                                                            .width *
                                                                        0.025),
                                                                child: MyText(
                                                                  text: vehicleMake[
                                                                              i]
                                                                          [
                                                                          'name']
                                                                      .toString(),
                                                                  size: media
                                                                          .width *
                                                                      fourteen,
                                                                  fontweight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color:
                                                                      textColor,
                                                                )),
                                                            Icon(
                                                              Icons.done,
                                                              color: (tempVehicleMakeId ==
                                                                      vehicleMake[i]
                                                                              [
                                                                              'id']
                                                                          .toString())
                                                                  ? online
                                                                  : Colors.grey,
                                                            ),
                                                          ],
                                                        ),
                                                      ));
                                                })
                                                .values
                                                .toList(),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Button(
                                      onTap: () async {
                                        setState(() {
                                          vehicleModelId = '';
                                          vehicleModelName = '';
                                          vehicleMakeId = tempVehicleMakeId;
                                          chooseVehicleMake = false;
                                          isbottom = -1000;
                                          _isLoading = true;
                                        });

                                        var result = await getVehicleModel();
                                        if (result == 'success') {
                                          setState(() {
                                            _isLoading = false;
                                          });
                                        } else {
                                          setState(() {
                                            _isLoading = false;
                                          });
                                        }
                                        modelcontroller.text = '';
                                        colorcontroller.text = '';
                                        numbercontroller.text = '';
                                      },
                                      text: languages[choosenLanguage]
                                          ['text_confirm'])
                                ],
                              ),
                            )
                          : (chooseVehicleModel == true &&
                                  vehicleModel.isNotEmpty)
                              ? Container(
                                  // margin: EdgeInsets.only(
                                  //     bottom: media.width * 0.03),
                                  width: media.width * 1,
                                  height: media.width * 0.7,
                                  padding: EdgeInsets.all(media.width * 0.03),
                                  decoration: BoxDecoration(
                                      color: page,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(
                                              media.width * 0.04),
                                          topRight: Radius.circular(
                                              media.width * 0.04)),
                                      border: Border.all(color: underline)),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      MyText(
                                        text: languages[choosenLanguage]
                                            ['text_vehicle_model'],
                                        size: media.width * fifteen,
                                        fontweight: FontWeight.w600,
                                      ),
                                      SizedBox(
                                        height: media.width * 0.02,
                                      ),
                                      Expanded(
                                        child: SingleChildScrollView(
                                          physics:
                                              const BouncingScrollPhysics(),
                                          child: Column(
                                            children: vehicleModel
                                                .asMap()
                                                .map((i, value) {
                                                  return MapEntry(
                                                      i,
                                                      InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            tempVehicleModelId =
                                                                vehicleModel[i]
                                                                    ['id'];
                                                          });
                                                        },
                                                        child: Row(
                                                          children: [
                                                            Container(
                                                                width: media
                                                                        .width *
                                                                    0.8,
                                                                padding: EdgeInsets.only(
                                                                    top: media
                                                                            .width *
                                                                        0.025,
                                                                    bottom: media
                                                                            .width *
                                                                        0.025),
                                                                child: MyText(
                                                                  text: vehicleModel[
                                                                              i]
                                                                          [
                                                                          'name']
                                                                      .toString(),
                                                                  size: media
                                                                          .width *
                                                                      fourteen,
                                                                  fontweight:
                                                                      FontWeight
                                                                          .w600,
                                                                )),
                                                            Icon(
                                                              Icons.done,
                                                              color: (tempVehicleModelId ==
                                                                      vehicleModel[
                                                                              i]
                                                                          [
                                                                          'id'])
                                                                  ? online
                                                                  : Colors.grey,
                                                            ),
                                                          ],
                                                        ),
                                                      ));
                                                })
                                                .values
                                                .toList(),
                                          ),
                                        ),
                                      ),
                                      Button(
                                          onTap: () async {
                                            setState(() {
                                              vehicleModelId =
                                                  tempVehicleModelId.toString();
                                              chooseVehicleModel = false;
                                              _isLoading = true;
                                              isbottom = -1000;
                                            });

                                            var result =
                                                await getVehicleModel();
                                            if (result == 'success') {
                                              setState(() {
                                                _isLoading = false;
                                              });
                                            } else {
                                              setState(() {
                                                _isLoading = false;
                                              });
                                            }
                                            modelcontroller.text = '';
                                            colorcontroller.text = '';
                                            numbercontroller.text = '';
                                          },
                                          text: languages[choosenLanguage]
                                              ['text_confirm'])
                                    ],
                                  ),
                                )
                              : Container(),
        ),
      ),
    );
  }

  Widget vehicleAddedPopup(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: media.width * 0.9,
              padding: EdgeInsets.all(media.width * 0.05),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_vehicle_added'],
                    size: media.width * sixteen,
                    fontweight: FontWeight.bold,
                  ),
                  const SizedBox(height: 32),
                  Button(
                    width: media.width * 0.2,
                    height: media.width * 0.1,
                    onTap: () => Navigator.pop(context, true),
                    text: languages[choosenLanguage]['text_ok'],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget carInfoError(Size media) {
    return Column(
      children: [
        SizedBox(
          width: media.width * 0.9,
          child: MyText(
            text: _error,
            color: Colors.red,
            size: media.width * fourteen,
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: media.width * 0.025)
      ],
    );
  }

  Widget carInfoNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}
