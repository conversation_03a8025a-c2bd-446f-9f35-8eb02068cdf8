import 'package:flutter/material.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:image_picker/image_picker.dart';

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../noInternet/nointernet.dart';
import 'fill_car_info_view.dart';
import 'required_information_view.dart';

class OwnerRegisterView extends StatefulWidget {
  const OwnerRegisterView({Key? key}) : super(key: key);

  @override
  State<OwnerRegisterView> createState() => _OwnerRegisterViewState();
}

String ownerName = ''; //name of user
String ownerEmail = ''; // email of user
String companyName = '';
String companyAddress = '';
String city = '';
String postalCode = '';
String taxNumber = '';
String ownerServiceLocation = '';

class _OwnerRegisterViewState extends State<OwnerRegisterView> {
  bool chooseWorkArea = false;

  bool _loading = true;
  // ignore: unused_field, prefer_final_fields
  bool _chooseLocation = false;
  var verifyEmailError = '';
  var error = '';
  ImagePicker picker = ImagePicker();

  TextEditingController emailText = TextEditingController();
  TextEditingController nameText = TextEditingController();
  TextEditingController companyText = TextEditingController();
  TextEditingController addressText = TextEditingController();
  TextEditingController cityText = TextEditingController();
  TextEditingController postalText = TextEditingController();
  TextEditingController taxText = TextEditingController();

  getLocations() async {
    myServiceId = '';
    var result = await getServiceLocation();
    if (result == 'success') {
      setState(() {
        _loading = false;
      });
    } else {
      setState(() {
        _loading = true;
      });
    }
  }

  @override
  void initState() {
    proImageFile1 = null;
    getLocations();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            body: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  child: Column(
                    children: [
                      ownerRegisterAppBar(media),
                      Expanded(
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 20),
                              MyText(
                                text: languages[choosenLanguage]
                                    ['text_company_info'],
                                size: media.width * fourteen,
                                fontweight: FontWeight.bold,
                              ),
                              const SizedBox(height: 16),
                              MyText(
                                text: languages[choosenLanguage]
                                    ['text_service_location'],
                                size: media.width * sixteen,
                                fontweight: FontWeight.w600,
                              ),
                              const SizedBox(height: 8),
                              serviceLocation(media),
                              if (chooseWorkArea == true &&
                                  serviceLocations.isNotEmpty)
                                serviceLocationList(media),
                              const SizedBox(height: 20),
                              companyNameTextField(media),
                              const SizedBox(height: 20),
                              addressTextField(media),
                              const SizedBox(height: 20),
                              cityTextField(media),
                              const SizedBox(height: 20),
                              postalCodeTextField(media),
                              const SizedBox(height: 20),
                              taxNumberTextField(media),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      if (error != '') ownerRegisterError(media),
                      ownerRegisterButton(media),
                    ],
                  ),
                ),
                if (internet == false) ownerRegisterNoInternet(media),
                if (_loading == true) const Positioned(child: Loading())
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget ownerRegisterAppBar(Size media) {
    return Row(
      children: [
        if (userDetails.isEmpty)
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Icon(Icons.arrow_back_ios, color: textColor),
          ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_reqinfo'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget serviceLocation(Size media) {
    return InkWell(
      onTap: () async {
        setState(() => chooseWorkArea = !chooseWorkArea);
        await getServiceLocation();
        setState(() {});
      },
      child: Container(
        height: media.width * 0.13,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: textColor),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: media.width * 0.66,
              child: MyText(
                text: (myServiceId != null && myServiceId == '')
                    ? languages[choosenLanguage]['text_service_loc'].toString()
                    : (myServiceId != null && myServiceId != '')
                        ? serviceLocations.isNotEmpty
                            ? serviceLocations
                                .firstWhere((element) =>
                                    element['id'] == myServiceId)['name']
                                .toString()
                            : ''
                        : userDetails['service_location_name'],
                size: (myServiceId != null && myServiceId != '')
                    ? media.width * sixteen
                    : media.width * fourteen,
              ),
            ),
            Container(
              height: media.width * 0.06,
              width: media.width * 0.06,
              decoration: BoxDecoration(
                color: topBar,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2.0,
                    spreadRadius: 2.0,
                    color: Colors.black.withOpacity(0.2),
                  )
                ],
              ),
              child: Icon(
                Icons.place,
                color: loaderColor,
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget serviceLocationList(Size media) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      width: media.width * 0.9,
      padding: EdgeInsets.all(media.width * 0.03),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: textColor),
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: serviceLocations
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    InkWell(
                      onTap: () async {
                        setState(() {
                          myServiceId = serviceLocations[i]['id'];
                          chooseWorkArea = false;
                        });
                        setState(() {});
                      },
                      child: Container(
                        width: media.width * 0.8,
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        child: MyText(
                          text: serviceLocations[i]['name'],
                          size: media.width * fourteen,
                          fontweight: FontWeight.w600,
                        ),
                      ),
                    ),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget companyNameTextField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: textColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: MyTextField(
        textController: companyText,
        hinttext: languages[choosenLanguage]['text_company_name'],
        onTap: (val) {
          companyName = companyText.text;
        },
      ),
    );
  }

  Widget addressTextField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: textColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: MyTextField(
        textController: addressText,
        hinttext: languages[choosenLanguage]['text_address'],
        onTap: (val) {
          companyAddress = addressText.text;
        },
      ),
    );
  }

  Widget cityTextField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: textColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: MyTextField(
        textController: cityText,
        hinttext: languages[choosenLanguage]['text_city'],
        onTap: (val) {
          city = cityText.text;
        },
      ),
    );
  }

  Widget postalCodeTextField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: textColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: MyTextField(
        textController: postalText,
        hinttext: languages[choosenLanguage]['text_postal_code'],
        inputType: TextInputType.number,
        onTap: (val) {
          postalCode = postalText.text;
        },
      ),
    );
  }

  Widget taxNumberTextField(Size media) {
    return Container(
      height: media.width * 0.13,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: textColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: MyTextField(
        textController: taxText,
        hinttext: languages[choosenLanguage]['text_tax_number'],
        onTap: (val) {
          taxNumber = taxText.text;
        },
      ),
    );
  }

  Widget ownerRegisterButton(Size media) {
    return (companyText.text.isNotEmpty &&
            addressText.text.isNotEmpty &&
            cityText.text.isNotEmpty &&
            postalText.text.isNotEmpty &&
            taxText.text.isNotEmpty)
        ? Container(
            width: media.width * 1,
            alignment: Alignment.center,
            child: Button(
              onTap: () async {
                FocusManager.instance.primaryFocus?.unfocus();
                setState(() {
                  verifyEmailError = '';
                  error = '';
                  _loading = true;
                });
                var val = await registerOwner();
                if (val == 'true') {
                  carInformationCompleted = true;
                  // ignore: use_build_context_synchronously
                  Navigator.pop(context, true);
                  serviceLocations.clear();
                } else {
                  error = val.toString();
                }
                setState(() => _loading = false);
              },
              text: languages[choosenLanguage]['text_next'],
            ),
          )
        : Container();
  }

  Widget ownerRegisterError(Size media) {
    return Container(
      margin: EdgeInsets.only(
        top: media.height * 0.03,
        bottom: media.height * 0.03,
      ),
      alignment: Alignment.center,
      width: media.width * 0.8,
      child: MyText(
        text: error,
        size: media.width * sixteen,
        color: Colors.red,
      ),
    );
  }

  Widget ownerRegisterNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}
