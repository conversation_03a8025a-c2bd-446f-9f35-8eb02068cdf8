import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/upload_documents_view.dart';
import 'package:safir_driver/pages/onTripPage/map_page.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import 'fill_car_info_view.dart';
import 'fill_user_info_view.dart';
import 'owner_register_view.dart';
import 'profile_information_view.dart';

class RequiredInformationView extends StatefulWidget {
  const RequiredInformationView({Key? key}) : super(key: key);

  @override
  State<RequiredInformationView> createState() =>
      _RequiredInformationViewState();
}

bool profileCompleted = false;
bool carInformationCompleted = false;
bool documentCompleted = false;

class _RequiredInformationViewState extends State<RequiredInformationView> {
  @override
  void initState() {
    setState(() => profileCompleted = true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: ValueListenableBuilder(
            valueListenable: valueNotifierHome.value,
            builder: (context, value, child) {
              if (userDetails['approve'] == true) {
                Future.delayed(
                  const Duration(milliseconds: 0),
                  () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (context) => const Maps()),
                      (route) => false,
                    );
                  },
                );
              }
              return Container(
                padding: const EdgeInsets.all(20),
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                child: Column(
                  children: [
                    requiredInfoAppBar(media),
                    const SizedBox(height: 20),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            requiredInformationHeader(media),
                            const SizedBox(height: 20),
                            Container(
                              width: media.width * 0.9,
                              padding: EdgeInsets.all(media.width * 0.04),
                              color: page,
                              child: Column(
                                children: [
                                  personalProfileSection(media),
                                  const SizedBox(height: 16),
                                  customDivider(),
                                  const SizedBox(height: 16),
                                  carInfoSection(media),
                                  const SizedBox(height: 16),
                                  customDivider(),
                                  const SizedBox(height: 16),
                                  docsSection(media),
                                ],
                              ),
                            ),
                            if (documentCompleted == true ||
                                userDetails['uploaded_document'] == true)
                              requiredInformationFooter(media),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget requiredInfoAppBar(Size media) {
    return Row(
      children: [
        if (userDetails.isEmpty)
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Icon(Icons.arrow_back_ios, color: textColor),
          ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_reqinfo'],
          size: 16,
        ),
        const Spacer(),
      ],
    );
  }

  Widget customDivider() {
    return Container(
      height: 1,
      color: const Color.fromARGB(255, 214, 213, 213),
    );
  }

  Widget personalProfileSection(Size media) {
    return InkWell(
      onTap: () async {
        if (profileCompleted != true && userDetails.isEmpty) {
          var nav = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ProfileInformationView()),
          );
          if (nav != null) {
            if (nav) {
              setState(() {});
            }
          }
        }
      },
      child: Row(
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  AppAssets.personIcon,
                  height: media.width * 0.05,
                  width: media.width * 0.05,
                  fit: BoxFit.contain,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_profile'],
                        size: media.width * twelve,
                        fontweight: FontWeight.bold,
                      ),
                      const SizedBox(height: 8),
                      MyText(
                        text: languages[choosenLanguage]['text_profile_para'],
                        size: media.width * fourteen,
                        color: const Color(0xff8A8A8A),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          if (profileCompleted == true || userDetails.isNotEmpty)
            Image.asset(
              AppAssets.doneIcon,
              height: media.width * 0.05,
              width: media.width * 0.05,
              fit: BoxFit.contain,
            ),
        ],
      ),
    );
  }

  Widget carInfoSection(Size media) {
    return InkWell(
      onTap: () async {
        if (profileCompleted == true) {
          if (carInformationCompleted != true && userDetails.isEmpty) {
            // ignore: prefer_typing_uninitialized_variables
            var nav;
            if (ischeckownerordriver == 'driver') {
              nav = await Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => FillCarInfoView(frompage: 1)),
              );
            } else {
              nav = await Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const OwnerRegisterView()),
              );
            }
            if (nav != null) {
              if (nav) {
                setState(() {});
              }
            }
          }
        }
      },
      child: Row(
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  AppAssets.carInformation,
                  height: media.width * 0.05,
                  width: media.width * 0.05,
                  fit: BoxFit.contain,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(
                        text: (ischeckownerordriver == 'driver')
                            ? languages[choosenLanguage]['text_car_info']
                            : 'Company Information',
                        size: media.width * twelve,
                        fontweight: FontWeight.bold,
                      ),
                      const SizedBox(height: 8),
                      MyText(
                        text: languages[choosenLanguage]['text_car_info_para'],
                        size: media.width * fourteen,
                        color: const Color(0xff8A8A8A),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          if (carInformationCompleted == true || userDetails.isNotEmpty)
            Image.asset(
              AppAssets.doneIcon,
              height: media.width * 0.05,
              width: media.width * 0.05,
              fit: BoxFit.contain,
            ),
        ],
      ),
    );
  }

  Widget docsSection(Size media) {
    return InkWell(
      onTap: () async {
        if (carInformationCompleted || userDetails.isNotEmpty) {
          var nav = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => UploadDocumentsView()),
          );
          if (nav != null) {
            if (nav) {
              setState(() {});
            }
          }
        }
      },
      child: Row(
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  AppAssets.documentIcon,
                  height: media.width * 0.05,
                  width: media.width * 0.05,
                  fit: BoxFit.contain,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_docs'],
                        size: media.width * twelve,
                        fontweight: FontWeight.bold,
                      ),
                      const SizedBox(height: 8),
                      MyText(
                        text: languages[choosenLanguage]['text_upload_pho_lic'],
                        size: media.width * fourteen,
                        color: const Color(0xff8A8A8A),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          (documentCompleted == true ||
                  (userDetails['uploaded_document'] == true &&
                      userDetails['declined_reason'] == null))
              ? Image.asset(
                  AppAssets.doneIcon,
                  height: media.width * 0.05,
                  width: media.width * 0.05,
                  fit: BoxFit.contain,
                )
              : (userDetails['declined_reason'] != null)
                  ? Image.asset(
                      AppAssets.cancelIcon,
                      height: media.width * 0.05,
                      width: media.width * 0.05,
                      fit: BoxFit.contain,
                    )
                  : Container(),
        ],
      ),
    );
  }

  Widget requiredInformationHeader(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MyText(
          text: (" ${languages[choosenLanguage]['text_welcome']}"
              " ${userDetails.isEmpty ? name.toString().split(' ')[0] : userDetails['name']}"),
          size: media.width * fourteen,
          fontweight: FontWeight.bold,
          color: textColor,
        ),
        const SizedBox(height: 20),
        MyText(
          text: languages[choosenLanguage]['text_reqinfo'],
          size: media.width * fourteen,
          fontweight: FontWeight.bold,
          color: textColor,
        ),
        const SizedBox(height: 20),
        MyText(
          text: languages[choosenLanguage]['text_become_captain'],
          size: media.width * fourteen,
          color: const Color(0xff8A8A8A),
        ),
      ],
    );
  }

  Widget requiredInformationFooter(Size media) {
    return Stack(
      children: [
        if (userDetails['declined_reason'] == null)
          Positioned(
            left: media.width * 0.1,
            right: media.width * 0.1,
            top: media.width * 0.05,
            bottom: media.width * 0.05,
            child: Container(
              height: media.width * 0.2,
              width: media.width * 0.3,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  opacity: 0.3,
                  image: AssetImage(AppAssets.wait),
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Row(
              children: [
                (userDetails['declined_reason'] == null)
                    ? Image.asset(
                        AppAssets.proposalApproval,
                        width: media.width * 0.045,
                        height: media.width * 0.045,
                      )
                    : Icon(
                        Icons.info,
                        size: media.width * 0.045,
                        color: Colors.red,
                      ),
                const SizedBox(width: 8),
                (userDetails['declined_reason'] == null)
                    ? MyText(
                        text: languages[choosenLanguage]
                            ['text_waiting_approval'],
                        size: media.width * fourteen,
                        fontweight: FontWeight.bold,
                      )
                    : MyText(
                        text: languages[choosenLanguage]
                            ['text_account_declined'],
                        size: media.width * fourteen,
                        fontweight: FontWeight.bold,
                      ),
              ],
            ),
            const SizedBox(height: 16),
            MyText(
              text: languages[choosenLanguage]['text_eva_profile'],
              size: media.width * twelve,
            ),
            const SizedBox(height: 8),
            MyText(
              text: (userDetails['declined_reason'] == null)
                  ? languages[choosenLanguage]['text_order_to']
                  : languages[choosenLanguage]['text_kindly_reup'],
              size: media.width * fourteen,
            ),
            const SizedBox(height: 12),
            MyText(
              text: (userDetails['declined_reason'] == null)
                  ? languages[choosenLanguage]['text_this_step']
                  : "${languages[choosenLanguage]['text_declined_reason']}"
                      " - "
                      "${userDetails['declined_reason']}",
              size: media.width * fourteen,
              color: verifyDeclined,
            )
          ],
        ),
      ],
    );
  }
}
