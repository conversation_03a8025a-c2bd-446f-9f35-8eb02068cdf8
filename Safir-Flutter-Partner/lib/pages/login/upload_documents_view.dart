import 'package:flutter/material.dart';

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import 'required_information_view.dart';
import 'upload_specific_document_view.dart';

// ignore: must_be_immutable
class UploadDocumentsView extends StatefulWidget {
  dynamic fleetid;
  UploadDocumentsView({Key? key, this.fleetid}) : super(key: key);

  @override
  State<UploadDocumentsView> createState() => _UploadDocumentsViewState();
}

int docsId = 0;
int choosenDocs = 0;

String docIdNumber = '';
String date = '';
DateTime expDate = DateTime.now();
// final ImagePicker _picker = ImagePicker();
dynamic imageFile;

class _UploadDocumentsViewState extends State<UploadDocumentsView> {
  bool _isLoading = true;

// dynamic imageFile;

  @override
  void initState() {
    getDocument();
    super.initState();
  }

  getDocument() async {
    // if (widget.fleetid == null) {
    await getDocumentsNeeded();
    // }
    // else {
    //   await getFleetDocumentsNeeded(widget.fleetid);
    // }
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              Container(
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    uploadDocumentAppBar(media),
                    const SizedBox(height: 20),
                    MyText(
                      text: languages[choosenLanguage]['text_docs']
                          .toString()
                          .toUpperCase(),
                      size: media.width * fourteen,
                      fontweight: FontWeight.bold,
                    ),
                    const SizedBox(height: 20),
                    if (documentsNeeded.isNotEmpty) uploadDocumentBody(media),
                    if (enableDocumentSubmit == true)
                      uploadDocumentButton(media),
                  ],
                ),
              ),
              if (_isLoading == true) const Positioned(child: Loading())
            ],
          ),
        ),
      ),
    );
  }

  Widget uploadDocumentAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            if (enableDocumentSubmit == true) {
              documentCompleted = true;
            } else {
              documentCompleted = false;
            }
            Navigator.pop(context, true);
          },
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_docs'],
          size: 16,
        ),
        const Spacer(),
      ],
    );
  }

  Widget uploadDocumentBody(Size media) {
    return Expanded(
      child: Column(
        children: documentsNeeded
            .asMap()
            .map(
              (i, value) {
                return MapEntry(
                  i,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(
                        text: documentsNeeded[i]['name'],
                        size: media.width * fourteen,
                        fontweight: FontWeight.bold,
                      ),
                      const SizedBox(height: 12),
                      InkWell(
                        onTap: () async {
                          docsId = documentsNeeded[i]['id'];
                          choosenDocs = i;
                          // docsData = (documentsNeeded[i]['driver_document'] != null) ? documentsNeeded[i]['driver_document']['data'] : {};
                          // ignore: unused_local_variable
                          var nav = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UploadSpecificDocumentView(
                                  from: (widget.fleetid == null)
                                      ? 'normal'
                                      : 'fleet'),
                            ),
                          );
                          setState(() {});
                        },
                        child: Container(
                          width: media.width * 0.9,
                          height: media.width * 0.165,
                          padding: EdgeInsets.symmetric(
                            horizontal: media.width * 0.02,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: (isDarkTheme == true)
                                  ? textColor.withOpacity(0.4)
                                  : textColor,
                              width: 1,
                            ),
                            color: (isDarkTheme == true)
                                ? Colors.black
                                : const Color(0xffF8F8F8),
                          ),
                          child: Row(
                            mainAxisAlignment:
                                (documentsNeeded[i]['is_uploaded'] == false)
                                    ? MainAxisAlignment.center
                                    : MainAxisAlignment.spaceBetween,
                            children: [
                              if (documentsNeeded[i]['is_uploaded'] == true)
                                Container(
                                  height: media.width * 0.1,
                                  width: media.width * 0.1,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(
                                        documentsNeeded[i]['driver_document']
                                                ['data']['document']
                                            .toString(),
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              if (documentsNeeded[i]['is_uploaded'] == true)
                                SizedBox(
                                  width: media.width * 0.5,
                                  child: MyText(
                                    text: documentsNeeded[i]
                                            ['document_status_string']
                                        .toString(),
                                    size: media.width * fourteen,
                                    textAlign: TextAlign.center,
                                    color: Colors.red,
                                  ),
                                ),
                              Icon(
                                (documentsNeeded[i]['is_uploaded'] == false)
                                    ? Icons.cloud_upload
                                    : Icons.done_outlined,
                                color: textColor,
                                size: media.width * 0.06,
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (documentsNeeded[i]['driver_document'] != null)
                        if (documentsNeeded[i]['driver_document']['data']
                                ['comment'] !=
                            null)
                          Container(
                            padding: EdgeInsets.only(top: media.width * 0.02),
                            width: media.width * 0.9,
                            child: MyText(
                              text: documentsNeeded[i]['driver_document']
                                      ['data']['comment']
                                  .toString(),
                              size: media.width * fourteen,
                              textAlign: TextAlign.center,
                              color: Colors.red,
                            ),
                          ),
                      SizedBox(height: media.width * 0.08),
                    ],
                  ),
                );
              },
            )
            .values
            .toList(),
      ),
    );
  }

  Widget uploadDocumentButton(Size media) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Button(
        onTap: () {
          documentCompleted = true;
          Navigator.pop(context, true);
        },
        text: languages[choosenLanguage]['text_submit'],
      ),
    );
  }
}
