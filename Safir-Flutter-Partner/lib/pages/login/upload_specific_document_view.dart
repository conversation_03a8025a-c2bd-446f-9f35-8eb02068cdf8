import 'package:flutter/material.dart';
import 'package:safir_driver/pages/login/upload_documents_view.dart';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:safir_driver/widgets/custom_close_button.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';

class UploadSpecificDocumentView extends StatefulWidget {
  final String from;
  const UploadSpecificDocumentView({Key? key, required this.from}) : super(key: key);

  @override
  State<UploadSpecificDocumentView> createState() =>
      _UploadSpecificDocumentViewState();
}

class _UploadSpecificDocumentViewState
    extends State<UploadSpecificDocumentView> {
  TextEditingController idNumber = TextEditingController();

  final ImagePicker _picker = ImagePicker();
  DateTime current = DateTime.now();
  String _permission = '';
  bool _uploadImage = false;
  bool _isLoading = false;
  String _error = '';

  //date picker
  _datePicker() async {
    DateTime? picker = await showDatePicker(
        context: context,
        initialDate: current,
        firstDate: current,
        lastDate: DateTime(2100));
    if (picker != null) {
      setState(() {
        expDate = picker;
        date = picker.toString().split(" ")[0];
      });
    }
  }

  //get gallery permission
  getGalleryPermission() async {
    dynamic status;
    if (platform == TargetPlatform.android) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        status = await Permission.storage.status;
        if (status != PermissionStatus.granted) {
          status = await Permission.storage.request();
        }

        /// use [Permissions.storage.status]
      } else {
        status = await Permission.photos.status;
        if (status != PermissionStatus.granted) {
          status = await Permission.photos.request();
        }
      }
    } else {
      status = await Permission.photos.status;
      if (status != PermissionStatus.granted) {
        status = await Permission.photos.request();
      }
    }
    return status;
  }

  //get camera permission
  getCameraPermission() async {
    var status = await Permission.camera.status;
    if (status != PermissionStatus.granted) {
      status = await Permission.camera.request();
    }
    return status;
  }

  //image pick from gallery
  imagePick() async {
    var permission = await getGalleryPermission();
    if (permission == PermissionStatus.granted) {
      final pickedFile = await _picker.pickImage(
          source: ImageSource.gallery, imageQuality: 50);
      setState(() {
        imageFile = pickedFile?.path;
        _uploadImage = false;
      });
    } else {
      setState(() {
        _permission = 'noPhotos';
      });
    }
  }

  //image pick from camera
  cameraPick() async {
    var permission = await getCameraPermission();
    if (permission == PermissionStatus.granted) {
      final pickedFile =
          await _picker.pickImage(source: ImageSource.camera, imageQuality: 50);
      setState(() {
        imageFile = pickedFile?.path;
        _uploadImage = false;
      });
    } else {
      setState(() {
        _permission = 'noCamera';
      });
    }
  }

  @override
  void initState() {
    date = '';
    imageFile = null;
    docIdNumber = '';

    super.initState();
  }

  pop() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Scaffold(
          body: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Stack(
              children: [
                Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  padding: const EdgeInsets.all(20),
                  color: page,
                  child: Column(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            documentUploadAppBar(media),
                            const SizedBox(height: 24),
                            uploadImageSection(media),
                            if (documentsNeeded[choosenDocs]
                                    ['has_identify_number'] ==
                                true)
                              documentsIdentifyNumber(media),
                            if (documentsNeeded[choosenDocs]
                                    ['has_expiry_date'] ==
                                true)
                              documentsExpiryDate(media),
                          ],
                        ),
                      ),
                      if (_error != '') documentUploadError(media),
                      documentUploadButton(media),
                    ],
                  ),
                ),
                if (_uploadImage == true) uploadImageBottomSheet(media),
                if (_permission != '') uploadImagePermissionPopup(media),
                if (_isLoading == true) const Positioned(child: Loading())
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget documentUploadAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_upload_docs'],
          size: 16,
        ),
        const Spacer(),
      ],
    );
  }

  Widget uploadImageSection(Size media) {
    return Stack(
      children: [
        Container(
          height: media.width * 0.5,
          width: media.width * 0.5,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.black, width: 1),
            // color: Colors.transparent.withOpacity(0.2),
          ),
          child: (imageFile != null)
              ? Container(
                  height: media.width * 0.5,
                  width: media.width * 0.5,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    image: DecorationImage(
                      image: FileImage(File(imageFile)),
                      fit: BoxFit.contain,
                    ),
                  ),
                )
              : Container(),
        ),
        Positioned(
          child: InkWell(
            onTap: () => setState(() => _uploadImage = true),
            child: Container(
              height: media.width * 0.5,
              width: media.width * 0.5,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white.withOpacity(0.4),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.cloud_upload,
                    size: media.width * 0.08,
                  ),
                  const SizedBox(height: 10),
                  MyText(
                    text: (imageFile == null)
                        ? languages[choosenLanguage]['text_upload_image']
                        : languages[choosenLanguage]['text_editimage'],
                    size: media.width * twelve,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget documentsIdentifyNumber(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 48),
        MyText(
          text: documentsNeeded[choosenDocs]['identify_number_locale_key']
              .toString(),
          size: media.width * sixteen,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 16),
        Container(
          height: media.width * 0.13,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: (isDarkTheme == true)
                  ? textColor.withOpacity(0.4)
                  : underline,
            ),
            color:
                (isDarkTheme == true) ? Colors.black : const Color(0xffF8F8F8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: MyTextField(
            textController: idNumber,
            hinttext: documentsNeeded[choosenDocs]['identify_number_locale_key']
                .toString(),
            onTap: (val) => setState(() => docIdNumber = val),
          ),
        ),
      ],
    );
  }

  Widget documentsExpiryDate(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 48),
        MyText(
          text: languages[choosenLanguage]['text_expiry_date'],
          size: media.width * sixteen,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 16),
        InkWell(
          onTap: () => _datePicker(),
          child: Container(
            height: media.width * 0.13,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: (isDarkTheme == true)
                    ? textColor.withOpacity(0.4)
                    : underline,
              ),
              color: (isDarkTheme == true)
                  ? Colors.black
                  : const Color(0xffF8F8F8),
            ),
            width: media.width * 0.9,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            alignment: languageDirection == 'ltr'
                ? Alignment.centerLeft
                : Alignment.centerRight,
            child: MyText(
              text: (date != '')
                  ? date
                  : languages[choosenLanguage]['text_choose_expiry'],
              size: media.width * fourteen,
            ),
          ),
        ),
      ],
    );
  }

  Widget documentUploadError(Size media) {
    return SizedBox(
      width: media.width * 0.9,
      child: MyText(
        text: _error,
        size: media.width * fourteen,
        color: Colors.red,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget documentUploadButton(Size media) {
    return Container(
      padding: const EdgeInsets.only(bottom: 20, top: 20),
      child: Button(
        onTap: () async {
          FocusManager.instance.primaryFocus?.unfocus();
          setState(() {
            _isLoading = true;
            _error = '';
          });
          if (imageFile != null) {
            if (documentsNeeded[choosenDocs]['has_identify_number'] == true &&
                docIdNumber != '' &&
                documentsNeeded[choosenDocs]['has_expiry_date'] == true &&
                date != '') {
              var result = await uploadDocs();

              if (result == 'success') {
                var result = await getDocumentsNeeded();
                if (result == 'success') {
                  pop();
                }
              } else {
                setState(() => _error = result.toString());
              }
            } else if (documentsNeeded[choosenDocs]['has_identify_number'] ==
                    true &&
                docIdNumber != '' &&
                documentsNeeded[choosenDocs]['has_expiry_date'] == false) {
              var result = await uploadDocs();

              if (result == 'success') {
                var result = await getDocumentsNeeded();
                if (result == 'success') {
                  pop();
                }
              } else {
                setState(() => _error = result.toString());
              }
            } else if (documentsNeeded[choosenDocs]['has_identify_number'] ==
                    false &&
                documentsNeeded[choosenDocs]['has_expiry_date'] == true &&
                date != '') {
              var result = await uploadDocs();

              if (result == 'success') {
                var result = await getDocumentsNeeded();
                if (result == 'success') {
                  pop();
                }
              } else {
                setState(() => _error = result.toString());
              }
            } else if (documentsNeeded[choosenDocs]['has_identify_number'] ==
                    false &&
                documentsNeeded[choosenDocs]['has_expiry_date'] == false) {
              var result = await uploadDocs();

              if (result == 'success') {
                var result = await getDocumentsNeeded();
                if (result == 'success') {
                  pop();
                }
              } else {
                setState(() => _error = result.toString());
              }
            } else {
              _error = languages[choosenLanguage]['text_fil_req_info'];
            }
          } else {
            _error = languages[choosenLanguage]['text_choose_image'];
          }
          setState(() => _isLoading = false);
        },
        text: languages[choosenLanguage]['text_submit'],
      ),
    );
  }

  Widget uploadImageBottomSheet(Size media) {
    return Positioned(
      bottom: 0,
      child: InkWell(
        onTap: () => setState(() => _uploadImage = false),
        child: Container(
          height: media.height * 1,
          width: media.width * 1,
          color: Colors.transparent.withOpacity(0.6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: EdgeInsets.all(media.width * 0.05),
                width: media.width * 1,
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    border: Border.all(
                      color: borderLines,
                      width: 1.2,
                    ),
                    color: page),
                child: Column(
                  children: [
                    Container(
                      height: 6,
                      width: media.width * 0.15,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(media.width * 0.01),
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            InkWell(
                              onTap: () => cameraPick(),
                              child: Container(
                                height: media.width * 0.171,
                                width: media.width * 0.171,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: borderLines, width: 1.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.camera_alt_outlined,
                                  size: media.width * 0.064,
                                  color: textColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            MyText(
                              text: languages[choosenLanguage]['text_camera'],
                              size: media.width * ten,
                              color: textColor,
                            )
                          ],
                        ),
                        Column(
                          children: [
                            InkWell(
                              onTap: () => imagePick(),
                              child: Container(
                                height: media.width * 0.171,
                                width: media.width * 0.171,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: borderLines, width: 1.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.image_outlined,
                                  size: media.width * 0.064,
                                  color: textColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            MyText(
                              text: languages[choosenLanguage]['text_gallery'],
                              size: media.width * ten,
                              color: textColor,
                            )
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget uploadImagePermissionPopup(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () => setState(() {
                _permission = '';
                _uploadImage = false;
              }),
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2.0,
                    spreadRadius: 2.0,
                    color: Colors.black.withOpacity(0.2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: media.width * 0.8,
                    child: MyText(
                      text: (_permission == 'noPhotos')
                          ? languages[choosenLanguage]
                              ['text_open_photos_setting']
                          : languages[choosenLanguage]
                              ['text_open_camera_setting'],
                      size: media.width * sixteen,
                      fontweight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () async => await openAppSettings(),
                        child: MyText(
                          text: languages[choosenLanguage]
                              ['text_open_settings'],
                          size: media.width * sixteen,
                          color: buttonColor,
                          fontweight: FontWeight.w600,
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          (_permission == 'noCamera')
                              ? cameraPick()
                              : imagePick();
                          setState(() => _permission = '');
                        },
                        child: MyText(
                          text: languages[choosenLanguage]['text_done'],
                          size: media.width * sixteen,
                          color: buttonColor,
                          fontweight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
