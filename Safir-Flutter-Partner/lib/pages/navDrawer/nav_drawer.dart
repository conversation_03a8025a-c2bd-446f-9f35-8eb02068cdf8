import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/NavigatorPages/settings_view.dart';
import 'package:safir_driver/pages/NavigatorPages/support_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../NavigatorPages/bank_details_view.dart';
import '../NavigatorPages/manage_drivers_view.dart';
import '../NavigatorPages/driverearnings.dart';
import '../NavigatorPages/profile_view.dart';
import '../NavigatorPages/history_view.dart';
import '../NavigatorPages/make_complaint_view.dart';
import '../NavigatorPages/manage_vehicles_view.dart';
import '../NavigatorPages/myroutebookings.dart';
import '../NavigatorPages/notification_view.dart';
import '../NavigatorPages/referral_view.dart';
import '../NavigatorPages/sos_view.dart';
import '../NavigatorPages/wallet_view.dart';
import '../login/landing_view.dart';
import '../login/login_view.dart';
import '../onTripPage/map_page.dart';

class NavDrawer extends StatefulWidget {
  const NavDrawer({Key? key}) : super(key: key);
  @override
  State<NavDrawer> createState() => _NavDrawerState();
}

class _NavDrawerState extends State<NavDrawer> {
  dynamic isCompleted;
  bool showFilter = false;
  // ignore: unused_field
  final bool _isLoading = false;
  // ignore: unused_field
  final String _error = '';
  List myHistory = [];
  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(
        const Duration(seconds: 2),
        () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false,
          );
        },
      );
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(
        const Duration(seconds: 2),
        () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false,
          );
        },
      );
    }
  }

  @override
  void initState() {
    if (userDetails['chat_id'] != null && chatStream == null) {
      streamAdminchat();
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return ValueListenableBuilder(
      valueListenable: valueNotifierHome.value,
      builder: (context, value, child) {
        return SafeArea(
          child: SizedBox(
            width: media.width * 0.8,
            child: Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Drawer(
                backgroundColor: page,
                child: Container(
                  padding: const EdgeInsets.only(bottom: 16),
                  width: media.width * 0.7,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const SizedBox(height: 16),
                              navDrawerProfile(media),
                              SizedBox(height: media.width * 0.05),
                              SizedBox(
                                width: media.width * 0.7,
                                child: Column(
                                  children: [
                                    navDrawerMyRouteBookingFeature(media),
                                    navDrawerHistory(media),
                                    navDrawerNotification(media),
                                    navDrawerWallet(media),
                                    navDrawerEarnings(media),
                                    navDrawerManageVehicle(media),
                                    navDrawerManageDriver(media),
                                    navDrawerBankDetails(media),
                                    navDrawerSOS(media),
                                    navDrawerMakeComplaints(media),
                                    navDrawerSettings(media),
                                    navDrawerSupport(media),
                                    navDrawerReferral(media),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                      navDrawerLogout(media),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget navDrawerProfile(Size media) {
    return InkWell(
      onTap: () async {
        var val = await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ProfileView()),
        );
        if (val) {
          setState(() {});
        }
      },
      child: Container(
        color: Colors.grey.withOpacity(0.3),
        width: media.width * 0.72,
        padding: EdgeInsets.fromLTRB(
          media.width * 0.03,
          media.width * 0.05,
          media.width * 0.03,
          media.width * 0.05,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              height: media.width * 0.15,
              width: media.width * 0.15,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: NetworkImage(userDetails['profile_picture']),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      MyText(
                        text: userDetails['name'],
                        size: media.width * eighteen,
                        fontweight: FontWeight.w600,
                        maxLines: 1,
                      ),
                      Icon(
                        Icons.arrow_forward_ios_outlined,
                        color: textColor,
                        size: media.width * fourteen,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: userDetails['mobile'],
                    size: media.width * fourteen,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget navDrawerMyRouteBookingFeature(Size media) {
    return (userDetails['role'] != 'owner' &&
            userDetails['enable_my_route_booking_feature'] == '1')
        ? InkWell(
            onTap: () async {
              var nav = await Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const MyRouteBooking()));
              if (nav != null) {
                if (nav) {
                  setState(() {});
                }
              }
            },
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: media.width * 0.025,
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            AppAssets.myRoute,
                            fit: BoxFit.contain,
                            width: media.width * 0.065,
                            color: const Color(0xFF858585),
                          ),
                          SizedBox(width: media.width * 0.025),
                          ShowUp(
                            delay: 150,
                            child: MyText(
                              text: languages[choosenLanguage]['text_my_route'],
                              overflow: TextOverflow.ellipsis,
                              size: media.width * sixteen,
                            ),
                          )
                        ],
                      ),
                    ),
                    const Spacer(),
                    if (userDetails['my_route_address'] != null)
                      Container(
                        height: media.width * 0.05,
                        width: media.width * 0.1,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.circular(media.width * 0.025),
                          color: (userDetails['enable_my_route_booking'] == 1)
                              ? Colors.green.withOpacity(0.4)
                              : Colors.grey.withOpacity(0.6),
                        ),
                        child: Row(
                          mainAxisAlignment:
                              (userDetails['enable_my_route_booking'] == 1)
                                  ? MainAxisAlignment.end
                                  : MainAxisAlignment.start,
                          children: [
                            Container(
                              height: media.width * 0.045,
                              width: media.width * 0.045,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color:
                                    (userDetails['enable_my_route_booking'] ==
                                            1)
                                        ? Colors.green
                                        : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    SizedBox(width: media.width * 0.05),
                  ],
                ),
                Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(
                    top: media.width * 0.01,
                    left: media.width * 0.05,
                  ),
                  child: Container(
                    color: textColor.withOpacity(0.1),
                    height: 1,
                  ),
                )
              ],
            ),
          )
        : Container();
  }

  Widget navDrawerHistory(Size media) {
    return NavMenu(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const HistoryView()),
        );
      },
      text: languages[choosenLanguage]['text_enable_history'],
      icon: Icons.view_list_outlined,
    );
  }

  Widget navDrawerNotification(Size media) {
    return (userDetails['role'] != 'owner')
        ? ValueListenableBuilder(
            valueListenable: valueNotifierNotification.value,
            builder: (context, value, child) {
              return InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const NotificationView()),
                  );
                  setState(() => userDetails['notifications_count'] = 0);
                },
                child: Container(
                  padding: EdgeInsets.only(top: media.width * 0.05),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.notifications_none,
                            size: media.width * 0.065,
                            color: const Color(0xFF858585),
                          ),
                          SizedBox(width: media.width * 0.025),
                          ShowUp(
                            delay: 150,
                            child: MyText(
                              text: languages[choosenLanguage]
                                      ['text_notification']
                                  .toString(),
                              overflow: TextOverflow.ellipsis,
                              size: media.width * sixteen,
                              color: textColor.withOpacity(0.8),
                            ),
                          ),
                          const Spacer(),
                          if (userDetails['notifications_count'] != 0)
                            Container(
                              padding: const EdgeInsets.all(6),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: buttonColor,
                              ),
                              child: Text(
                                userDetails['notifications_count'].toString(),
                                style: GoogleFonts.notoKufiArabic(
                                  fontSize: 14,
                                  color: buttonText,
                                ),
                              ),
                            ),
                          SizedBox(width: media.width * 0.05),
                        ],
                      ),
                      Container(
                        alignment: Alignment.centerRight,
                        padding: EdgeInsets.only(
                          top: media.width * 0.05,
                          left: media.width * 0.05,
                        ),
                        child: Container(
                          color: textColor.withOpacity(0.1),
                          height: 1,
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
          )
        : Container();
  }

  Widget navDrawerWallet(Size media) {
    if (userDetails['owner_id'] == null &&
        userDetails['show_wallet_feature_on_mobile_app'] == '1') {
      return NavMenu(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const WalletView()),
          );
        },
        text: languages[choosenLanguage]['text_enable_wallet'],
        icon: Icons.payment,
      );
    } else {
      return Container();
    }
  }

  Widget navDrawerEarnings(Size media) {
    return NavMenu(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const DriverEarnings()),
        );
      },
      text: languages[choosenLanguage]['text_earnings'],
      image: AppAssets.earing,
    );
  }

  Widget navDrawerManageVehicle(Size media) {
    return userDetails['role'] == 'owner'
        ? NavMenu(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ManageVehiclesView()),
              );
            },
            text: languages[choosenLanguage]['text_manage_vehicle'],
            image: AppAssets.updateVehicleInfo,
          )
        : Container();
  }

  Widget navDrawerManageDriver(Size media) {
    return userDetails['role'] == 'owner'
        ? NavMenu(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ManageDriversView()),
              );
            },
            text: languages[choosenLanguage]['text_manage_drivers'],
            image: AppAssets.manageDriver,
          )
        : Container();
  }

  Widget navDrawerBankDetails(Size media) {
    return userDetails['owner_id'] == null &&
            userDetails['show_bank_info_feature_on_mobile_app'] == "1"
        ? NavMenu(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const BankDetailsView()),
              );
            },
            text: languages[choosenLanguage]['text_updateBank'],
            icon: Icons.account_balance_outlined,
          )
        : Container();
  }

  Widget navDrawerSOS(Size media) {
    if (userDetails['role'] != 'owner') {
      return NavMenu(
        onTap: () async {
          var nav = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SosView()),
          );
          if (nav) {
            setState(() {});
          }
        },
        text: languages[choosenLanguage]['text_sos'],
        icon: Icons.connect_without_contact,
      );
    } else {
      return Container();
    }
  }

  Widget navDrawerMakeComplaints(Size media) {
    return NavMenu(
      icon: Icons.toc,
      text: languages[choosenLanguage]['text_make_complaints'],
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const MakeComplaintView()),
        );
      },
    );
  }

  Widget navDrawerSettings(Size media) {
    return NavMenu(
      onTap: () async {
        var nav = await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingsView()),
        );
        if (nav) {
          setState(() {});
        }
      },
      text: languages[choosenLanguage]['text_settings'],
      icon: Icons.settings,
    );
  }

  Widget navDrawerSupport(Size media) {
    return ValueListenableBuilder(
      valueListenable: valueNotifierChat.value,
      builder: (context, value, child) {
        return InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SupportView()),
            );
          },
          child: Container(
            padding: EdgeInsets.only(top: media.width * 0.05),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: media.width * 0.065,
                      color: const Color(0xFF858585),
                    ),
                    SizedBox(width: media.width * 0.025),
                    SizedBox(
                      child: ShowUp(
                        delay: 150,
                        child: MyText(
                          text: languages[choosenLanguage]['text_support'],
                          overflow: TextOverflow.ellipsis,
                          size: media.width * sixteen,
                          color: textColor.withOpacity(0.8),
                        ),
                      ),
                    ),
                    const Spacer(),
                    if ((unSeenChatCount != '0'))
                      Container(
                        padding: const EdgeInsets.all(6),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: buttonColor,
                        ),
                        child: Text(
                          unSeenChatCount,
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: 14,
                            color: buttonText,
                          ),
                        ),
                      ),
                    SizedBox(width: media.width * 0.05),
                  ],
                ),
                Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(
                    top: media.width * 0.05,
                    left: media.width * 0.05,
                  ),
                  child: Container(
                    color: textColor.withOpacity(0.1),
                    height: 1,
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget navDrawerReferral(Size media) {
    if (userDetails['owner_id'] == null && userDetails['role'] == 'driver') {
      return NavMenu(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ReferralView()),
          );
        },
        text: languages[choosenLanguage]['text_enable_referal'],
        icon: Icons.offline_share_outlined,
      );
    } else {
      return Container();
    }
  }

  Widget navDrawerLogout(Size media) {
    return InkWell(
      onTap: () {
        setState(() => logout = true);
        valueNotifierHome.incrementNotifier();
        Navigator.pop(context);
      },
      child: Container(
        padding: EdgeInsets.only(
          left: media.width * 0.25,
        ),
        height: media.width * 0.13,
        width: media.width * 0.8,
        color: Colors.red.withOpacity(0.2),
        child: Row(
          mainAxisAlignment: (languageDirection == 'ltr')
              ? MainAxisAlignment.start
              : MainAxisAlignment.end,
          children: [
            Icon(
              Icons.logout,
              size: media.width * 0.05,
              color: Colors.red,
            ),
            SizedBox(width: media.width * 0.025),
            MyText(
              text: languages[choosenLanguage]['text_sign_out'],
              size: media.width * sixteen,
              color: Colors.red,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            )
          ],
        ),
      ),
    );
  }
}
