import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:safir_driver/widgets/custom_close_button.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/landing_view.dart';
import 'pick_contact_view.dart';

class SosView extends StatefulWidget {
  const SosView({Key? key}) : super(key: key);

  @override
  State<SosView> createState() => _SosViewState();
}

class _SosViewState extends State<SosView> {
  bool _isDeleting = false;
  bool _isLoading = false;
  String _deleteId = '';

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LandingView()),
          (route) => false,
        );
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
          (route) => false,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      child: SafeArea(
        child: Material(
          child: ValueListenableBuilder(
              valueListenable: valueNotifierHome.value,
              builder: (context, value, child) {
                return Directionality(
                  textDirection: (languageDirection == 'rtl')
                      ? TextDirection.rtl
                      : TextDirection.ltr,
                  child: Stack(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        height: media.height * 1,
                        width: media.width * 1,
                        color: page,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            sosAppBar(media),
                            const SizedBox(height: 20),
                            MyText(
                              text: languages[choosenLanguage]
                                      ['text_add_trust_contact']
                                  .toString()
                                  .toUpperCase(),
                              size: media.width * twelve,
                              fontweight: FontWeight.w600,
                            ),
                            const SizedBox(height: 12),
                            MyText(
                              text: languages[choosenLanguage]
                                  ['text_trust_contact_4'],
                              size: media.width * twelve,
                              textAlign: TextAlign.start,
                              color: hintColor,
                            ),
                            const SizedBox(height: 20),
                            Expanded(
                              child: SingleChildScrollView(
                                physics: const BouncingScrollPhysics(),
                                child: (sosData
                                        .where((element) =>
                                            element['user_type'] != 'admin')
                                        .isNotEmpty)
                                    ? showSosData(media)
                                    : Column(
                                        children: [
                                          pickContactButton(media),
                                          if (sosData.isEmpty)
                                            sosEmptyData(media)
                                        ],
                                      ),
                              ),
                            ),
                            addSosButton(media),
                          ],
                        ),
                      ),
                      if (_isDeleting == true) deleteSosPopup(media),
                      if (_isLoading == true) const Positioned(child: Loading())
                    ],
                  ),
                );
              }),
        ),
      ),
    );
  }

  Widget sosAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_sos'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget deleteSosPopup(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () => setState(() => _isDeleting = false),
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_removeSos'],
                    size: media.width * sixteen,
                    fontweight: FontWeight.w600,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  Button(
                    onTap: () async {
                      setState(() => _isLoading = true);
                      var val = await deleteSos(_deleteId);
                      if (val == 'success') {
                        setState(() {
                          _isDeleting = false;
                        });
                      } else if (val == 'logout') {
                        navigateLogout();
                      }
                      setState(() => _isLoading = false);
                    },
                    text: languages[choosenLanguage]['text_confirm'],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget addSosButton(Size media) {
    return (sosData.where((element) => element['user_type'] != 'admin').length <
            5)
        ? Container(
            padding: const EdgeInsets.only(top: 20),
            child: Button(
              onTap: () async {
                var nav = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PickContactView(),
                  ),
                );
                if (nav) {
                  setState(() {});
                }
              },
              text: languages[choosenLanguage]['text_continue'],
            ),
          )
        : Container();
  }

  Widget showSosData(Size media) {
    return Column(
      children: sosData
          .asMap()
          .map((i, value) {
            return MapEntry(
              i,
              (sosData[i]['user_type'] != 'admin')
                  ? Container(
                      padding: EdgeInsets.all(media.width * 0.02),
                      decoration: BoxDecoration(
                        border: Border.all(color: hintColor),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            height: media.width * 0.13,
                            width: media.width * 0.13,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: hintColor.withOpacity(0.2),
                            ),
                            alignment: Alignment.center,
                            child: MyText(
                              text:
                                  sosData[i]['name'].toString().substring(0, 1),
                              size: media.width * twenty,
                              fontweight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MyText(
                                  text: sosData[i]['name'],
                                  size: media.width * sixteen,
                                  fontweight: FontWeight.w600,
                                ),
                                const SizedBox(height: 8),
                                MyText(
                                  text: sosData[i]['number'],
                                  size: media.width * twelve,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          InkWell(
                            onTap: () {
                              setState(() {
                                _deleteId = sosData[i]['id'];
                                _isDeleting = true;
                              });
                            },
                            child: Icon(Icons.delete, color: textColor),
                          ),
                        ],
                      ),
                    )
                  : Container(),
            );
          })
          .values
          .toList(),
    );
  }

  Widget pickContactButton(Size media) {
    return InkWell(
      onTap: () async {
        var nav = await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const PickContactView()),
        );
        if (nav) {
          setState(() {});
        }
      },
      child: Container(
        padding: EdgeInsets.all(media.width * 0.04),
        width: media.width * 0.9,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(media.width * 0.02),
          border: Border.all(color: hintColor),
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(color: online, shape: BoxShape.circle),
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: media.width * sixteen,
              ),
            ),
            const SizedBox(width: 12),
            MyText(
              text: languages[choosenLanguage]['text_new_connection'],
              color: textColor.withOpacity(0.7),
              size: media.width * fourteen,
            )
          ],
        ),
      ),
    );
  }

  Widget sosEmptyData(Size media) {
    return Column(
      children: [
        const SizedBox(height: 20),
        Container(
          alignment: Alignment.center,
          height: media.width * 0.6,
          width: media.width * 0.6,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                isDarkTheme ? AppAssets.sosDark : AppAssets.sosLight,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
        SizedBox(
          width: media.width * 0.9,
          child: Column(
            children: [
              MyText(
                text: languages[choosenLanguage]['text_no_contact'],
                textAlign: TextAlign.center,
                fontweight: FontWeight.w600,
                color: textColor,
                size: media.width * sixteen,
              ),
              MyText(
                text: languages[choosenLanguage]['text_add_contact_safety'],
                textAlign: TextAlign.center,
                fontweight: FontWeight.w500,
                color: textColor,
                size: media.width * fourteen,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
