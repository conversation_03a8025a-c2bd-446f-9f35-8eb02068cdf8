import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/landing_view.dart';
import '../login/login_view.dart';
import '../noInternet/nointernet.dart';
import '../onTripPage/map_page.dart';

class FaqView extends StatefulWidget {
  const FaqView({Key? key}) : super(key: key);

  @override
  State<FaqView> createState() => _FaqViewState();
}

class _FaqViewState extends State<FaqView> {
  bool _faqCompleted = false;
  dynamic _selectedQuestion;
  bool _isLoading = true;

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  @override
  void initState() {
    faqDatas();
    super.initState();
  }

  //get faq datas
  faqDatas() async {
    var val = await getFaqData(center.latitude, center.longitude);
    if (val == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      setState(() {
        _isLoading = false;
        _faqCompleted = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: ValueListenableBuilder(
          valueListenable: valueNotifierHome.value,
          builder: (context, value, child) {
            return Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Stack(
                children: [
                  Container(
                    height: media.height * 1,
                    width: media.width * 1,
                    color: page,
                    padding: EdgeInsets.fromLTRB(
                      media.width * 0.05,
                      media.width * 0.05,
                      media.width * 0.05,
                      0,
                    ),
                    child: Column(
                      children: [
                        faqAppBar(media),
                        const SizedBox(height: 20),
                        faqImage(media),
                        const SizedBox(height: 20),
                        Expanded(
                          child: SingleChildScrollView(
                            child: (faqData.isNotEmpty)
                                ? faqBody(media)
                                : (_faqCompleted == true)
                                    ? faqEmptyData(media)
                                    : Container(),
                          ),
                        )
                      ],
                    ),
                  ),
                  if (internet == false) faqNoInternet(),
                  if (_isLoading == true)
                    const Positioned(top: 0, child: Loading())
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget faqAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_faq'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget faqImage(Size media) {
    return SizedBox(
      width: media.width * 0.9,
      height: media.height * 0.16,
      child: Image.asset(
        AppAssets.faq,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget faqEmptyData(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_noDataFound'],
      size: media.width * eighteen,
      fontweight: FontWeight.w600,
    );
  }

  Widget faqBody(Size media) {
    return Column(
      children: [
        Column(
          children: faqData
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    InkWell(
                      onTap: () {
                        if (_selectedQuestion == i) {
                          setState(() => _selectedQuestion = null);
                        } else {
                          setState(() => _selectedQuestion = i);
                        }
                      },
                      child: Container(
                        width: media.width * 0.9,
                        margin: EdgeInsets.only(
                          top: media.width * 0.025,
                          bottom: media.width * 0.025,
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: page,
                          border: Border.all(color: borderLines, width: 1.2),
                        ),
                        child: Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                MyText(
                                  text: faqData[i]['question'],
                                  size: media.width * fourteen,
                                  fontweight: FontWeight.w600,
                                ),
                                RotatedBox(
                                  quarterTurns:
                                      (_selectedQuestion == i) ? 2 : 0,
                                  child: Image.asset(
                                    AppAssets.chevronDown,
                                    width: media.width * 0.075,
                                  ),
                                ),
                              ],
                            ),
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              child: (_selectedQuestion == i)
                                  ? Row(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.only(
                                            top: media.width * 0.025,
                                          ),
                                          child: MyText(
                                            text: faqData[i]['answer'],
                                            size: media.width * twelve,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Container(),
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )
              .values
              .toList(),
        ),
        if (myFaqPage['pagination'] != null)
          if (myFaqPage['pagination']['current_page'] <
              myFaqPage['pagination']['total_pages'])
            InkWell(
              onTap: () async {
                dynamic val;
                setState(() => _isLoading = true);
                val = await getFaqPages(
                  '${center.latitude}/${center.longitude}?page=${myFaqPage['pagination']['current_page'] + 1}',
                );
                if (val == 'logout') {
                  navigateLogout();
                }
                setState(() => _isLoading = false);
              },
              child: Container(
                padding: EdgeInsets.all(media.width * 0.025),
                margin: EdgeInsets.only(bottom: media.width * 0.05),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: page,
                  border: Border.all(color: borderLines, width: 1.2),
                ),
                child: Text(
                  languages[choosenLanguage]['text_loadmore'],
                  style: GoogleFonts.notoKufiArabic(
                    fontSize: media.width * sixteen,
                    color: textColor,
                  ),
                ),
              ),
            )
      ],
    );
  }

  Widget faqNoInternet() {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () {
          setState(
            () {
              internetTrue();
              _isLoading = true;
              _faqCompleted = false;
              faqDatas();
            },
          );
        },
      ),
    );
  }
}
