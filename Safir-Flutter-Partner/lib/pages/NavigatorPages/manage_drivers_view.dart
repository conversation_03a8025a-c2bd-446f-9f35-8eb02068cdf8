import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';
import 'add_driver_view.dart';

class ManageDriversView extends StatefulWidget {
  const ManageDriversView({Key? key}) : super(key: key);

  @override
  State<ManageDriversView> createState() => _ManageDriversViewState();
}

class _ManageDriversViewState extends State<ManageDriversView> {
  bool _isLoading = true;
  String isclickmenu = '';

  @override
  void initState() {
    setState(() {
      getdriverdata();
    });
    super.initState();
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const LandingView(),
            ),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const LoginView(),
            ),
            (route) => false);
      });
    }
  }

  getdriverdata() async {
    var val = await fleetDriverDetails();
    if (val == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                child: Column(
                  children: [
                    driverDetailsAppBar(media),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: fleetdriverList.isNotEmpty
                            ? driverList(media)
                            : (_isLoading == false)
                                ? noDrivers(media)
                                : Container(),
                      ),
                    ),
                    addDriverButton(media),
                  ],
                ),
              ),
              if (_isLoading == true) const Positioned(child: Loading()),
            ],
          ),
        ),
      ),
    );
  }

  Widget driverDetailsAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_manage_drivers'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget noDrivers(Size media) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: media.width * 0.3),
        Container(
          alignment: Alignment.center,
          height: media.width * 0.5,
          width: media.width * 0.5,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                (isDarkTheme)
                    ? AppAssets.noDataFoundDark
                    : AppAssets.noDataFound,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: media.width * 0.8,
          child: MyText(
            text: languages[choosenLanguage]['text_noDataFound'],
            textAlign: TextAlign.center,
            fontweight: FontWeight.w800,
            size: media.width * sixteen,
          ),
        ),
      ],
    );
  }

  Widget driverList(Size media) {
    return Column(
      children: [
        for (var i = 0; i < fleetdriverList.length; i++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: InkWell(
              onTap: () => setState(() => isclickmenu = ''),
              child: Container(
                padding: const EdgeInsets.all(4),
                width: media.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: hintColor),
                ),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          height: media.width * 0.24,
                          width: media.width * 0.24,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: NetworkImage(
                                fleetdriverList[i]['profile_picture']
                                    .toString(),
                              ),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        MyText(
                          text: fleetdriverList[i]['name'].toString(),
                          maxLines: 1,
                          size: media.width * sixteen,
                          fontweight: FontWeight.bold,
                        ),
                      ],
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      height: media.width * 0.3,
                      width: 1,
                      color: hintColor,
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MyText(
                            text: fleetdriverList[i]['mobile'].toString(),
                            size: media.width * fourteen,
                          ),
                          if (fleetdriverList[i]['car_number'].toString() ==
                              'null')
                            Container(
                              height: media.width * 0.1,
                              width: media.width * 0.2,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: const AssetImage(AppAssets.disableCar),
                                  colorFilter: ColorFilter.mode(
                                    Colors.white.withOpacity(0.2),
                                    BlendMode.dstATop,
                                  ),
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          // ignore: unnecessary_null_comparison
                          fleetdriverList[i]['car_number'].toString() == 'null'
                              ? fleetdriverList[i]['approve'] == false
                                  ? Container(
                                      alignment: Alignment.center,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4),
                                      decoration: BoxDecoration(
                                        color: buttonColor,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: MyText(
                                        text: languages[choosenLanguage]
                                            ['text_waiting_approval'],
                                        size: media.width * fourteen,
                                        color: buttonText,
                                      ),
                                    )
                                  : Container(
                                      alignment: Alignment.center,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4),
                                      decoration: BoxDecoration(
                                        color: buttonColor,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: MyText(
                                        text: languages[choosenLanguage]
                                            ['text_fleet_not_assigned'],
                                        size: media.width * fourteen,
                                        color: buttonText,
                                      ),
                                    )
                              : MyText(
                                  text: fleetdriverList[i]['car_number']
                                      .toString(),
                                  size: media.width * fourteen,
                                  fontweight: FontWeight.bold,
                                ),
                          if (fleetdriverList[i]['car_make_name'].toString() !=
                                  'null' &&
                              fleetdriverList[i]['car_model_name'] != 'null')
                            MyText(
                              text:
                                  '${fleetdriverList[i]['car_make_name']},${fleetdriverList[i]['car_model_name']}',
                              size: media.width * fourteen,
                            ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              if (fleetdriverList[i]['vehicle_type_icon'] !=
                                  null)
                                Container(
                                  height: media.width * 0.1,
                                  width: media.width * 0.2,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(
                                        fleetdriverList[i]['vehicle_type_icon']
                                            .toString(),
                                      ),
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              const Spacer(),
                              InkWell(
                                onTap: () {
                                  deleteDriverPopup(
                                    media,
                                    fleetdriverList[i]['id'],
                                  );
                                },
                                child: MyText(
                                  text: languages[choosenLanguage]
                                      ['text_delete_driver'],
                                  size: media.width * twelve,
                                  color: inputFieldSeparator,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget addDriverButton(Size media) {
    return Button(
      onTap: () async {
        var nav = await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AddDriverView()),
        );
        if (nav != null) {
          if (nav) {
            await getdriverdata();
          }
        }
      },
      text: languages[choosenLanguage]['text_add_driver'],
    );
  }

  deleteDriverPopup(media, driverId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          elevation: 0,
          backgroundColor: page,
          content: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Container(
              width: media.width * 0.8,
              color: page,
              child: MyText(
                text: languages[choosenLanguage]['text_delete_confirmation'],
                size: media.width * sixteen,
                fontweight: FontWeight.normal,
              ),
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: Button(
                    height: media.width * 0.09,
                    onTap: () => Navigator.pop(context),
                    text: languages[choosenLanguage]['text_no'],
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Button(
                    height: media.width * 0.09,
                    onTap: () async {
                      Navigator.pop(context);
                      setState(() => _isLoading = true);
                      var val = await deletefleetdriver(driverId);
                      if (val == 'logout') {
                        navigateLogout();
                      }
                      getdriverdata();
                    },
                    text: languages[choosenLanguage]['text_yes'],
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
