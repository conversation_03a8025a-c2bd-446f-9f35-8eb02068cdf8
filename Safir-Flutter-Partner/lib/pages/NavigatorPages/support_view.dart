import 'package:flutter/material.dart';
import 'package:safir_driver/pages/NavigatorPages/faq_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import 'admin_chat_view.dart';

class SupportView extends StatefulWidget {
  const SupportView({super.key});

  @override
  State<SupportView> createState() => _SupportViewState();
}

class _SupportViewState extends State<SupportView> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              Container(
                padding: EdgeInsets.all(media.width * 0.05),
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                child: Column(
                  children: [
                    supportAppBar(media),
                    const SizedBox(height: 20),
                    supportAdminChatSection(media),
                    const SizedBox(height: 10),
                    supportFAQSection(media),
                    const SizedBox(height: 10),
                    supportPrivacySection(media),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget supportAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_support'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget supportAdminChatSection(Size media) {
    return ValueListenableBuilder(
      valueListenable: valueNotifierChat.value,
      builder: (context, value, child) {
        return ShowUp(
          delay: 200,
          child: Material(
            elevation: 5,
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return const AdminChatView();
                    },
                  ),
                );
              },
              child: Container(
                color: page,
                padding: EdgeInsets.all(media.width * 0.03),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.chat,
                          size: media.width * 0.07,
                          color: textColor.withOpacity(0.5),
                        ),
                        SizedBox(width: media.width * 0.025),
                        Expanded(
                          child: MyText(
                            text: languages[choosenLanguage]['text_chat_us'],
                            overflow: TextOverflow.ellipsis,
                            size: media.width * sixteen,
                            color: textColor.withOpacity(0.8),
                          ),
                        ),
                        Row(
                          children: [
                            if (unSeenChatCount != '0')
                              Container(
                                height: 20,
                                width: 20,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: buttonColor,
                                ),
                                child: Text(
                                  unSeenChatCount,
                                  style: GoogleFonts.notoSans(
                                      fontSize: media.width * fourteen,
                                      color: buttonText),
                                ),
                              ),
                            Icon(
                              Icons.arrow_right_rounded,
                              size: media.width * 0.05,
                              color: textColor.withOpacity(0.8),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget supportFAQSection(Size media) {
    return SubMenu(
      icon: Icons.warning_amber,
      text: languages[choosenLanguage]['text_faq'],
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const FaqView(),
          ),
        );
      },
    );
  }

  Widget supportPrivacySection(Size media) {
    return SubMenu(
      onTap: () {
        openBrowser(''); // TODO: Add privacy policy link
      },
      text: languages[choosenLanguage]['text_privacy'],
      icon: Icons.privacy_tip_outlined,
    );
  }
}
