import 'package:flutter/material.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../login/landing_view.dart';

class ChangeLanguageView extends StatefulWidget {
  const ChangeLanguageView({Key? key}) : super(key: key);

  @override
  State<ChangeLanguageView> createState() => _ChangeLanguageViewState();
}

class _ChangeLanguageViewState extends State<ChangeLanguageView> {
  var _choosenLanguage = choosenLanguage;

  //navigate pop
  pop() {
    Navigator.pop(context, true);
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      child: SafeArea(
        child: Material(
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Scaffold(
              backgroundColor: page,
              body: Padding(
                padding: EdgeInsets.all(media.width * 0.05),
                child: Column(
                  children: [
                    changeLanguageAppBar(media),
                    const SizedBox(height: 20),
                    changeLanguageBody(media),
                    const SizedBox(height: 20),
                    changeLanguageButton(media),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget changeLanguageBody(Size media) {
    return Expanded(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: languages
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    InkWell(
                      onTap: () => setState(() => _choosenLanguage = i),
                      child: Container(
                        padding: EdgeInsets.all(media.width * 0.025),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            MyText(
                              text: languagesCode
                                  .firstWhere((e) => e['code'] == i)['name']
                                  .toString(),
                              size: media.width * sixteen,
                            ),
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.05,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: textColor,
                                  width: 1.2,
                                ),
                              ),
                              alignment: Alignment.center,
                              child: (_choosenLanguage == i)
                                  ? Container(
                                      height: media.width * 0.03,
                                      width: media.width * 0.03,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: textColor,
                                      ),
                                    )
                                  : Container(),
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget changeLanguageButton(Size media) {
    return Button(
      onTap: () async {
        choosenLanguage = _choosenLanguage;
        if (choosenLanguage == 'ar' ||
            choosenLanguage == 'ur' ||
            choosenLanguage == 'iw') {
          languageDirection = 'rtl';
        } else {
          languageDirection = 'ltr';
        }
        var val = await getlangid();
        if (val == 'logout') {
          navigateLogout();
        }
        pref.setString('languageDirection', languageDirection);
        pref.setString('choosenLanguage', _choosenLanguage);
        valueNotifierHome.incrementNotifier();
        pop();
      },
      text: languages[choosenLanguage]['text_confirm'],
    );
  }

  Widget changeLanguageAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, false),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_change_language'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }
}
