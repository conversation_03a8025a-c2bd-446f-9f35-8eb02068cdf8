import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../noInternet/nointernet.dart';
import 'history_view.dart';

class HistoryDetailsView extends StatefulWidget {
  const HistoryDetailsView({Key? key}) : super(key: key);

  @override
  State<HistoryDetailsView> createState() => _HistoryDetailsViewState();
}

String complaintDesc = '';
int complaintType = 0;

class _HistoryDetailsViewState extends State<HistoryDetailsView> {
  String _error = '';

  @override
  void initState() {
    makecomplaint = 0;
    makecomplaintbool = false;
    _isLoading = false;
    _tripStops = myHistory[selectedHistory]['requestStops']['data'];
    printWrapped(myHistory[selectedHistory].toString());
    getData();
    super.initState();
  }

  List _tripStops = [];

  bool _showOptions = false;

  getData() async {
    setState(() {
      complaintType = 0;
      complaintDesc = '';
      generalComplaintList = [];
    });

    await getGeneralComplaint("request");
    setState(() {
      _isLoading = false;
      if (generalComplaintList.isNotEmpty) {
        complaintType = 0;
      }
    });
  }

  int makecomplaint = 0;
  bool makecomplaintbool = false;
  bool _isLoading = false;
  TextEditingController complaintText = TextEditingController();

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        color: page,
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              SizedBox(
                height: media.height * 1,
                width: media.width * 1,
                child: Column(
                  children: [
                    historyDetailsAppBar(media),
                    Expanded(
                      child: Container(
                        width: media.width * 1,
                        color: Colors.grey.withOpacity(0.2),
                        padding: EdgeInsets.symmetric(
                          horizontal: media.width * 0.04,
                          vertical: media.width * 0.02,
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: page,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  physics: const BouncingScrollPhysics(),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      historyDetailsHeader(media),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8),
                                        child: Container(
                                          height: 1,
                                          width: media.width * 0.9,
                                          color: hintColor,
                                        ),
                                      ),
                                      driverDetails(media),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8),
                                        child: Container(
                                          height: 1,
                                          width: media.width * 0.9,
                                          color: hintColor,
                                        ),
                                      ),
                                      addressesDetails(media),
                                      const SizedBox(height: 24),
                                      historyDetailsSpaceAndTime(media),
                                      const SizedBox(height: 30),
                                      (myHistory[selectedHistory]
                                                  ['requestBill'] !=
                                              null)
                                          ? fareDetails(media)
                                          : cancelRide(media),
                                    ],
                                  ),
                                ),
                              ),
                              makeComplaintButton(media),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (makecomplaintbool == true) makeComplaintView(media),
              if (_isLoading == true) const Positioned(child: Loading()),
              if (internet == false) NoInternet(onTap: () => internetTrue()),
            ],
          ),
        ),
      ),
    );
  }

  Widget historyDetailsAppBar(Size media) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Icon(Icons.arrow_back_ios, color: textColor),
          ),
          const Spacer(),
          MyText(
            text: languages[choosenLanguage]['text_tripsummary'],
            size: 16,
            fontweight: FontWeight.bold,
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget driverDetails(Size media) {
    return Row(
      children: [
        Container(
          height: media.width * 0.13,
          width: media.width * 0.13,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: NetworkImage(
                (userDetails['role'] == 'owner')
                    ? myHistory[selectedHistory]['driverDetail']['data']
                            ['profile_picture']
                        .toString()
                    : myHistory[selectedHistory]['userDetail']['data']
                            ['profile_picture']
                        .toString(),
              ),
            ),
          ),
        ),
        const SizedBox(width: 4),
        MyText(
          text: (userDetails['role'] == 'owner')
              ? myHistory[selectedHistory]['driverDetail']['data']['name']
                  .toString()
              : myHistory[selectedHistory]['userDetail']['data']['name']
                  .toString(),
          size: media.width * sixteen,
        ),
        const Spacer(),
        MyText(
          text: (userDetails['role'] == 'owner')
              ? myHistory[selectedHistory]['ride_user_rating'] != null
                  ? myHistory[selectedHistory]['ride_user_rating'].toString()
                  : '-'
              : myHistory[selectedHistory]['ride_driver_rating'].toString(),
          size: media.width * eighteen,
          fontweight: FontWeight.w600,
          color: textColor,
        ),
        const SizedBox(width: 4),
        Icon(
          Icons.star,
          size: media.width * twenty,
          color: Colors.yellow[600],
        )
      ],
    );
  }

  Widget historyDetailsSpaceAndTime(Size media) {
    return (myHistory[selectedHistory]['drop_address'] != null)
        ? Container(
            width: media.width * 0.7,
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(color: hintColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IntrinsicHeight(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_distance'],
                        size: media.width * fourteen,
                      ),
                      const SizedBox(height: 8),
                      MyText(
                        text: myHistory[selectedHistory]['total_distance'] +
                            ' ' +
                            myHistory[selectedHistory]['unit'],
                        size: media.width * twelve,
                        fontweight: FontWeight.w700,
                      )
                    ],
                  ),
                  SizedBox(
                    width: 1,
                    child: Expanded(
                      child: Container(
                        width: 1,
                        height: double.infinity,
                        color: hintColor,
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_duration'],
                        size: media.width * fourteen,
                      ),
                      const SizedBox(height: 8),
                      MyText(
                        text:
                            '${myHistory[selectedHistory]['total_time']} mins',
                        size: media.width * twelve,
                        fontweight: FontWeight.w700,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        : Container();
  }

  Widget historyDetailsHeader(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(
              text: myHistory[selectedHistory]['converted_created_at'],
              size: media.width * fourteen,
              color: hintColor,
            ),
            const Spacer(),
            MyText(
              text: (myHistory[selectedHistory]['payment_opt'] == '1')
                  ? languages[choosenLanguage]['text_cash']
                  : (myHistory[selectedHistory]['payment_opt'] == '2')
                      ? languages[choosenLanguage]['text_wallet']
                      : (myHistory[selectedHistory]['payment_opt'] == '0')
                          ? languages[choosenLanguage]['text_card']
                          : '',
              size: media.width * fourteen,
              fontweight: FontWeight.w600,
              color: textColor,
            ),
            const SizedBox(width: 6),
            MyText(
              text: (myHistory[selectedHistory]['is_cancelled'] == 1)
                  ? (myHistory[selectedHistory]['is_completed'] == 1)
                      ? myHistory[selectedHistory]['requestBill']['data']
                              ['requested_currency_symbol'] +
                          ' ' +
                          myHistory[selectedHistory]['requestBill']['data']
                                  ['total_amount']
                              .toString()
                      : myHistory[selectedHistory]['request_eta_amount']
                          .toString()
                  : myHistory[selectedHistory]['requestBill']['data']
                          ['requested_currency_symbol'] +
                      ' ' +
                      '${myHistory[selectedHistory]['requestBill']['data']['total_amount']}',
              size: media.width * fourteen,
              fontweight: FontWeight.w600,
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(
              text: myHistory[selectedHistory]['request_number'],
              size: media.width * twelve,
              color: hintColor,
            ),
            MyText(
              text: (myHistory[selectedHistory]['is_rental'] == false)
                  ? languages[choosenLanguage]['text_regular']
                  : languages[choosenLanguage]['text_rental'],
              size: media.width * twelve,
              fontweight: FontWeight.w700,
            )
          ],
        ),
      ],
    );
  }

  Widget addressesDetails(Size media) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              height: media.width * 0.05,
              width: media.width * 0.05,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.green,
              ),
              child: Container(
                height: media.width * 0.025,
                width: media.width * 0.025,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: MyText(
                text: myHistory[selectedHistory]['pick_address'],
                size: media.width * twelve,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Column(
          children: _tripStops
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    (i < _tripStops.length - 1)
                        ? Container(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                Container(
                                  height: media.width * 0.05,
                                  width: media.width * 0.05,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.red.withOpacity(0.4),
                                  ),
                                  child: MyText(
                                    text: (i + 1).toString(),
                                    color: const Color(0xFFFF0000),
                                    fontweight: FontWeight.w600,
                                    size: media.width * twelve,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: MyText(
                                    text: _tripStops[i]['address'],
                                    size: media.width * twelve,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Container(),
                  );
                },
              )
              .values
              .toList(),
        ),
        if ((myHistory[selectedHistory]['drop_address'] != null))
          Row(
            children: [
              Container(
                height: media.width * 0.05,
                width: media.width * 0.05,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.red.withOpacity(0.4),
                ),
                child: Icon(
                  Icons.location_on,
                  size: media.width * 0.03,
                  color: const Color(0xFFFF0000),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: MyText(
                  text: myHistory[selectedHistory]['drop_address'],
                  size: media.width * twelve,
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget makeComplaintButton(Size media) {
    return (myHistory[selectedHistory]['is_cancelled'] != 1)
        ? Button(
            onTap: () {
              setState(() {
                _error = '';
                makecomplaintbool = true;
                makecomplaint = 1;
                complaintText.text = '';
              });
            },
            text: languages[choosenLanguage]['text_make_complaints'],
          )
        : Container();
  }

  Widget fareDetails(Size media) {
    return Column(
      children: [
        MyText(
          text: languages[choosenLanguage]['text_tripfare'],
          size: media.width * fourteen,
          fontweight: FontWeight.w700,
        ),
        const SizedBox(height: 16),
        if (myHistory[selectedHistory]['is_rental'] == true)
          Container(
            padding: EdgeInsets.only(bottom: media.width * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  languages[choosenLanguage]['text_ride_type'],
                  style: GoogleFonts.notoSans(
                      fontSize: media.width * fourteen, color: textColor),
                ),
                Text(
                  driverReq['rental_package_name'],
                  style: GoogleFonts.notoSans(
                      fontSize: media.width * fourteen, color: textColor),
                ),
              ],
            ),
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_baseprice'],
          myHistory[selectedHistory]['requestBill']['data']['base_price']
              .toString(),
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_distprice'],
          myHistory[selectedHistory]['requestBill']['data']['distance_price']
              .toString(),
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_timeprice'],
          myHistory[selectedHistory]['requestBill']['data']['time_price']
              .toString(),
        ),
        fareDetailsDivider(media),
        if (myHistory[selectedHistory]['requestBill']['data']
                ['cancellation_fee'] !=
            0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_cancelfee'],
                myHistory[selectedHistory]['requestBill']['data']
                        ['cancellation_fee']
                    .toString(),
              ),
              fareDetailsDivider(media),
            ],
          ),
        if (myHistory[selectedHistory]['requestBill']['data']
                ['airport_surge_fee'] !=
            0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_surge_fee'],
                myHistory[selectedHistory]['requestBill']['data']
                        ['airport_surge_fee']
                    .toString(),
              ),
              fareDetailsDivider(media),
            ],
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_waiting_price'] +
              ' (' +
              myHistory[selectedHistory]['requestBill']['data']
                  ['requested_currency_symbol'] +
              ' ' +
              myHistory[selectedHistory]['requestBill']['data']
                      ['waiting_charge_per_min']
                  .toString() +
              ' x ' +
              myHistory[selectedHistory]['requestBill']['data']
                      ['calculated_waiting_time']
                  .toString() +
              ' mins' +
              ')',
          myHistory[selectedHistory]['requestBill']['data']['waiting_charge']
              .toString(),
        ),
        fareDetailsDivider(media),
        if (myHistory[selectedHistory]['requestBill']['data']
                ['admin_commision'] !=
            0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_convfee'],
                myHistory[selectedHistory]['requestBill']['data']
                        ['admin_commision']
                    .toString(),
              ),
              fareDetailsDivider(media),
            ],
          ),
        if (myHistory[selectedHistory]['requestBill']['data']
                ['promo_discount'] !=
            null)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_discount'],
                myHistory[selectedHistory]['requestBill']['data']
                        ['promo_discount']
                    .toString(),
                true,
              ),
              fareDetailsDivider(media),
            ],
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_taxes'],
          myHistory[selectedHistory]['requestBill']['data']['service_tax']
              .toString(),
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_totalfare'],
          myHistory[selectedHistory]['requestBill']['data']['total_amount']
              .toString(),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget fareDetailsItem(Size media, String text, String amount,
      [bool isRed = false]) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        MyText(
          text: text,
          size: media.width * twelve,
          color: isRed ? Colors.red : null,
        ),
        MyText(
          text: myHistory[selectedHistory]['requestBill']['data']
                  ['requested_currency_symbol'] +
              ' ' +
              amount,
          size: media.width * twelve,
          color: isRed ? Colors.red : null,
        ),
      ],
    );
  }

  Widget fareDetailsDivider(Size media) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: media.width * 0.03),
      height: 1.5,
      color: const Color(0xffE0E0E0),
    );
  }

  Widget cancelRide(Size media) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        MyText(
          text: languages[choosenLanguage]['text_cancelled'],
          size: media.width * eighteen,
          color: verifyDeclined,
        )
      ],
    );
  }

  Widget makeComplaintView(Size media) {
    return Positioned(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        height: media.height * 1,
        width: media.width * 1,
        padding: const EdgeInsets.all(20),
        color: page,
        child: Column(
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () => setState(() {
                    makecomplaintbool = false;
                    makecomplaint = 1;
                  }),
                  child: (makecomplaint == 1)
                      ? MyText(
                          text: 'Cancel',
                          size: media.width * fourteen,
                          color: const Color(0xffFF0000),
                        )
                      : SizedBox(
                          height: media.width * 0.06,
                          width: media.width * 0.06,
                          child: const Icon(Icons.close),
                        ),
                ),
                const Spacer(),
                MyText(
                  text: languages[choosenLanguage]['text_make_complaints'],
                  size: 16,
                  fontweight: FontWeight.bold,
                ),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 30),
            if (makecomplaint == 1)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_why_report'],
                    size: media.width * sixteen,
                    fontweight: FontWeight.w700,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: languages[choosenLanguage]['text_we_appriciate'],
                    size: media.width * fourteen,
                    color: hintColor,
                  ),
                  const SizedBox(height: 20),
                  InkWell(
                    onTap: () {
                      setState(() {
                        if (_showOptions == false) {
                          _showOptions = true;
                        } else {
                          _showOptions = false;
                        }
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: media.width * 0.05, right: media.width * 0.05),
                      height: media.width * 0.12,
                      width: media.width * 0.9,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: borderLines, width: 1.2)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          MyText(
                            text: generalComplaintList[complaintType]['title'],
                            size: media.width * fourteen,
                          ),
                          RotatedBox(
                            quarterTurns: (_showOptions == true) ? 2 : 0,
                            child: Container(
                              height: media.width * 0.07,
                              width: media.width * 0.07,
                              decoration: const BoxDecoration(
                                  image: DecorationImage(
                                      image: AssetImage(
                                          'assets/images/chevron-down.png'),
                                      fit: BoxFit.contain)),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: media.width * 0.05,
                  ),
                  if (_showOptions == true)
                    Container(
                      padding: EdgeInsets.all(media.width * 0.02),
                      margin: EdgeInsets.only(bottom: media.width * 0.05),
                      height: media.width * 0.3,
                      width: media.width * 0.9,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(width: 1.2, color: borderLines),
                        color: page,
                      ),
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          children: generalComplaintList
                              .asMap()
                              .map((i, value) {
                                return MapEntry(
                                    i,
                                    InkWell(
                                      onTap: () {
                                        setState(() {
                                          complaintType = i;
                                          _showOptions = false;
                                        });
                                      },
                                      child: Container(
                                        width: media.width * 0.7,
                                        padding: EdgeInsets.only(
                                            top: media.width * 0.025,
                                            bottom: media.width * 0.025),
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1.1,
                                                    color: (i ==
                                                            generalComplaintList
                                                                    .length -
                                                                1)
                                                        ? Colors.transparent
                                                        : borderLines))),
                                        child: MyText(
                                          text: generalComplaintList[i]
                                              ['title'],
                                          size: media.width * fourteen,
                                        ),
                                      ),
                                    ));
                              })
                              .values
                              .toList(),
                        ),
                      ),
                    ),
                  Container(
                    padding: EdgeInsets.all(media.width * 0.025),
                    width: media.width * 0.9,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: (_error == '') ? borderLines : Colors.red,
                            width: 1.2)),
                    child: MyTextField(
                      textController: complaintText,
                      hinttext: languages[choosenLanguage]['text_complaint_2'] +
                          ' (' +
                          languages[choosenLanguage]['text_complaint_3'] +
                          ')',
                      maxline: 5,
                      onTap: (val) {
                        if (val.length >= 10 && _error != '') {
                          setState(() {
                            _error = '';
                          });
                        }
                      },
                    ),
                  ),
                  if (_error != '')
                    Container(
                      width: media.width * 0.9,
                      padding: EdgeInsets.symmetric(
                        vertical: media.width * 0.025,
                      ),
                      child: MyText(
                        text: _error,
                        size: media.width * fourteen,
                        color: Colors.red,
                      ),
                    ),
                ],
              ),
            if (makecomplaint == 2)
              Column(
                children: [
                  SizedBox(
                    height: media.width * 0.3,
                  ),
                  Container(
                    alignment: Alignment.center,
                    height: media.width * 0.13,
                    width: media.width * 0.13,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: const Color(0xffFF0000),
                      gradient: LinearGradient(
                          colors: <Color>[
                            const Color(0xffFF0000),
                            Colors.black.withOpacity(0.2),
                          ],
                          begin: FractionalOffset.topCenter,
                          end: FractionalOffset.bottomCenter),
                    ),
                    child: Icon(
                      Icons.done,
                      size: media.width * 0.09,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(
                    height: media.width * 0.03,
                  ),
                  MyText(
                    text: languages[choosenLanguage]['text_thanks_let'],
                    size: media.width * sixteen,
                    fontweight: FontWeight.w700,
                  ),
                  SizedBox(
                    height: media.width * 0.03,
                  ),
                  MyText(
                    text: languages[choosenLanguage]['text_thanks_feedback'],
                    size: media.width * fourteen,
                    color: textColor.withOpacity(0.4),
                  )
                ],
              ),
            const Spacer(),
            Button(
              onTap: () async {
                if (makecomplaint == 1) {
                  if (complaintText.text.length >= 10) {
                    setState(() => _isLoading = true);
                    complaintDesc = complaintText.text;
                    dynamic result;
                    result = await makeRequestComplaint();
                    if (result == 'success') {
                      setState(() {
                        makecomplaint = 2;
                        _isLoading = false;
                      });
                    }
                  } else {
                    setState(() => _error = languages[choosenLanguage]
                        ['text_complaint_text_error']);
                  }
                } else {
                  setState(() {
                    makecomplaintbool = false;
                    makecomplaint = 1;
                  });
                }
              },
              text: languages[choosenLanguage]['text_continue'],
            ),
          ],
        ),
      ),
    );
  }
}
