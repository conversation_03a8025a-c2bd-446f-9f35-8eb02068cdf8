import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../login/login_view.dart';
import '../noInternet/nointernet.dart';
import 'history_details_view.dart';

class HistoryView extends StatefulWidget {
  const HistoryView({Key? key}) : super(key: key);

  @override
  State<HistoryView> createState() => _HistoryViewState();
}

dynamic selectedHistory;

class _HistoryViewState extends State<HistoryView> {
  int _showHistory = 1;
  bool _isLoading = true;
  dynamic isCompleted;
  bool showFilter = false;

  @override
  void initState() {
    _isLoading = true;
    _getHistory();
    historyFiltter = 'is_completed=1';
    shimmer = AnimationController.unbounded(vsync: MyTickerProvider())
      ..repeat(min: -0.5, max: 1.5, period: const Duration(milliseconds: 1000));
    super.initState();
  }

  navigateLogout() {
    Future.delayed(
      const Duration(seconds: 2),
      () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
          (route) => false,
        );
      },
    );
  }

  //get history
  _getHistory() async {
    if (mounted) {
      setState(() {
        myHistoryPage.clear();
        myHistory.clear();
      });
    }
    var val = await getHistory('');
    if (val == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              Column(
                children: [
                  historyAppBar(media),
                  Expanded(
                    child: Container(
                      width: media.width * 1,
                      color: page.withOpacity(0.9),
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          children: [
                            const SizedBox(height: 10),
                            (myHistory.isNotEmpty)
                                ? historyData(media)
                                : (_isLoading)
                                    ? historyLoadingShimmer(media)
                                    : historyEmptyData(media),
                            //load more button
                            if (myHistoryPage['pagination'] != null)
                              if (myHistoryPage['pagination']['current_page'] <
                                  myHistoryPage['pagination']['total_pages'])
                                historyLoadMoreButton(media),
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
              if (showFilter) historyShowFilter(media),
              if (internet == false) historyNoInternet()
            ],
          ),
        ),
      ),
    );
  }

  Widget historyData(Size media) {
    return Column(
      children: myHistory
          .asMap()
          .map(
            (i, value) {
              return MapEntry(
                i,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () {
                        selectedHistory = i;
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HistoryDetailsView(),
                          ),
                        );
                      },
                      child: Container(
                        width: media.width * 1,
                        padding: EdgeInsets.fromLTRB(
                          media.width * 0.025,
                          media.width * 0.02,
                          media.width * 0.025,
                          media.width * 0.05,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: page,
                        ),
                        margin: EdgeInsets.only(
                          bottom: media.width * 0.02,
                          left: media.width * 0.03,
                          right: media.width * 0.03,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                MyText(
                                  text: myHistory[i]['vehicle_type_name']
                                      .toString(),
                                  fontweight: FontWeight.w600,
                                  size: media.width * fourteen,
                                ),
                                MyText(
                                  text: myHistory[i]['converted_created_at']
                                      .toString(),
                                  color: hintColor,
                                  fontweight: FontWeight.bold,
                                  size: media.width * twelve,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                MyText(
                                  text: (myHistory[i]['is_completed'] == 1)
                                      ? languages[choosenLanguage]
                                          ['text_completed']
                                      : (myHistory[i]['is_cancelled'] == 1)
                                          ? languages[choosenLanguage]
                                              ['text_cancelled']
                                          : (myHistory[i]['is_out_station'] ==
                                                  1)
                                              ? languages[choosenLanguage]
                                                  ['text_outstation_ride']
                                              : languages[choosenLanguage]
                                                  ['text_upcoming'],
                                  fontweight: FontWeight.w600,
                                  color: (myHistory[i]['is_completed'] == 1)
                                      ? online
                                      : (myHistory[i]['is_cancelled'] == 1)
                                          ? verifyDeclined
                                          : (myHistory[i]['is_out_station'] ==
                                                  1)
                                              ? Colors.orange
                                              : Colors.yellow,
                                  size: media.width * fourteen,
                                ),
                                Row(
                                  children: [
                                    MyText(
                                      text: (myHistory[i]['payment_opt'] == '1')
                                          ? languages[choosenLanguage]
                                              ['text_cash']
                                          : (myHistory[i]['payment_opt'] == '2')
                                              ? languages[choosenLanguage]
                                                  ['text_wallet']
                                              : (myHistory[i]['payment_opt'] ==
                                                      '0')
                                                  ? languages[choosenLanguage]
                                                      ['text_card']
                                                  : '',
                                      size: media.width * fourteen,
                                      color: textColor,
                                      fontweight: FontWeight.bold,
                                    ),
                                    const SizedBox(width: 8),
                                    MyText(
                                      text: (myHistory[i]['is_completed'] == 1)
                                          ? myHistory[i]['requestBill']['data'][
                                                  'requested_currency_symbol'] +
                                              ' ' +
                                              myHistory[i]['requestBill']
                                                      ['data']['total_amount']
                                                  .toString()
                                          : myHistory[i][
                                                  'requested_currency_symbol'] +
                                              ' ' +
                                              myHistory[i]['request_eta_amount']
                                                  .toString(),
                                      fontweight: FontWeight.bold,
                                      size: media.width * fourteen,
                                    ),
                                  ],
                                )
                              ],
                            ),
                            const SizedBox(height: 8),
                            const MySeparator(),
                            SizedBox(height: media.width * 0.02),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: media.width * 0.05,
                                  width: media.width * 0.05,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.green.withOpacity(0.4),
                                  ),
                                  child: Container(
                                    height: media.width * 0.025,
                                    width: media.width * 0.025,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.green,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: MyText(
                                    text: myHistory[i]['pick_address'],
                                    maxLines: 1,
                                    size: media.width * twelve,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: media.width * 0.03),
                            if (myHistory[i]['drop_address'] != null)
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    height: media.width * 0.06,
                                    width: media.width * 0.06,
                                    alignment: Alignment.center,
                                    child: Icon(
                                      Icons.location_on,
                                      color: const Color(0xFFFF0000),
                                      size: media.width * eighteen,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: MyText(
                                      text: myHistory[i]['drop_address'],
                                      maxLines: 1,
                                      size: media.width * twelve,
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
          .values
          .toList(),
    );
  }

  Widget historyLoadingShimmer(Size media) {
    return SingleChildScrollView(
      child: Column(
        children: [
          for (var i = 0; i < 10; i++)
            AnimatedBuilder(
              animation: shimmer,
              builder: (context, widget) {
                return ShaderMask(
                  blendMode: BlendMode.srcATop,
                  shaderCallback: (bounds) {
                    return LinearGradient(
                      colors: shaderColor,
                      stops: shaderStops,
                      begin: shaderBegin,
                      end: shaderEnd,
                      tileMode: TileMode.clamp,
                      transform:
                          SlidingGradientTransform(slidePercent: shimmer.value),
                    ).createShader(bounds);
                  },
                  child: Container(
                    padding: EdgeInsets.fromLTRB(
                      media.width * 0.025,
                      media.width * 0.02,
                      media.width * 0.025,
                      media.width * 0.05,
                    ),
                    margin: EdgeInsets.only(
                      bottom: media.width * 0.02,
                      left: media.width * 0.03,
                      right: media.width * 0.03,
                    ),
                    decoration: BoxDecoration(
                      color: page,
                      borderRadius: BorderRadius.circular(media.width * 0.02),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.15,
                              color: hintColor.withOpacity(0.5),
                            ),
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.15,
                              color: hintColor.withOpacity(0.5),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.2,
                              color: hintColor.withOpacity(0.5),
                            ),
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.2,
                              color: hintColor.withOpacity(0.5),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const MySeparator(),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.05,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: hintColor.withOpacity(0.5)),
                            ),
                            const SizedBox(width: 20),
                            Expanded(
                              child: Container(
                                height: media.width * 0.05,
                                color: hintColor.withOpacity(0.5),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              height: media.width * 0.05,
                              width: media.width * 0.05,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: hintColor.withOpacity(0.5)),
                            ),
                            const SizedBox(width: 20),
                            Expanded(
                              child: Container(
                                height: media.width * 0.05,
                                color: hintColor.withOpacity(0.5),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            )
        ],
      ),
    );
  }

  Widget historyLoadMoreButton(Size media) {
    return InkWell(
      onTap: () async {
        setState(() => _isLoading = true);
        dynamic val;
        if (historyFiltter == '') {
          val = await getHistoryPages(
              'page=${myHistoryPage['pagination']['current_page'] + 1}');
        } else {
          if (_showHistory == 1) {
            val = await getHistoryPages(
                'is_completed=1&page=${myHistoryPage['pagination']['current_page'] + 1}');
          } else if (_showHistory == 2) {
            val = await getHistoryPages(
                'is_cancelled=1&page=${myHistoryPage['pagination']['current_page'] + 1}');
          }
        }

        if (val == 'logout') {
          navigateLogout();
        }
        setState(() => _isLoading = false);
      },
      child: Container(
        padding: EdgeInsets.all(media.width * 0.025),
        margin: EdgeInsets.only(bottom: media.width * 0.05),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: page,
          border: Border.all(color: borderLines, width: 1.2),
        ),
        child: MyText(
          text: languages[choosenLanguage]['text_loadmore'],
          size: media.width * sixteen,
        ),
      ),
    );
  }

  Widget historyAppBar(Size media) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        media.width * 0.05,
        media.width * 0.03,
        media.width * 0.05,
        media.width * 0.03,
      ),
      color: page,
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Icon(Icons.arrow_back_ios, color: textColor),
          ),
          Expanded(
            child: MyText(
              textAlign: TextAlign.center,
              text: (_showHistory == 1)
                  ? languages[choosenLanguage]['text_completed_rides']
                  : (_showHistory == 2)
                      ? languages[choosenLanguage]['text_cancelled_rides']
                      : languages[choosenLanguage]['text_enable_history'],
              size: 16,
              maxLines: 1,
              fontweight: FontWeight.w600,
            ),
          ),
          InkWell(
            onTap: () => setState(() => showFilter = !showFilter),
            child: SizedBox(
              height: media.width * 0.1,
              width: media.width * 0.1,
              child: Image.asset(
                AppAssets.tune,
                color: textColor,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget historyShowFilter(Size media) {
    return Positioned(
      right: (languageDirection == 'ltr') ? media.width * 0.05 : null,
      left: (languageDirection == 'rtl') ? media.width * 0.05 : null,
      top: MediaQuery.of(context).padding.top + media.width * 0.1,
      child: Material(
        color: Colors.transparent,
        elevation: 10,
        child: Container(
          height: media.width * 0.32,
          width: media.width * 0.35,
          padding: EdgeInsets.all(media.width * 0.03),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: page,
            border: Border.all(color: borderLines, width: 0.7),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              InkWell(
                onTap: () async {
                  setState(() {
                    myHistory.clear();
                    myHistoryPage.clear();
                    _isLoading = true;
                    showFilter = false;
                    _showHistory = 1;
                  });
                  historyFiltter = 'is_completed=1';
                  await getHistory('');
                  setState(() => _isLoading = false);
                },
                child: Container(
                  width: media.width * 0.32,
                  padding: EdgeInsets.fromLTRB(
                    media.width * 0.01,
                    media.width * 0.01,
                    media.width * 0.01,
                    media.width * 0.02,
                  ),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: hintColor)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_completed'],
                        maxLines: 1,
                        size: media.width * fourteen,
                      ),
                      Container(
                        height: media.width * 0.04,
                        width: media.width * 0.04,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: textColor),
                        ),
                        alignment: Alignment.center,
                        child: Container(
                          height: media.width * 0.025,
                          width: media.width * 0.025,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: (_showHistory == 1) ? textColor : page,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 2),
              InkWell(
                onTap: () async {
                  setState(() {
                    myHistory.clear();
                    myHistoryPage.clear();
                    _showHistory = 2;
                    _isLoading = true;
                    showFilter = false;
                  });
                  historyFiltter = 'is_cancelled=1';
                  await getHistory('');
                  setState(() => _isLoading = false);
                },
                child: Container(
                  width: media.width * 0.32,
                  padding: EdgeInsets.fromLTRB(
                    media.width * 0.01,
                    media.width * 0.01,
                    media.width * 0.01,
                    media.width * 0.02,
                  ),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: hintColor)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      MyText(
                        text: languages[choosenLanguage]['text_cancelled'],
                        maxLines: 1,
                        size: media.width * fourteen,
                      ),
                      Container(
                        height: media.width * 0.04,
                        width: media.width * 0.04,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: textColor),
                        ),
                        alignment: Alignment.center,
                        child: Container(
                          height: media.width * 0.025,
                          width: media.width * 0.025,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: (_showHistory == 2) ? textColor : page),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget historyEmptyData(Size media) {
    return SizedBox(
      height: media.height * 0.6,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            height: media.width * 0.6,
            width: media.width * 0.6,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  (isDarkTheme)
                      ? AppAssets.noDataFoundDark
                      : AppAssets.noDataFound,
                ),
                fit: BoxFit.contain,
              ),
            ),
          ),
          MyText(
            text: languages[choosenLanguage]['text_noDataFound'],
            textAlign: TextAlign.center,
            fontweight: FontWeight.w800,
            size: media.width * sixteen,
          ),
        ],
      ),
    );
  }

  Widget historyNoInternet() {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}
