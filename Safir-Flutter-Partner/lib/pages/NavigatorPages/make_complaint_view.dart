import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/translation/translation.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../noInternet/noInternet.dart';

class MakeComplaintView extends StatefulWidget {
  const MakeComplaintView({Key? key}) : super(key: key);

  @override
  State<MakeComplaintView> createState() => _MakeComplaintViewState();
}

int complaintType = 0;

class _MakeComplaintViewState extends State<MakeComplaintView> {
  bool _isLoading = true;
  bool _showOptions = false;
  TextEditingController complaintText = TextEditingController();
  bool _success = false;
  String complaintDesc = '';
  String _error = '';

  @override
  void initState() {
    getData();
    super.initState();
  }

  getData() async {
    setState(() {
      complaintType = 0;
      complaintDesc = '';
      generalComplaintList = [];
    });

    await getGeneralComplaint("general");

    setState(() {
      _isLoading = false;
      if (generalComplaintList.isNotEmpty) {
        complaintType = 0;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      child: SafeArea(
        child: Material(
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Stack(
              children: [
                Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      makeComplaintAppBar(media),
                      const SizedBox(height: 20),
                      (generalComplaintList.isNotEmpty)
                          ? Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  makeComplaintType(media),
                                  if (_showOptions == true)
                                    complaintOptions(media),
                                  const SizedBox(height: 20),
                                  makeComplaintTextField(media),
                                  if (_error != '') makeComplaintError(media)
                                ],
                              ),
                            )
                          : Expanded(child: noComplaints(media)),
                      if (generalComplaintList.isNotEmpty)
                        makeComplaintButton(media),
                    ],
                  ),
                ),
                (_success == true)
                    ? Positioned(
                        child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        height: media.height * 1,
                        width: media.width * 1,
                        color: Colors.transparent.withOpacity(0.6),
                        child: Column(
                          children: [
                            SizedBox(
                              height: media.height * 0.1,
                            ),
                            Container(
                              padding: EdgeInsets.all(media.width * 0.03),
                              height: media.width * 0.12,
                              width: media.width * 1,
                              color: topBar,
                              child: Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      Navigator.pop(context, true);
                                    },
                                    child: MyText(
                                      text: languages[choosenLanguage]
                                          ['text_cancel'],
                                      size: media.width * fourteen,
                                      color: const Color(0xffFF0000),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Container(
                                  width: media.width * 1,
                                  padding: EdgeInsets.all(media.width * 0.04),
                                  color: page,
                                  child: Column(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          children: [
                                            SizedBox(
                                              height: media.width * 0.3,
                                            ),
                                            Container(
                                              alignment: Alignment.center,
                                              height: media.width * 0.13,
                                              width: media.width * 0.13,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: const Color(0xffFF0000),
                                                gradient: LinearGradient(
                                                    colors: <Color>[
                                                      const Color(0xffFF0000),
                                                      Colors.black
                                                          .withOpacity(0.2),
                                                    ],
                                                    begin: FractionalOffset
                                                        .topCenter,
                                                    end: FractionalOffset
                                                        .bottomCenter),
                                              ),
                                              child: Icon(
                                                Icons.done,
                                                size: media.width * 0.09,
                                                color: Colors.white,
                                              ),
                                            ),
                                            SizedBox(
                                              height: media.width * 0.03,
                                            ),
                                            MyText(
                                              text: languages[choosenLanguage]
                                                  ['text_thanks_let'],
                                              size: media.width * sixteen,
                                              fontweight: FontWeight.w700,
                                            ),
                                            SizedBox(
                                              height: media.width * 0.03,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Button(
                                          // color: textColor,
                                          textcolor: page,
                                          onTap: () async {
                                            Navigator.pop(context, true);
                                          },
                                          text: languages[choosenLanguage]
                                              ['text_continue'])
                                    ],
                                  )),
                            )
                          ],
                        ),
                      ))
                    : Container(),
                if (_isLoading == true) const Positioned(child: Loading()),
                if (internet == false) makeComplaintNoInternet(media)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget makeComplaintAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, false),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_make_complaints'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget noComplaints(Size media) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: media.width * 0.3),
        Container(
          alignment: Alignment.center,
          height: media.width * 0.5,
          width: media.width * 0.5,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                (isDarkTheme)
                    ? AppAssets.noDataFoundDark
                    : AppAssets.noDataFound,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: media.width * 0.8,
          child: MyText(
            text: languages[choosenLanguage]['text_noDataFound'],
            textAlign: TextAlign.center,
            fontweight: FontWeight.w800,
            size: media.width * sixteen,
          ),
        ),
      ],
    );
  }

  Widget makeComplaintTextField(Size media) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: (_error == '') ? borderLines : Colors.red,
          width: 1.2,
        ),
      ),
      child: TextField(
        controller: complaintText,
        minLines: 5,
        maxLines: 5,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 8 ,vertical: 12),
          border: InputBorder.none,
          hintStyle: GoogleFonts.notoKufiArabic(
            color: textColor.withOpacity(0.4),
            fontSize: media.width * fourteen,
          ),
          hintText: languages[choosenLanguage]['text_complaint_2'] +
              ' (' +
              languages[choosenLanguage]['text_complaint_3'] +
              ')',
        ),
        onChanged: (val) {
          complaintDesc = val;
          if (val.length >= 10 && _error != '') {
            setState(() => _error = '');
          }
        },
        style: GoogleFonts.notoKufiArabic(color: textColor),
      ),
    );
  }

  Widget makeComplaintButton(Size media) {
    return Button(
      onTap: () async {
        if (complaintText.text.length >= 10) {
          setState(() => _isLoading = true);
          dynamic result;
          result = await makeGeneralComplaint(complaintDesc);
          setState(() {
            if (result == 'success') {
              _success = true;
            }
            _isLoading = false;
          });
        } else {
          setState(() {
            _error = languages[choosenLanguage]['text_complaint_text_error'];
          });
        }
      },
      text: languages[choosenLanguage]['text_submit'],
    );
  }

  Widget makeComplaintType(Size media) {
    return InkWell(
      onTap: () {
        setState(() => _showOptions = !_showOptions);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        height: media.width * 0.12,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: borderLines, width: 1.2),
        ),
        child: Row(
          children: [
            Expanded(
              child: MyText(
                text: generalComplaintList[complaintType]['title'],
                maxLines: 1,
                size: media.width * twelve,
              ),
            ),
            RotatedBox(
              quarterTurns: (_showOptions == true) ? 2 : 0,
              child: Container(
                height: media.width * 0.08,
                width: media.width * 0.08,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(AppAssets.chevronDown),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget complaintOptions(Size media) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Container(
        height: media.width * 0.4,
        width: media.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(width: 1.2, color: borderLines),
        ),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: generalComplaintList
                .asMap()
                .map(
                  (i, value) {
                    return MapEntry(
                      i,
                      InkWell(
                        onTap: () {
                          setState(() {
                            complaintType = i;
                            _showOptions = false;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                width: 1.1,
                                color: (i == generalComplaintList.length - 1)
                                    ? Colors.transparent
                                    : borderLines,
                              ),
                            ),
                          ),
                          child: MyText(
                              text: generalComplaintList[i]['title'],
                              size: media.width * twelve),
                        ),
                      ),
                    );
                  },
                )
                .values
                .toList(),
          ),
        ),
      ),
    );
  }

  Widget makeComplaintError(Size media) {
    return Container(
      padding: EdgeInsets.only(
          top: media.width * 0.025, bottom: media.width * 0.025),
      child: MyText(
        text: _error,
        size: media.width * fourteen,
        color: Colors.red,
      ),
    );
  }

  Widget makeComplaintNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(onTap: () => internetTrue()),
    );
  }
}
