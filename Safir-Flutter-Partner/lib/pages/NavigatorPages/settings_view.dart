import 'package:flutter/material.dart';
import 'package:safir_driver/widgets/custom_close_button.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/landing_view.dart';
import '../login/login_view.dart';
import 'change_language_view.dart';

class SettingsView extends StatefulWidget {
  const SettingsView({super.key});

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  bool _isLoading = false;
  bool deleteAccount = false;
  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  themefun() async {
    if (isDarkTheme) {
      isDarkTheme = false;
      page = Colors.white;
      textColor = Colors.black;
      buttonColor = theme;
      loaderColor = theme;
      hintColor = const Color(0xff12121D).withOpacity(0.3);
    } else {
      isDarkTheme = true;
      page = const Color(0xFF3D3D3D);
      textColor = Colors.white.withOpacity(0.9);
      buttonColor = Colors.white;
      loaderColor = Colors.white;
      hintColor = Colors.white.withOpacity(0.3);
    }
    await getDetailsOfDevice();

    pref.setBool('isDarkTheme', isDarkTheme);

    valueNotifierHome.incrementNotifier();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return ValueListenableBuilder(
      valueListenable: valueNotifierHome.value,
      builder: (context, value, child) {
        return SafeArea(
          child: Material(
            child: Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Stack(
                children: [
                  Container(
                    padding: EdgeInsets.all(media.width * 0.05),
                    height: media.height * 1,
                    width: media.width * 1,
                    color: page,
                    child: Column(
                      children: [
                        settingsAppBar(media),
                        const SizedBox(height: 20),
                        SubMenu(
                          icon: Icons.language_outlined,
                          text: languages[choosenLanguage]
                              ['text_change_language'],
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ChangeLanguageView(),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 8),
                        ShowUp(
                          delay: 200,
                          child: Material(
                            elevation: 5,
                            child: InkWell(
                              onTap: () async => themefun(),
                              child: Container(
                                color: page,
                                padding: EdgeInsets.all(media.width * 0.03),
                                child: Row(
                                  children: [
                                    Icon(
                                      isDarkTheme
                                          ? Icons.brightness_4_outlined
                                          : Icons.brightness_3_rounded,
                                      size: media.width * 0.075,
                                      color: textColor.withOpacity(0.5),
                                    ),
                                    SizedBox(width: media.width * 0.025),
                                    Expanded(
                                      child: Text(
                                        languages[choosenLanguage]
                                            ['text_select_theme'],
                                        style: GoogleFonts.notoKufiArabic(
                                          fontSize: media.width * sixteen,
                                          color: textColor.withOpacity(0.8),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: media.width * 0.07,
                                      child: Switch(
                                        value: isDarkTheme,
                                        onChanged: (toggle) async => themefun(),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (userDetails['owner_id'] == null)
                          SubMenu(
                            icon: Icons.delete_outline,
                            text: languages[choosenLanguage]
                                ['text_delete_account'],
                            onTap: () => setState(() => deleteAccount = true),
                          ),
                      ],
                    ),
                  ),
                  if (deleteAccount == true) deleteAccountPopup(media),
                  if (_isLoading == true) const Positioned(child: Loading())
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget deleteAccountPopup(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () => setState(() => deleteAccount = false),
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  Text(
                    (userDetails['is_deleted_at'] == null)
                        ? languages[choosenLanguage]['text_delete_confirm']
                        : userDetails['is_deleted_at'].toString(),
                    textAlign: TextAlign.center,
                    style: GoogleFonts.notoKufiArabic(
                      fontSize: media.width * sixteen,
                      color: textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Button(
                    onTap: () async {
                      if (userDetails['is_deleted_at'] == null) {
                        setState(() => _isLoading = true);
                        var result = await userDelete();
                        if (result == 'success') {
                          await getUserDetails();
                          deleteAccount = false;
                        } else if (result == 'logout') {
                          navigateLogout();
                        } else {
                          deleteAccount = true;
                        }
                        setState(() => _isLoading = false);
                      } else {
                        setState(() => deleteAccount = false);
                      }
                    },
                    text: languages[choosenLanguage]['text_confirm'],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget settingsAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, true),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_settings'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }
}
