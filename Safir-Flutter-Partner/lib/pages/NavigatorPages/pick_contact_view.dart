import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:safir_driver/widgets/custom_close_button.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../noInternet/nointernet.dart';

class PickContactView extends StatefulWidget {
  const PickContactView({super.key});

  @override
  State<PickContactView> createState() => _PickContactViewState();
}

List contacts = [];

class _PickContactViewState extends State<PickContactView> {
  bool _isLoading = false;
  String pickedName = '';
  String pickedNumber = '';
  bool _contactDenied = false;
  bool _noPermission = false;

  @override
  void initState() {
    getContact();
    super.initState();
  }

//get contact permission
  getContactPermission() async {
    var status = await Permission.contacts.status;

    return status;
  }

//fetch contact data
  getContact() async {
    if (contacts.isEmpty) {
      var permission = await getContactPermission();
      if (permission == PermissionStatus.granted) {
        if (mounted) {
          setState(() => _isLoading = true);
        }

        List<Contact> contactsList = await FlutterContacts.getContacts(
          withProperties: true,
          deduplicateProperties: false,
        );

        // ignore: avoid_function_literals_in_foreach_calls
        contactsList.toSet().forEach((contact) {
          contact.phones.toSet().forEach((phone) {
            contacts.add({
              'name': contact.displayName,
              'phone': phone.number,
            });
          });
        });

        if (mounted) {
          setState(() => _isLoading = false);
        }
      } else {
        setState(() {
          _noPermission = true;
          _isLoading = false;
        });
      }
    }
  }

  //navigate pop

  pop() {
    Navigator.pop(context, true);
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LandingView()),
          (route) => false,
        );
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
          (route) => false,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      child: SafeArea(
        child: Material(
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Stack(
              children: [
                Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      pickContactAppBar(media),
                      const SizedBox(height: 20),
                      Expanded(
                          child: (contacts.isNotEmpty)
                              ? pickContactData(media)
                              : pickContactEmptyData(media)),
                      if (pickedName != '') confirmPickContactButton(media)
                    ],
                  ),
                ),
                if (_noPermission == true) pickContactNoPermission(media),
                if (_contactDenied) pickContactPermissionDeniedPopup(media),
                if (_isLoading == true) const Positioned(child: Loading()),
                if (internet == false) pickContactNoInternet(media)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget pickContactAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, true),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_sos'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
        InkWell(
          onTap: () {
            setState(() => contacts.clear());
            getContact();
          },
          child: Icon(Icons.replay_outlined, color: textColor),
        ),
      ],
    );
  }

  Widget pickContactNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }

  Widget pickContactNoPermission(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            PopUp(
              close: false,
              heading: languages[choosenLanguage]['text_req_permission'],
              heading2: languages[choosenLanguage]['text_contact_permission'],
              buttonText: languages[choosenLanguage]['text_confirm'],
              buttononTap: () async {
                var status = await Permission.contacts.request();
                setState(() {
                  _isLoading = true;
                  _noPermission = false;
                });
                if (status == PermissionStatus.granted) {
                  getContact();
                } else {
                  _contactDenied = true;
                  _isLoading = false;
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget pickContactPermissionDeniedPopup(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () {
                setState(() => _contactDenied = false);
                Navigator.pop(context, true);
              },
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2.0,
                    spreadRadius: 2.0,
                    color: Colors.black.withOpacity(0.2),
                  )
                ],
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: media.width * 0.8,
                    child: Text(
                      languages[choosenLanguage]['text_open_contact_setting'],
                      style: GoogleFonts.notoKufiArabic(
                        fontSize: media.width * sixteen,
                        color: textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () async => await openAppSettings(),
                        child: Text(
                          languages[choosenLanguage]['text_open_settings'],
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: media.width * sixteen,
                            color: buttonColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          setState(() => _contactDenied = false);
                          getContact();
                        },
                        child: Text(
                          languages[choosenLanguage]['text_done'],
                          style: GoogleFonts.notoKufiArabic(
                              fontSize: media.width * sixteen,
                              color: buttonColor,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget confirmPickContactButton(Size media) {
    return Container(
      padding: const EdgeInsets.only(top: 20),
      child: Button(
        onTap: () async {
          setState(() => _isLoading = true);
          var val = await addSos(pickedName, pickedNumber);
          if (val == 'success') {
            pop();
          } else if (val == 'logout') {
            navigateLogout();
          }
          setState(() => _isLoading = false);
        },
        text: languages[choosenLanguage]['text_confirm'],
      ),
    );
  }

  Widget pickContactEmptyData(Size media) {
    return Container(
      alignment: Alignment.center,
      height: media.width * 0.5,
      width: media.width * 0.5,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(
            (isDarkTheme) ? AppAssets.noDataFoundDark : AppAssets.noDataFound,
          ),
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget pickContactData(Size media) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: contacts
            .asMap()
            .map(
              (i, value) {
                return MapEntry(
                  i,
                  (sosData
                          .map((e) => e['number'])
                          .toString()
                          .replaceAll(' ', '')
                          .contains(contacts[i]['phone']
                              .toString()
                              .replaceAll(' ', '')))
                      ? Container()
                      : Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                pickedName = contacts[i]['name'];
                                pickedNumber = contacts[i]['phone'];
                              });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      contacts[i]['name'],
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.notoKufiArabic(
                                        fontSize: media.width * fourteen,
                                        fontWeight: FontWeight.w600,
                                        color: textColor,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      contacts[i]['phone'],
                                      style: GoogleFonts.notoKufiArabic(
                                        fontSize: media.width * twelve,
                                        color: textColor,
                                      ),
                                    )
                                  ],
                                ),
                                Container(
                                  height: media.width * 0.05,
                                  width: media.width * 0.05,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: textColor,
                                      width: 1.2,
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  child: (pickedName == contacts[i]['name'])
                                      ? Container(
                                          height: media.width * 0.03,
                                          width: media.width * 0.03,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: textColor,
                                          ),
                                        )
                                      : Container(),
                                )
                              ],
                            ),
                          ),
                        ),
                );
              },
            )
            .values
            .toList(),
      ),
    );
  }
}
