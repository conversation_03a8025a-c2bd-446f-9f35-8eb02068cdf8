import 'package:flutter/material.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';
import '../noInternet/nointernet.dart';
import 'withdraw_view.dart';

class BankDetailsView extends StatefulWidget {
  const BankDetailsView({Key? key}) : super(key: key);

  @override
  State<BankDetailsView> createState() => _BankDetailsViewState();
}

class _BankDetailsViewState extends State<BankDetailsView> {
//text controller for editing bank details
  TextEditingController holderName = TextEditingController();
  TextEditingController bankName = TextEditingController();
  TextEditingController accountNumber = TextEditingController();
  TextEditingController bankCode = TextEditingController();

  bool _isLoading = true;
  String _showError = '';
  bool _edit = false;

  @override
  void initState() {
    getBankDetails();
    super.initState();
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LandingView()),
          (route) => false,
        );
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
          (route) => false,
        );
      });
    }
  }

  getBankDetails() async {
    var val = await getBankInfo();
    if (val == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

//showing error
  _errorClear() async {
    Future.delayed(const Duration(seconds: 4), () {
      setState(() => _showError = '');
    });
  }

  //navigate pop
  pop() {
    Navigator.pop(context, true);
  }

  InputBorder? focusedBorder = OutlineInputBorder(
    borderSide:
        BorderSide(color: (isDarkTheme == true) ? textColor : Colors.blue),
  );

  InputBorder? border = OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    gapPadding: 1,
  );

  InputBorder? enabledBorder = OutlineInputBorder(
    borderSide: BorderSide(
      color: (isDarkTheme == true) ? textColor : hintColor,
    ),
    borderRadius: BorderRadius.circular(12),
    gapPadding: 1,
  );

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            body: Stack(
              children: [
                Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  padding: EdgeInsets.all(media.width * 0.05),
                  child: Column(
                    children: [
                      bankDetailsAppBar(media),
                      const SizedBox(height: 32),
                      Expanded(
                        child: SingleChildScrollView(
                          child: (bankData.isEmpty || _edit == true)
                              ? fillBankDetailsSection(media)
                              : bankDetailsData(media),
                        ),
                      ),
                      (_edit == true || bankData.isEmpty)
                          ? Row(
                              mainAxisAlignment: (bankData.isEmpty)
                                  ? MainAxisAlignment.center
                                  : MainAxisAlignment.spaceBetween,
                              children: [
                                if (bankData.isNotEmpty)
                                  cancelEditBankDetailsButton(media),
                                confirmFillBankDetailsButton(media),
                              ],
                            )
                          : editBankDetailsButton(media),
                    ],
                  ),
                ),
                if (_showError != '') bankDetailsError(media),
                if (internet == false) bankDetailsNoInternet(media),
                if (_isLoading == true) const Positioned(child: Loading())
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget bankDetailsAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_bankDetails'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget fillBankDetailsSection(Size media) {
    return Column(
      children: [
        ShowUp(
          delay: 100,
          child: TextField(
            controller: holderName,
            decoration: InputDecoration(
              labelText: languages[choosenLanguage]['text_accoutHolderName'],
              labelStyle: TextStyle(
                color: (isDarkTheme == true) ? textColor : null,
              ),
              focusedBorder: focusedBorder,
              border: border,
              enabledBorder: enabledBorder,
              isDense: true,
            ),
            style: GoogleFonts.notoKufiArabic(color: textColor),
          ),
        ),
        const SizedBox(height: 20),
        ShowUp(
          delay: 100,
          child: TextField(
            controller: accountNumber,
            decoration: InputDecoration(
              labelText: languages[choosenLanguage]['text_accountNumber'],
              labelStyle: TextStyle(
                color: (isDarkTheme == true) ? textColor : null,
              ),
              focusedBorder: focusedBorder,
              border: border,
              enabledBorder: enabledBorder,
              isDense: true,
            ),
            style: GoogleFonts.notoKufiArabic(color: textColor),
          ),
        ),
        const SizedBox(height: 20),
        ShowUp(
          delay: 100,
          child: TextField(
            controller: bankName,
            decoration: InputDecoration(
              labelText: languages[choosenLanguage]['text_bankName'],
              labelStyle: TextStyle(
                color: (isDarkTheme == true) ? textColor : null,
              ),
              focusedBorder: focusedBorder,
              border: border,
              enabledBorder: enabledBorder,
              isDense: true,
            ),
            style: GoogleFonts.notoKufiArabic(color: textColor),
          ),
        ),
        const SizedBox(height: 20),
        ShowUp(
          delay: 100,
          child: TextField(
            controller: bankCode,
            decoration: InputDecoration(
              labelText: languages[choosenLanguage]['text_bankCode'],
              labelStyle: TextStyle(
                color: (isDarkTheme == true) ? textColor : null,
              ),
              focusedBorder: focusedBorder,
              border: border,
              enabledBorder: enabledBorder,
              isDense: true,
            ),
            style: GoogleFonts.notoKufiArabic(color: textColor),
          ),
        ),
        SizedBox(height: media.width * 0.1),
      ],
    );
  }

  Widget bankDetailsData(Size media) {
    return SizedBox(
      width: media.width * 0.9,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyText(
            text: languages[choosenLanguage]['text_accoutHolderName'],
            size: media.width * sixteen,
            color: hintColor,
          ),
          const SizedBox(height: 6),
          MyText(
            text: bankData['account_name'],
            size: media.width * sixteen,
            color: textColor,
          ),
          const SizedBox(height: 20),
          MyText(
            text: languages[choosenLanguage]['text_bankName'],
            size: media.width * sixteen,
            color: hintColor,
          ),
          const SizedBox(height: 6),
          MyText(
            text: bankData['bank_name'],
            size: media.width * sixteen,
            color: textColor,
          ),
          const SizedBox(height: 20),
          MyText(
            text: languages[choosenLanguage]['text_accountNumber'],
            size: media.width * sixteen,
            color: hintColor,
          ),
          const SizedBox(height: 6),
          MyText(
            text: bankData['account_no'],
            size: media.width * sixteen,
            color: textColor,
          ),
          const SizedBox(height: 20),
          MyText(
            text: languages[choosenLanguage]['text_bankCode'],
            size: media.width * sixteen,
            color: hintColor,
          ),
          const SizedBox(height: 6),
          MyText(
            text: bankData['bank_code'],
            size: media.width * sixteen,
            color: textColor,
          )
        ],
      ),
    );
  }

  Widget editBankDetailsButton(Size media) {
    return Button(
      onTap: () {
        setState(() {
          accountNumber.text = bankData['account_no'].toString();
          bankName.text = bankData['bank_name'];
          bankCode.text = bankData['bank_code'];
          holderName.text = bankData['account_name'];
          _edit = true;
        });
      },
      text: languages[choosenLanguage]['text_edit'],
    );
  }

  Widget cancelEditBankDetailsButton(Size media) {
    return Button(
      onTap: () => setState(() => _edit = false),
      width: media.width * 0.4,
      text: languages[choosenLanguage]['text_cancel'],
    );
  }

  Widget confirmFillBankDetailsButton(Size media) {
    return Button(
      onTap: () async {
        FocusManager.instance.primaryFocus?.unfocus();
        if (holderName.text.isNotEmpty &&
            accountNumber.text.isNotEmpty &&
            bankCode.text.isNotEmpty &&
            bankName.text.isNotEmpty) {
          setState(() => _isLoading = true);
          var val = await addBankData(holderName.text, accountNumber.text,
              bankCode.text, bankName.text);
          if (val == 'success') {
            setState(() => _edit = false);
            if (addBank == true) {
              pop();
            }
          } else if (val == 'logout') {
            navigateLogout();
          } else {
            setState(() {
              _showError = val.toString();
              _errorClear();
            });
          }
          setState(() => _isLoading = false);
        }
      },
      width: (bankData.isEmpty) ? media.width * 0.88 : media.width * 0.4,
      color: (holderName.text == '' ||
              accountNumber.text == '' ||
              bankCode.text == '' ||
              bankName.text == '')
          ? Colors.grey
          : buttonColor,
      text: languages[choosenLanguage]['text_confirm'],
    );
  }

  Widget bankDetailsNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }

  Widget bankDetailsError(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.center,
              width: media.width * 0.8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    spreadRadius: 2,
                    blurRadius: 2,
                  )
                ],
              ),
              padding: EdgeInsets.all(media.width * 0.05),
              child: SizedBox(
                width: media.width * 0.7,
                child: MyText(
                  text: _showError.toString(),
                  size: media.width * sixteen,
                  textAlign: TextAlign.center,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
