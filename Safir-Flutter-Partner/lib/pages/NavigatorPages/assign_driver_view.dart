import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';

class AssignDriverView extends StatefulWidget {
  final String? fleetid;
  final int? i;
  const AssignDriverView({Key? key, required this.fleetid, required this.i})
      : super(key: key);

  @override
  State<AssignDriverView> createState() => _AssignDriverViewState();
}

class _AssignDriverViewState extends State<AssignDriverView> {
  String isassigndriver = '';
  int? driverid;
  bool _isLoadingassigndriver = true;
  bool _showToast = false;

  @override
  void initState() {
    setState(() {
      getdriverdata();
      isassigndriver = '';
    });
    super.initState();
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  getdriverdata() async {
    var val =
        await fleetDriverDetails(fleetid: widget.fleetid, isassigndriver: true);
    if (val == 'logout') {
      navigateLogout();
    }
    var val1 = await getVehicleInfo();
    if (val1 == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      setState(() {
        // if (_isLoadingassigndriver == true) {
        //   showToast();
        // }
        _isLoadingassigndriver = false;
      });
    }
  }

  showToast() {
    setState(() {
      _showToast = true;
    });
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _showToast = false;
      });
    });
  }

  String fleetid = '';

  //navigate
  pop() {
    Navigator.pop(context, true);
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                child: Column(
                  children: [
                    assignDriverAppBar(media),
                    const SizedBox(height: 20),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          children: [
                            vehicleDetails(media),
                            MyText(
                              text: languages[choosenLanguage]
                                  ['text_assign_new_driver'],
                              size: media.width * eighteen,
                              fontweight: FontWeight.bold,
                            ),
                            const SizedBox(height: 20),
                            Container(
                              child: fleetdriverList.isNotEmpty
                                  ? fleetDriverList(media)
                                  : (_isLoadingassigndriver == false)
                                      ? noFleetDrivers(media)
                                      : Container(),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    assignDriverButton(media),
                  ],
                ),
              ),
              if (_isLoadingassigndriver) const Positioned(child: Loading()),
              if (_showToast == true) assignDriverShowToast(media),
            ],
          ),
        ),
      ),
    );
  }

  Widget assignDriverAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, true),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_manage_drivers'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget vehicleDetails(Size media) {
    return SizedBox(
      height: media.width * 0.35,
      child: Stack(
        children: [
          Container(
            height: media.width * 0.3,
            width: media.width,
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: hintColor),
            ),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (vehicledata[widget.i!]['driverDetail'] != null)
                      Container(
                        height: media.width * 0.20,
                        width: media.width * 0.20,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                              image: NetworkImage(
                                vehicledata[widget.i!]['driverDetail']['data']
                                        ['profile_picture']
                                    .toString(),
                              ),
                              fit: BoxFit.fill),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                        ),
                      ),
                    if (vehicledata[widget.i!]['driverDetail'] != null)
                      MyText(
                        text: vehicledata[widget.i!]['driverDetail']['data']
                                ['name']
                            .toString(),
                        size: media.width * sixteen,
                        fontweight: FontWeight.bold,
                      ),
                    if (vehicledata[widget.i!]['driverDetail'] == null)
                      MyText(
                        text: languages[choosenLanguage]['text_no_driver'],
                        size: media.width * sixteen,
                        fontweight: FontWeight.bold,
                      ),
                  ],
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  height: media.width * 0.3,
                  width: 1,
                  color: hintColor,
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (vehicledata[widget.i!]['driverDetail'] != null)
                          MyText(
                            text: vehicledata[widget.i!]['driverDetail']['data']
                                    ['mobile']
                                .toString(),
                            size: media.width * fourteen,
                          ),
                        MyText(
                          text: vehicledata[widget.i!]['license_number']
                              .toString(),
                          size: media.width * fourteen,
                        ),
                        MyText(
                          text:
                              '${vehicledata[widget.i!]['brand']},${vehicledata[widget.i!]['model']}',
                          size: media.width * fourteen,
                        ),
                        Container(
                          height: media.width * 0.1,
                          width: media.width * 0.2,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: NetworkImage(
                                vehicledata[widget.i!]['type_icon'].toString(),
                              ),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget fleetDriverList(Size media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (var i = 0; i < fleetdriverList.length; i++)
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Stack(
              children: [
                InkWell(
                  onTap: () {
                    setState(() {
                      isassigndriver = i.toString();
                      driverid =
                          fleetdriverList[int.parse(isassigndriver)]['id'];
                    });
                  },
                  child: Container(
                    width: media.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      border: Border.all(color: hintColor),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(media.width * 0.02),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                height: media.width * 0.20,
                                width: media.width * 0.20,
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                    image: NetworkImage(
                                      fleetdriverList[i]['profile_picture']
                                          .toString(),
                                    ),
                                    fit: BoxFit.fill,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              MyText(
                                text: fleetdriverList[i]['name'].toString(),
                                maxLines: 1,
                                size: media.width * sixteen,
                                fontweight: FontWeight.bold,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: media.width * 0.3,
                          width: 1,
                          color: hintColor,
                        ),
                        Expanded(
                          flex: 9,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MyText(
                                  text: fleetdriverList[i]['mobile'].toString(),
                                  size: media.width * fourteen,
                                ),
                                // ignore: unnecessary_null_comparison
                                if (fleetdriverList[i]['car_number']
                                        .toString() !=
                                    'null')
                                  MyText(
                                    text: fleetdriverList[i]['car_number']
                                        .toString(),
                                    size: media.width * fourteen,
                                  ),
                                if (fleetdriverList[i]['car_make_name']
                                            .toString() !=
                                        'null' &&
                                    fleetdriverList[i]['car_model_name'] !=
                                        null)
                                  MyText(
                                    text:
                                        '${fleetdriverList[i]['car_make_name']},${fleetdriverList[i]['car_model_name']}',
                                    size: media.width * fourteen,
                                  ),

                                if (fleetdriverList[i]['vehicle_type_icon'] !=
                                    null)
                                  Container(
                                    height: media.width * 0.1,
                                    width: media.width * 0.2,
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                          image: NetworkImage(
                                            fleetdriverList[i]
                                                    ['vehicle_type_icon']
                                                .toString(),
                                          ),
                                          fit: BoxFit.cover),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
                            alignment: Alignment.center,
                            height: media.width * 0.08,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(),
                            ),
                            child: Container(
                              height: media.width * 0.04,
                              decoration: BoxDecoration(
                                color: isassigndriver != i.toString()
                                    ? Colors.green
                                    : page,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget noFleetDrivers(Size media) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: media.width * 0.3),
        Container(
          alignment: Alignment.center,
          height: media.width * 0.5,
          width: media.width * 0.5,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                (isDarkTheme)
                    ? AppAssets.noDataFoundDark
                    : AppAssets.noDataFound,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: media.width * 0.8,
          child: MyText(
            text: languages[choosenLanguage]['text_noDataFound'],
            textAlign: TextAlign.center,
            fontweight: FontWeight.w800,
            size: media.width * sixteen,
          ),
        ),
      ],
    );
  }

  Widget assignDriverButton(Size media) {
    return Button(
      onTap: () async {
        setState(() => _isLoadingassigndriver = true);
        var result = await assignDriver(driverid, widget.fleetid);
        if (result == 'true') {
          var val = await getVehicleInfo();
          if (val == 'logout') {
            navigateLogout();
          } else {
            pop();
          }
        } else if (result == 'logout') {
          navigateLogout();
        } else {
          setState(() => _isLoadingassigndriver = false);
          showToast();
        }
      },
      text: languages[choosenLanguage]['text_assign_driver'],
    );
  }

  Widget assignDriverShowToast(Size media) {
    return Positioned(
      bottom: media.height * 0.2,
      left: media.width * 0.2,
      right: media.width * 0.2,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(media.width * 0.025),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.transparent.withOpacity(0.6)),
        child: MyText(
          text: languages[choosenLanguage]['text_select_driver'],
          size: media.width * twelve,
          color: topBar,
        ),
      ),
    );
  }
}
