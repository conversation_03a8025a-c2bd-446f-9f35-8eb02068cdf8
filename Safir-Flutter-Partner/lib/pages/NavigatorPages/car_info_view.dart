import 'package:flutter/material.dart';
import 'package:safir_driver/functions/functions.dart';
import 'package:safir_driver/pages/login/fill_car_info_view.dart';
import 'package:safir_driver/styles/styles.dart';
import 'package:safir_driver/translation/translation.dart';
import 'package:safir_driver/widgets/widgets.dart';

class CarInfoView extends StatefulWidget {
  const CarInfoView({super.key});

  @override
  State<CarInfoView> createState() => _CarInfoViewState();
}

class _CarInfoViewState extends State<CarInfoView> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;

    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Container(
            color: page,
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                carInfoAppBar(media),
                const SizedBox(height: 20),
                (userDetails['owner_id'] != null &&
                        userDetails['vehicle_type_name'] == null)
                    ? Expanded(
                        child: Center(
                          child: MyText(
                            text: languages[choosenLanguage]
                                ['text_no_fleet_assigned'],
                            size: media.width * eighteen,
                            fontweight: FontWeight.bold,
                          ),
                        ),
                      )
                    : carInfoData(media),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget carInfoAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, true),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_car_info'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
        if (userDetails['owner_id'] == null)
          InkWell(
            onTap: () async {
              myServiceId = userDetails['service_location_id'];
              var nav = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FillCarInfoView(frompage: 2),
                ),
              );
              if (nav != null) {
                if (nav) {
                  setState(() {});
                }
              }
            },
            child: MyText(
              textAlign: TextAlign.end,
              maxLines: 1,
              text: languages[choosenLanguage]['text_edit'],
              color: buttonColor,
              size: media.width * sixteen,
              fontweight: FontWeight.w500,
            ),
          )
      ],
    );
  }

  Widget carInfoData(Size media) {
    return Column(
      children: [
        SizedBox(
          width: media.width * 0.9,
          child: MyText(
            text: languages[choosenLanguage]['text_type'],
            size: media.width * fourteen,
            color: hintColor,
          ),
        ),
        Container(
          height: media.width * 0.1,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                  color: (isDarkTheme == true)
                      ? textColor.withOpacity(0.4)
                      : underline),
            ),
            color: page,
          ),
          child: Row(
            children: [
              if (userDetails['owner_id'] == null)
                for (int i = 0;
                    i <= userDetails['driverVehicleType']['data'].length - 1;
                    i++)
                  MyText(
                    text:
                        '${userDetails['driverVehicleType']['data'][i]['vehicletype_name']},',
                    size: media.width * fourteen,
                    color: textColor,
                  ),
              if (userDetails['owner_id'] != null)
                MyText(
                  text: userDetails['vehicle_type_name'],
                  size: media.width * sixteen,
                  color: textColor,
                ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        ProfileDetails(
          heading: languages[choosenLanguage]['text_make_name'],
          readyonly: true,
          value: userDetails['car_make_name'],
        ),
        const SizedBox(height: 20),
        ProfileDetails(
          heading: languages[choosenLanguage]['text_model_name'],
          readyonly: true,
          value: userDetails['car_model_name'],
        ),
        const SizedBox(height: 20),
        ProfileDetails(
          heading: languages[choosenLanguage]['text_license'],
          readyonly: true,
          value: userDetails['car_number'],
        ),
        const SizedBox(height: 20),
        ProfileDetails(
          heading: languages[choosenLanguage]['text_color'],
          readyonly: true,
          value: userDetails['car_color'],
        ),
      ],
    );
  }
}
