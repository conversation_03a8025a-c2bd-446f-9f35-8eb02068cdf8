import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/loadingPage/loading.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:safir_driver/widgets/custom_close_button.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../login/landing_view.dart';

class NotificationView extends StatefulWidget {
  const NotificationView({Key? key}) : super(key: key);

  @override
  State<NotificationView> createState() => _NotificationViewState();
}

class _NotificationViewState extends State<NotificationView> {
  bool isLoading = true;
  bool error = false;
  dynamic notificationid;

  @override
  void initState() {
    getdata();
    super.initState();
  }

  getdata() async {
    var val = await getnotificationHistory();
    if (val == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      isLoading = false;
    }
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LandingView()),
          (route) => false,
        );
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
          (route) => false,
        );
      });
    }
  }

  bool showinfo = false;
  int? showinfovalue;

  bool showToastbool = false;

  showToast() async {
    setState(() => showToastbool = true);
    Future.delayed(const Duration(seconds: 1), () async {
      setState(() => showToastbool = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: ValueListenableBuilder(
          valueListenable: valueNotifierHome.value,
          builder: (context, value, child) {
            return Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Stack(
                children: [
                  Container(
                    height: media.height * 1,
                    width: media.width * 1,
                    color: page,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        notificationAppBar(media),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: Column(
                              children: [
                                (notificationHistory.isNotEmpty)
                                    ? Column(
                                        children: [
                                          notificationData(media),
                                          loadMoreButton(media),
                                        ],
                                      )
                                    : emptyNotifications(media),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (showinfo == true) notificationShowInfo(media),
                  if (error == true) removeNotificationPopup(media),
                  if (isLoading == true) const Positioned(child: Loading()),
                  if (showToastbool == true) removeNotificationToast(media)
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget notificationAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_notification'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget removeNotificationPopup(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]
                        ['text_delete_notification'],
                    size: media.width * sixteen,
                    fontweight: FontWeight.w600,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Button(
                        onTap: () async {
                          setState(() {
                            error = false;
                            notificationid = null;
                          });
                        },
                        text: languages[choosenLanguage]['text_no'],
                        width: media.width * 0.3,
                      ),
                      Button(
                        onTap: () async {
                          setState(() => isLoading = true);
                          var result = await deleteNotification(notificationid);
                          if (result == 'success') {
                            setState(() {
                              getdata();
                              error = false;
                              isLoading = false;
                              showToast();
                            });
                          } else if (result == 'logout') {
                            navigateLogout();
                          }
                        },
                        text: languages[choosenLanguage]['text_yes'],
                        width: media.width * 0.3,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget removeNotificationToast(Size media) {
    return Positioned(
      bottom: media.height * 0.2,
      left: media.width * 0.2,
      right: media.width * 0.2,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(media.width * 0.025),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.transparent.withOpacity(0.6)),
        child: MyText(
          text: languages[choosenLanguage]['text_notification_deleted'],
          size: media.width * twelve,
          color: topBar,
        ),
      ),
    );
  }

  Widget notificationShowInfo(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () {
                setState(() {
                  showinfo = false;
                  showinfovalue = null;
                });
              },
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  MyText(
                    text:
                        notificationHistory[showinfovalue!]['title'].toString(),
                    size: media.width * sixteen,
                    fontweight: FontWeight.w600,
                  ),
                  const SizedBox(height: 20),
                  MyText(
                    text:
                        notificationHistory[showinfovalue!]['body'].toString(),
                    size: media.width * fourteen,
                    color: hintColor,
                  ),
                  if (notificationHistory[showinfovalue!]['image'] != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: Image.network(
                        notificationHistory[showinfovalue!]['image'],
                        height: media.width * 0.4,
                        width: media.width * 0.4,
                        fit: BoxFit.contain,
                      ),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget emptyNotifications(Size media) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: media.width * 0.3),
        Container(
          alignment: Alignment.center,
          height: media.width * 0.5,
          width: media.width * 0.5,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                (isDarkTheme)
                    ? AppAssets.noDataFoundDark
                    : AppAssets.noDataFound,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: 24),
        MyText(
          text: languages[choosenLanguage]['text_noDataFound'],
          textAlign: TextAlign.center,
          fontweight: FontWeight.w800,
          size: media.width * sixteen,
        ),
      ],
    );
  }

  Widget loadMoreButton(Size media) {
    return (notificationHistoryPage['pagination'] != null)
        ? (notificationHistoryPage['pagination']['current_page'] <
                notificationHistoryPage['pagination']['total_pages'])
            ? InkWell(
                onTap: () async {
                  setState(() => isLoading = true);
                  var val = await getNotificationPages(
                      'page=${notificationHistoryPage['pagination']['current_page'] + 1}');
                  if (val == 'logout') {
                    navigateLogout();
                  }
                  setState(() => isLoading = false);
                },
                child: Container(
                  padding: EdgeInsets.all(media.width * 0.025),
                  margin: EdgeInsets.only(bottom: media.width * 0.05),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: page,
                    border: Border.all(
                      color: borderLines,
                      width: 1.2,
                    ),
                  ),
                  child: Text(
                    languages[choosenLanguage]['text_loadmore'],
                    style: GoogleFonts.notoSans(
                      fontSize: media.width * sixteen,
                      color: textColor,
                    ),
                  ),
                ),
              )
            : Container()
        : Container();
  }

  Widget notificationData(Size media) {
    return Column(
      children: notificationHistory
          .asMap()
          .map((i, value) {
            return MapEntry(
              i,
              InkWell(
                onTap: () {
                  setState(() {
                    showinfovalue = i;
                    showinfo = true;
                  });
                },
                child: ShowUp(
                  delay: 100,
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: media.width * 0.02),
                    width: media.width * 0.9,
                    padding: EdgeInsets.all(media.width * 0.025),
                    decoration: BoxDecoration(
                      border: Border.all(color: borderLines, width: 1.2),
                      borderRadius: BorderRadius.circular(12),
                      color: page,
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              height: media.width * 0.1067,
                              width: media.width * 0.1067,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: textColor.withOpacity(0.25),
                              ),
                              alignment: Alignment.center,
                              child: Icon(
                                Icons.notifications,
                                color: textColor,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    notificationHistory[i]['title'].toString(),
                                    overflow: TextOverflow.ellipsis,
                                    style: GoogleFonts.notoKufiArabic(
                                      fontSize: media.width * fourteen,
                                      color: textColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    notificationHistory[i]['body'].toString(),
                                    overflow: TextOverflow.ellipsis,
                                    style: GoogleFonts.notoKufiArabic(
                                      fontSize: media.width * twelve,
                                      color: hintColor,
                                    ),
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    notificationHistory[i]
                                            ['converted_created_at']
                                        .toString(),
                                    overflow: TextOverflow.ellipsis,
                                    style: GoogleFonts.notoKufiArabic(
                                        fontSize: media.width * twelve,
                                        color: textColor,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              alignment: Alignment.centerRight,
                              width: media.width * 0.15,
                              child: IconButton(
                                onPressed: () {
                                  setState(() {
                                    error = true;
                                    notificationid =
                                        notificationHistory[i]['id'];
                                  });
                                },
                                icon: const Icon(
                                  Icons.delete_forever,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (notificationHistory[i]['image'] != null)
                          Padding(
                            padding: EdgeInsets.only(top: media.width * 0.02),
                            child: Image.network(
                              notificationHistory[i]['image'],
                              height: media.width * 0.1,
                              width: media.width * 0.8,
                              fit: BoxFit.contain,
                            ),
                          )
                      ],
                    ),
                  ),
                ),
              ),
            );
          })
          .values
          .toList(),
    );
  }
}
