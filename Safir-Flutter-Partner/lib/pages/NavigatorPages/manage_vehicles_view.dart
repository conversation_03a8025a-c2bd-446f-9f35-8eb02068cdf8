import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/fill_car_info_view.dart';
import '../login/landing_view.dart';
import '../noInternet/nointernet.dart';
import 'assign_driver_view.dart';
import 'fleetdocuments.dart';

class ManageVehiclesView extends StatefulWidget {
  const ManageVehiclesView({Key? key}) : super(key: key);

  @override
  State<ManageVehiclesView> createState() => _ManageVehiclesViewState();
}

String fleetid = '';

class _ManageVehiclesViewState extends State<ManageVehiclesView> {
  bool _isLoading = true;
  String isclickmenu = '';

  @override
  void initState() {
    getvehicledata();
    super.initState();
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  getvehicledata() async {
    isclickmenu = '';
    var val = await getVehicleInfo();
    if (val == 'logout') {
      navigateLogout();
    }
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        Navigator.popUntil(context, (route) => route.isFirst);
        return true;
      },
      child: SafeArea(
        child: Material(
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  child: Column(
                    children: [
                      manageVehiclesAppBar(media),
                      const SizedBox(height: 20),
                      Expanded(
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          child: vehicledata.isNotEmpty
                              ? vehiclesList(media)
                              : (_isLoading == false)
                                  ? noVehicles(media)
                                  : Container(),
                        ),
                      ),
                      const SizedBox(height: 12),
                      addVehicleButton(media),
                    ],
                  ),
                ),
                if (_isLoading == true) const Positioned(child: Loading()),
                if (internet == false) manageVehiclesNoInternet(media),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget manageVehiclesAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.popUntil(context, (route) => route.isFirst),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_manage_vehicle'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget noVehicles(Size media) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: media.width * 0.3),
        Container(
          alignment: Alignment.center,
          height: media.width * 0.5,
          width: media.width * 0.5,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                (isDarkTheme)
                    ? AppAssets.noDataFoundDark
                    : AppAssets.noDataFound,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: media.width * 0.8,
          child: MyText(
            text: languages[choosenLanguage]['text_noDataFound'],
            textAlign: TextAlign.center,
            fontweight: FontWeight.w800,
            size: media.width * sixteen,
          ),
        ),
      ],
    );
  }

  Widget vehiclesList(Size media) {
    return Column(
      children: [
        for (var i = 0; i < vehicledata.length; i++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Stack(
              children: [
                InkWell(
                  onTap: () {
                    setState(() {
                      isclickmenu = '';
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    height: media.width * 0.3,
                    width: media.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      border: Border.all(color: hintColor),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          height: media.width * 0.3,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: Container(
                                  width: media.width * 0.2,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(
                                        vehicledata[i]['type_icon'].toString(),
                                      ),
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ),
                              MyText(
                                text:
                                    vehicledata[i]['license_number'].toString(),
                                size: media.width * fourteen,
                              ),
                              MyText(
                                text: vehicledata[i]['brand'].toString(),
                                size: media.width * fourteen,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: media.width * 0.3,
                          width: 1,
                          color: hintColor,
                        ),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Stack(
                              children: [
                                Center(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                        image: AssetImage(
                                          vehicledata[i]['approve']
                                                      .toString() ==
                                                  '1'
                                              ? AppAssets.approved
                                              : AppAssets.waitIcon,
                                        ),
                                        opacity: 0.4,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                    height: media.width * 0.15,
                                    width: media.width * 0.15,
                                  ),
                                ),
                                Positioned(
                                  child: SizedBox(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        if (vehicledata[i]['driverDetail'] !=
                                            null)
                                          MyText(
                                            text: vehicledata[i]['driverDetail']
                                                    ['data']['name']
                                                .toString(),
                                            size: media.width * fourteen,
                                            fontweight: FontWeight.bold,
                                          ),
                                        if (vehicledata[i]['driverDetail'] !=
                                            null)
                                          MyText(
                                            text: vehicledata[i]['driverDetail']
                                                    ['data']['mobile']
                                                .toString(),
                                            size: media.width * fourteen,
                                          ),
                                        MyText(
                                          text: vehicledata[i]['model']
                                              .toString(),
                                          size: media.width * fourteen,
                                          fontweight: FontWeight.bold,
                                        ),
                                        if (vehicledata[i]['driverDetail'] ==
                                            null)
                                          MyText(
                                            text: languages[choosenLanguage]
                                                ['text_driver_not_assigned'],
                                            size: media.width * sixteen,
                                            fontweight: FontWeight.bold,
                                          ),
                                        if (vehicledata[i]['driverDetail'] ==
                                                null &&
                                            vehicledata[i]['approve']
                                                    .toString() ==
                                                '0')
                                          Container(
                                            alignment: Alignment.center,
                                            padding: const EdgeInsets.all(4),
                                            decoration: BoxDecoration(
                                              color: buttonColor,
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                            ),
                                            child: MyText(
                                                text: languages[choosenLanguage]
                                                    ['text_waiting_approval'],
                                                size: media.width * fourteen,
                                                color: buttonText),
                                          )
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: () {
                                setState(() {
                                  if (isclickmenu == i.toString()) {
                                    isclickmenu = '';
                                  } else {
                                    isclickmenu = i.toString();
                                  }
                                  fleetid =
                                      vehicledata[int.parse(isclickmenu)]['id'];
                                });
                              },
                              child: Icon(
                                Icons.more_vert,
                                color: textColor,
                                size: 30,
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                if (isclickmenu == i.toString())
                  Positioned(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          height: media.width * 0.3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.0),
                            color: page,
                            border: Border.all(color: hintColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              if (vehicledata[i]['approve'].toString() != '0')
                                MenuClass(
                                  ontap: () async {
                                    var nav = await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => AssignDriverView(
                                            fleetid: fleetid, i: i),
                                      ),
                                    );
                                    if (nav != null) {
                                      if (nav) {
                                        isclickmenu = '';
                                        getvehicledata();
                                      }
                                    }
                                  },
                                  text: languages[choosenLanguage]
                                      ['text_assign_driver'],
                                ),
                              MenuClass(
                                ontap: () async {
                                  var nav = await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          FleetDocuments(fleetid: fleetid),
                                    ),
                                  );
                                  if (nav != null) {
                                    if (nav) {
                                      setState(() => isclickmenu = '');
                                    }
                                  }
                                },
                                text: (vehicledata[i]['approve'].toString() !=
                                        '1')
                                    ? languages[choosenLanguage]
                                        ['text_upload_doc']
                                    : languages[choosenLanguage]
                                        ['text_edit_docs'],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ),
      ],
    );
  }

  Widget addVehicleButton(Size media) {
    return Button(
      onTap: () async {
        myServiceId = userDetails['service_location_id'];
        isowner = true;
        var nav = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FillCarInfoView(frompage: 3),
          ),
        );
        if (nav != null) {
          if (nav) {
            getvehicledata();
          }
        }
      },
      text: languages[choosenLanguage]['text_add_vehicle'],
    );
  }

  Widget manageVehiclesNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}

class MenuClass extends StatelessWidget {
  final String text;
  final void Function() ontap;
  const MenuClass({Key? key, required this.text, required this.ontap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;

    return Padding(
      padding:
          EdgeInsets.only(left: media.width * 0.03, top: media.width * 0.01),
      child: InkWell(
        onTap: ontap,
        child: SizedBox(
          width: media.width * 0.4,
          height: media.width * 0.08,
          child: MyText(
            text: text,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            size: media.width * fourteen,
            fontweight: FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
