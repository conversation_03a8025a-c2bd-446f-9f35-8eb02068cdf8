import 'dart:async';
import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:safir_driver/widgets/custom_close_button.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:uuid/uuid.dart';
import 'package:geolocator/geolocator.dart' as geolocs;
import 'package:permission_handler/permission_handler.dart' as perm;

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/landing_view.dart';
import '../noInternet/nointernet.dart';
import 'map_page.dart';
import 'package:flutter_map/flutter_map.dart' as fm;
// ignore: depend_on_referenced_packages
import 'package:latlong2/latlong.dart' as fmlt;

class DropLocationView extends StatefulWidget {
  const DropLocationView({Key? key}) : super(key: key);

  @override
  State<DropLocationView> createState() => _DropLocationViewState();
}

List<AddressList> addressList = <AddressList>[];
bool serviceNotAvailable = false;

class _DropLocationViewState extends State<DropLocationView>
    with WidgetsBindingObserver {
  GoogleMapController? _controller;
  final fm.MapController _fmController = fm.MapController();
  late PermissionStatus permission;
  Location location = Location();
  String _state = '';
  bool _isLoading = false;
  String sessionToken = const Uuid().v4();
  final _debouncer = Debouncer(milliseconds: 1000);
  LatLng _center = const LatLng(41.4219057, -102.0840772);
  LatLng centerLocation = const LatLng(41.4219057, -102.0840772);
  TextEditingController search = TextEditingController();
  String favNameText = '';
  bool _locationDenied = false;
  bool favAddressAdd = false;
  bool droplocation = false;
  String dropAddressConfirmation = '';
  bool _error = false;
  TextEditingController username = TextEditingController();
  TextEditingController userphonenumber = TextEditingController();

  void _onMapCreated(GoogleMapController controller) {
    setState(() {
      _controller = controller;
      _controller?.setMapStyle(mapStyle);
    });
  }

  navigate() {
    // Navigator.push(context, MaterialPageRoute(builder: (context)=> Maps()));
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const Maps()),
        (route) => false);
    setState(() {
      _isLoading = false;
    });
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false);
      });
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false);
      });
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    getLocs();
    addAutoFill.clear();
    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (_controller != null) {
        _controller?.setMapStyle(mapStyle);
      }
    }
  }

//get current location
  getLocs() async {
    permission = await location.hasPermission();

    if (permission == PermissionStatus.denied ||
        permission == PermissionStatus.deniedForever) {
      setState(() {
        _state = '3';
        _isLoading = false;
      });
    } else if (permission == PermissionStatus.granted ||
        permission == PermissionStatus.grantedLimited) {
      var locs = await geolocs.Geolocator.getLastKnownPosition();
      if (locs != null) {
        setState(() {
          _center = LatLng(double.parse(locs.latitude.toString()),
              double.parse(locs.longitude.toString()));
          centerLocation = LatLng(double.parse(locs.latitude.toString()),
              double.parse(locs.longitude.toString()));
        });
      } else {
        var loc = await geolocs.Geolocator.getCurrentPosition(
            desiredAccuracy: geolocs.LocationAccuracy.low);
        setState(() {
          _center = LatLng(double.parse(loc.latitude.toString()),
              double.parse(loc.longitude.toString()));
          centerLocation = LatLng(double.parse(loc.latitude.toString()),
              double.parse(loc.longitude.toString()));
        });
      }
      _controller?.animateCamera(CameraUpdate.newLatLngZoom(center, 14.0));
      setState(() {
        _state = '3';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: ValueListenableBuilder(
          valueListenable: valueNotifierHome.value,
          builder: (context, value, child) {
            return Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Container(
                height: media.height * 1,
                width: media.width * 1,
                color: page,
                child: Stack(
                  children: [
                    SizedBox(
                      height: media.height * 1,
                      width: media.width * 1,
                      child: (_state == '3')
                          ? (mapType == 'google')
                              ? GoogleMap(
                                  onMapCreated: _onMapCreated,
                                  initialCameraPosition: CameraPosition(
                                    target: _center,
                                    zoom: 14.0,
                                  ),
                                  onCameraMove: (CameraPosition position) {
                                    //pick current location
                                    setState(() {
                                      centerLocation = position.target;
                                    });
                                  },
                                  onCameraIdle: () async {
                                    var val = await geoCoding(
                                        centerLocation.latitude,
                                        centerLocation.longitude);
                                    dropAddressConfirmation = val;
                                    setState(() {});
                                  },
                                  minMaxZoomPreference:
                                      const MinMaxZoomPreference(8.0, 20.0),
                                  myLocationButtonEnabled: false,
                                  buildingsEnabled: false,
                                  zoomControlsEnabled: false,
                                  myLocationEnabled: true,
                                )
                              : fm.FlutterMap(
                                  mapController: _fmController,
                                  options: fm.MapOptions(
                                      onMapEvent: (v) async {
                                        if (v.source ==
                                                fm.MapEventSource
                                                    .nonRotatedSizeChange &&
                                            addressList.isEmpty) {
                                          _center = LatLng(
                                              v.camera.center.latitude,
                                              v.camera.center.longitude);
                                          setState(() {});

                                          var val = await geoCoding(
                                              _center.latitude,
                                              _center.longitude);
                                          if (val != '') {
                                            setState(() {
                                              dropAddressConfirmation = val;
                                            });
                                          }
                                        }
                                        if (v.source ==
                                                fm.MapEventSource.dragEnd ||
                                            v.source ==
                                                fm.MapEventSource
                                                    .mapController) {
                                          _center = LatLng(
                                              v.camera.center.latitude,
                                              v.camera.center.longitude);

                                          var val = await geoCoding(
                                              _center.latitude,
                                              _center.longitude);
                                          if (val != '') {
                                            setState(() {
                                              dropAddressConfirmation = val;
                                            });
                                          }
                                        }
                                      },
                                      onPositionChanged: (p, l) async {
                                        if (l == false) {
                                          _center = LatLng(p.center!.latitude,
                                              p.center!.longitude);
                                          setState(() {});

                                          var val = await geoCoding(
                                              _center.latitude,
                                              _center.longitude);
                                          if (val != '') {
                                            setState(() {
                                              if (addressList
                                                  .where((element) =>
                                                      element.type == 'drop')
                                                  .isNotEmpty) {
                                                var add = addressList
                                                    .firstWhere((element) =>
                                                        element.type == 'drop');
                                                add.address = val;
                                                add.latlng = LatLng(
                                                    _center.latitude,
                                                    _center.longitude);
                                              } else {
                                                addressList.add(AddressList(
                                                    id: '1',
                                                    type: 'drop',
                                                    address: val,
                                                    latlng: LatLng(
                                                        _center.latitude,
                                                        _center.longitude),
                                                    name: userDetails['name'],
                                                    number:
                                                        userDetails['mobile']));
                                              }
                                            });
                                          }
                                        }
                                      },
                                      // ignore: deprecated_member_use
                                      interactiveFlags:
                                          ~fm.InteractiveFlag.doubleTapZoom,
                                      initialCenter: fmlt.LatLng(
                                          center.latitude, center.longitude),
                                      initialZoom: 16,
                                      onTap: (P, L) {
                                        setState(() {});
                                      }),
                                  children: [
                                    fm.TileLayer(
                                      urlTemplate:
                                          'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                      userAgentPackageName: 'com.example.app',
                                    ),
                                    const fm.RichAttributionWidget(
                                      attributions: [],
                                    ),
                                  ],
                                )
                          : (_state == '2')
                              ? locationPermissionAllTime(media)
                              : Container(),
                    ),
                    dropLocationMarker(media),
                    Positioned(
                      bottom: MediaQuery.of(context).viewInsets.bottom,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          dropMyLocation(media),
                          const SizedBox(height: 40),
                          dropLocationFooter(media),
                        ],
                      ),
                    ),
                    dropLocationSearch(media),
                    if (droplocation == true && etaDetails.isNotEmpty)
                      tripDetails(media),
                    if (_error == true) dropLocationError(media),
                    if (_locationDenied) dropLocationDenied(media),
                    if (_isLoading) const Positioned(child: Loading()),
                    if (internet == false) dropLocationNoInternet(media),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget tripDetails(Size media) {
    return Positioned(
      bottom: 0 + MediaQuery.of(context).viewInsets.bottom,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 1,
              color: page,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        width: media.width * 0.05,
                        child: Image.asset(AppAssets.cash, fit: BoxFit.contain),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        userDetails['currency_symbol'] + ' ',
                        style: GoogleFonts.notoKufiArabic(color: textColor),
                      ),
                      Text(
                        etaDetails['total'].toStringAsFixed(2),
                        style: GoogleFonts.notoKufiArabic(color: textColor),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: media.width * 0.05,
                        width: media.width * 0.05,
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(
                            shape: BoxShape.circle, color: Colors.green),
                        child: Container(
                          height: media.width * 0.025,
                          width: media.width * 0.025,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: MyText(
                          text: addressList
                              .firstWhere((element) => element.type == 'pickup')
                              .address,
                          maxLines: 2,
                          fontweight: FontWeight.w600,
                          size: media.width * twelve,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: media.width * 0.06,
                        width: media.width * 0.06,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.red.withOpacity(0.1),
                        ),
                        child: Icon(
                          Icons.location_on_outlined,
                          color: const Color(0xFFFF0000),
                          size: media.width * eighteen,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: MyText(
                          text: addressList
                              .firstWhere((element) => element.type == 'drop')
                              .address,
                          maxLines: 2,
                          fontweight: FontWeight.w600,
                          size: media.width * twelve,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Container(
                    height: media.width * 0.1,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey, width: 1.5),
                      color: page,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: TextField(
                      controller: username,
                      decoration: InputDecoration(
                        contentPadding: (languageDirection == 'rtl')
                            ? EdgeInsets.only(bottom: media.width * 0.03)
                            : EdgeInsets.only(bottom: media.width * 0.042),
                        border: InputBorder.none,
                        hintText: languages[choosenLanguage]['text_name'],
                        hintStyle: GoogleFonts.notoKufiArabic(
                          fontSize: media.width * twelve,
                          color: textColor.withOpacity(0.4),
                        ),
                      ),
                      style: GoogleFonts.notoKufiArabic(color: textColor),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: media.width * 0.1,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey, width: 1.5),
                      color: page,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: TextField(
                      controller: userphonenumber,
                      keyboardType: TextInputType.number,
                      textAlignVertical: TextAlignVertical.center,
                      decoration: InputDecoration(
                        contentPadding: (languageDirection == 'rtl')
                            ? EdgeInsets.only(bottom: media.width * 0.03)
                            : EdgeInsets.only(bottom: media.width * 0.042),
                        border: InputBorder.none,
                        hintText: languages[choosenLanguage]
                            ['text_phone_number'],
                        hintStyle: GoogleFonts.notoKufiArabic(
                          fontSize: media.width * twelve,
                          color: textColor.withOpacity(0.4),
                        ),
                      ),
                      style: GoogleFonts.notoKufiArabic(color: textColor),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: Button(
                          onTap: () => setState(() => droplocation = false),
                          text: languages[choosenLanguage]['text_cancel'],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Button(
                          onTap: () async {
                            if (username.text.isNotEmpty &&
                                userphonenumber.text.isNotEmpty) {
                              FocusManager.instance.primaryFocus?.unfocus();
                              setState(() {
                                _error = false;
                                _isLoading = true;
                              });
                              var val = await createRequest(
                                  username.text, userphonenumber.text);
                              if (val == 'success') {
                                navigate();
                              } else if (val == 'logout') {
                                navigateLogout();
                              } else {
                                _error = true;
                                setState(() {
                                  _isLoading = false;
                                });
                              }
                            }
                          },
                          text: languages[choosenLanguage]['text_ridenow'],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget locationPermissionAllTime(Size media) {
    return Container(
      height: media.height * 1,
      width: media.width * 1,
      alignment: Alignment.center,
      child: Container(
        padding: EdgeInsets.all(media.width * 0.05),
        width: media.width * 0.9,
        height: 160,
        decoration: BoxDecoration(
          color: page,
          boxShadow: [
            BoxShadow(
              blurRadius: 5,
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
            )
          ],
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languages[choosenLanguage]['text_loc_permission'],
              style: GoogleFonts.notoKufiArabic(
                fontSize: media.width * sixteen,
                color: textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () async {
                  setState(() {
                    _state = '';
                  });
                  await location.requestPermission();
                  getLocs();
                },
                child: Text(
                  languages[choosenLanguage]['text_ok'],
                  style: GoogleFonts.notoKufiArabic(
                    fontWeight: FontWeight.bold,
                    fontSize: media.width * twenty,
                    color: buttonColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget dropLocationFooter(Size media) {
    return Container(
      color: page,
      width: media.width * 1,
      padding: EdgeInsets.all(media.width * 0.05),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: media.width * 0.03,
              vertical: media.width * 0.01,
            ),
            height: media.width * 0.1,
            width: media.width * 0.9,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey,
                width: 1.5,
              ),
              borderRadius: BorderRadius.circular(media.width * 0.02),
              color: page,
            ),
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Container(
                  height: media.width * 0.04,
                  width: media.width * 0.04,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xffFF0000).withOpacity(0.3),
                  ),
                  child: Container(
                    height: media.width * 0.02,
                    width: media.width * 0.02,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xffFF0000),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: (dropAddressConfirmation == '')
                      ? Text(
                          languages[choosenLanguage]['text_pickdroplocation'],
                          style: GoogleFonts.notoKufiArabic(
                              fontSize: media.width * twelve, color: hintColor),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              width: media.width * 0.7,
                              child: Text(
                                dropAddressConfirmation,
                                style: GoogleFonts.notoSans(
                                  fontSize: media.width * twelve,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Button(
            color: buttonColor,
            textcolor: buttonText,
            onTap: () async {
              if (dropAddressConfirmation != '') {
                //remove in envato
                if (addressList
                    .where((element) => element.type == 'drop')
                    .isEmpty) {
                  addressList.add(AddressList(
                      id: (addressList.length + 1).toString(),
                      type: 'drop',
                      address: dropAddressConfirmation,
                      latlng: centerLocation));
                } else {
                  addressList
                      .firstWhere((element) => element.type == 'drop')
                      .address = dropAddressConfirmation;
                  addressList
                      .firstWhere((element) => element.type == 'drop')
                      .latlng = centerLocation;
                }
                if (addressList.length == 2) {
                  setState(() {
                    _isLoading = true;
                  });
                  var val = await etaRequest();
                  if (val == 'logout') {
                    navigateLogout();
                  }
                  setState(() {
                    _isLoading = false;
                    droplocation = true;
                  });
                }
              }
            },
            text: languages[choosenLanguage]['text_confirm'],
          )
        ],
      ),
    );
  }

  Widget dropMyLocation(Size media) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: InkWell(
        onTap: () async {
          if (locationAllowed == true) {
            if (mapType == 'google') {
              _controller
                  ?.animateCamera(CameraUpdate.newLatLngZoom(center, 18.0));
            } else {
              _fmController.move(
                  fmlt.LatLng(center.latitude, center.longitude), 14);
            }
          } else {
            if (serviceEnabled == true) {
              setState(() {
                _locationDenied = true;
              });
            } else {
              await location.requestService();
              if (await geolocs.GeolocatorPlatform.instance
                  .isLocationServiceEnabled()) {
                setState(() {
                  _locationDenied = true;
                });
              }
            }
          }
        },
        child: Container(
          height: media.width * 0.1,
          width: media.width * 0.1,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                blurRadius: 2,
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 2,
              )
            ],
            color: page,
            borderRadius: BorderRadius.circular(media.width * 0.02),
          ),
          child: Icon(Icons.my_location_sharp, color: textColor),
        ),
      ),
    );
  }

  Widget dropLocationSearch(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        padding: EdgeInsets.fromLTRB(
          media.width * 0.05,
          12.5,
          media.width * 0.05,
          0,
        ),
        width: media.width * 1,
        height: (addAutoFill.isNotEmpty) ? media.height * 1 : null,
        color: (addAutoFill.isEmpty) ? Colors.transparent : page,
        child: Column(
          children: [
            dropLocationAppBar(media),
            const SizedBox(height: 20),
            if (addAutoFill.isNotEmpty) addressesAutoFillData(media),
          ],
        ),
      ),
    );
  }

  Widget dropLocationDenied(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () => setState(() => _locationDenied = false),
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2.0,
                    spreadRadius: 2.0,
                    color: Colors.black.withOpacity(0.2),
                  )
                ],
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: media.width * 0.8,
                    child: Text(
                      languages[choosenLanguage]['text_open_loc_settings'],
                      style: GoogleFonts.notoKufiArabic(
                        fontSize: media.width * sixteen,
                        color: textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(height: media.width * 0.05),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () async => await perm.openAppSettings(),
                        child: Text(
                          languages[choosenLanguage]['text_open_settings'],
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: media.width * sixteen,
                            color: buttonColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          setState(() {
                            _locationDenied = false;
                            _isLoading = true;
                          });
                          getLocs();
                        },
                        child: Text(
                          languages[choosenLanguage]['text_done'],
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: media.width * sixteen,
                            color: buttonColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget addressesAutoFillData(Size media) {
    return Container(
      height: media.height * 0.45,
      padding: EdgeInsets.all(media.width * 0.02),
      width: media.width * 0.9,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(media.width * 0.05),
        color: page,
      ),
      child: SingleChildScrollView(
        child: Column(
          children: addAutoFill
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    (i < 7)
                        ? Container(
                            padding: EdgeInsets.symmetric(
                              vertical: media.width * 0.04,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () async {
                                    // ignore: prefer_typing_uninitialized_variables
                                    var val;
                                    // if (mapType ==
                                    //     'google') {
                                    val = await geoCodingForLatLng(
                                        addAutoFill[i]['fullText']);
                                    // }
                                    setState(() {
                                      _center = (addAutoFill[i]
                                                  ['description'] !=
                                              null)
                                          ? val
                                          : LatLng(
                                              double.parse(addAutoFill[i]['lat']
                                                  .toString()),
                                              double.parse(addAutoFill[i]['lon']
                                                  .toString()));
                                      dropAddressConfirmation = (addAutoFill[i]
                                                  ['description'] !=
                                              null)
                                          ? addAutoFill[i]['description']
                                          : addAutoFill[i]['display_name'];
                                      if (mapType == 'google') {
                                        _controller?.moveCamera(
                                            CameraUpdate.newLatLngZoom(
                                                _center, 14.0));
                                      } else {
                                        _fmController.move(
                                            fmlt.LatLng(_center.latitude,
                                                center.longitude),
                                            14);
                                      }
                                    });
                                    addAutoFill.clear();
                                    FocusManager.instance.primaryFocus
                                        ?.unfocus();
                                  },
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      (addAutoFill[i]['description'] != null)
                                          ? addAutoFill[i]['description']
                                          : addAutoFill[i]['display_name'],
                                      style: GoogleFonts.notoSans(
                                        fontSize: media.width * twelve,
                                        color: textColor,
                                      ),
                                      maxLines: 2,
                                    ),
                                  ),
                                ),
                                Container(
                                  height: media.width * 0.1,
                                  width: media.width * 0.1,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.grey[200],
                                  ),
                                  child: const Icon(Icons.access_time),
                                ),
                              ],
                            ),
                          )
                        : Container(),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget dropLocationAppBar(Size media) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Container(
            height: media.width * 0.1,
            width: media.width * 0.1,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 2,
                )
              ],
              color: page,
            ),
            alignment: Alignment.center,
            child: Icon(Icons.arrow_back, color: textColor),
          ),
        ),
        Container(
          height: media.width * 0.1,
          width: media.width * 0.75,
          padding: EdgeInsets.symmetric(horizontal: media.width * 0.05),
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 2,
                blurRadius: 2,
              )
            ],
            color: page,
            borderRadius: BorderRadius.circular(media.width * 0.05),
          ),
          child: TextField(
              controller: search,
              autofocus: true,
              decoration: InputDecoration(
                contentPadding: (languageDirection == 'rtl')
                    ? EdgeInsets.only(bottom: media.width * 0.03)
                    : EdgeInsets.only(bottom: media.width * 0.042),
                border: InputBorder.none,
                hintText: languages[choosenLanguage]
                    ['text_4lettersforautofill'],
                hintStyle: GoogleFonts.notoKufiArabic(
                  fontSize: media.width * twelve,
                  color: textColor.withOpacity(0.4),
                ),
              ),
              style: GoogleFonts.notoKufiArabic(color: textColor),
              maxLines: 1,
              onChanged: (val) {
                _debouncer.run(() {
                  if (val.length >= 3) {
                    if (storedAutoAddress
                        .where((element) => element['description']
                            .toString()
                            .toLowerCase()
                            .contains(val.toLowerCase()))
                        .isNotEmpty) {
                      addAutoFill.removeWhere((element) =>
                          element['description']
                              .toString()
                              .toLowerCase()
                              .contains(val.toLowerCase()) ==
                          false);

                      storedAutoAddress
                          .where((element) => element['description']
                              .toString()
                              .toLowerCase()
                              .contains(val.toLowerCase()))
                          .forEach((element) {
                        addAutoFill.add(element);
                      });
                      valueNotifierHome.incrementNotifier();
                    } else {
                      getAutocomplete(val, sessionToken, _center.latitude,
                          _center.longitude);
                    }
                  } else if (val.isEmpty) {
                    setState(() {
                      addAutoFill.clear();
                    });
                  }
                });
              }),
        )
      ],
    );
  }

  Widget dropLocationMarker(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        alignment: Alignment.center,
        child: Column(
          children: [
            SizedBox(
              height: (media.height / 2) - media.width * 0.08,
            ),
            Image.asset(
              AppAssets.dropMarker,
              width: media.width * 0.07,
              height: media.width * 0.08,
            ),
          ],
        ),
      ),
    );
  }

  // Widget addFavoriteAddress(Size media) {
  //   return Positioned(
  //     top: 0,
  //     child: Container(
  //       height: media.height * 1,
  //       width: media.width * 1,
  //       color: Colors.transparent.withOpacity(0.6),
  //       child: Column(
  //         mainAxisAlignment: MainAxisAlignment.center,
  //         children: [
  //           CustomCloseButton(
  //             media: media,
  //             onTap: () {
  //               setState(
  //                 () {
  //                   favName = '';
  //                   favAddressAdd = false;
  //                 },
  //               );
  //             },
  //           ),
  //           Container(
  //             padding: EdgeInsets.all(media.width * 0.05),
  //             width: media.width * 0.9,
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(12),
  //               color: page,
  //             ),
  //             child: Column(
  //               children: [
  //                 Text(
  //                   languages[choosenLanguage]['text_saveaddressas'],
  //                   style: GoogleFonts.notoKufiArabic(
  //                     fontSize: media.width * sixteen,
  //                     color: textColor,
  //                     fontWeight: FontWeight.w600,
  //                   ),
  //                 ),
  //                 const SizedBox(height: 12),
  //                 Text(
  //                   favSelectedAddress,
  //                   style: GoogleFonts.notoKufiArabic(
  //                     fontSize: media.width * twelve,
  //                     color: textColor,
  //                   ),
  //                 ),
  //                 const SizedBox(height: 12),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     InkWell(
  //                       onTap: () {
  //                         FocusManager.instance.primaryFocus?.unfocus();
  //                         setState(() => favName = 'Home');
  //                       },
  //                       child: Container(
  //                         padding: EdgeInsets.all(media.width * 0.01),
  //                         child: Row(
  //                           children: [
  //                             Container(
  //                               height: media.width * 0.04,
  //                               width: media.width * 0.04,
  //                               decoration: BoxDecoration(
  //                                 shape: BoxShape.circle,
  //                                 border: Border.all(
  //                                   color: isDarkTheme
  //                                       ? Colors.white
  //                                       : const Color(0xff222222),
  //                                   width: 1.2,
  //                                 ),
  //                               ),
  //                               alignment: Alignment.center,
  //                               child: (favName == 'Home')
  //                                   ? Container(
  //                                       height: media.width * 0.025,
  //                                       width: media.width * 0.025,
  //                                       decoration: BoxDecoration(
  //                                         shape: BoxShape.circle,
  //                                         color: isDarkTheme
  //                                             ? Colors.white
  //                                             : const Color(0xff222222),
  //                                       ),
  //                                     )
  //                                   : Container(),
  //                             ),
  //                             const SizedBox(width: 4),
  //                             Text(
  //                               languages[choosenLanguage]['text_home'],
  //                               style: GoogleFonts.notoKufiArabic(
  //                                 color: textColor,
  //                               ),
  //                             )
  //                           ],
  //                         ),
  //                       ),
  //                     ),
  //                     InkWell(
  //                       onTap: () {
  //                         FocusManager.instance.primaryFocus?.unfocus();
  //                         setState(() => favName = 'Work');
  //                       },
  //                       child: Container(
  //                         padding: EdgeInsets.all(media.width * 0.01),
  //                         child: Row(
  //                           children: [
  //                             Container(
  //                               height: media.width * 0.04,
  //                               width: media.width * 0.04,
  //                               decoration: BoxDecoration(
  //                                 shape: BoxShape.circle,
  //                                 border: Border.all(
  //                                   color: isDarkTheme
  //                                       ? Colors.white
  //                                       : const Color(0xff222222),
  //                                   width: 1.2,
  //                                 ),
  //                               ),
  //                               alignment: Alignment.center,
  //                               child: (favName == 'Work')
  //                                   ? Container(
  //                                       height: media.width * 0.025,
  //                                       width: media.width * 0.025,
  //                                       decoration: BoxDecoration(
  //                                         shape: BoxShape.circle,
  //                                         color: isDarkTheme
  //                                             ? Colors.white
  //                                             : const Color(0xff222222),
  //                                       ),
  //                                     )
  //                                   : Container(),
  //                             ),
  //                             const SizedBox(width: 4),
  //                             Text(
  //                               languages[choosenLanguage]['text_work'],
  //                               style: GoogleFonts.notoKufiArabic(
  //                                 color: textColor,
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ),
  //                     InkWell(
  //                       onTap: () {
  //                         FocusManager.instance.primaryFocus?.unfocus();
  //                         setState(() => favName = 'Others');
  //                       },
  //                       child: Container(
  //                         padding: EdgeInsets.all(media.width * 0.01),
  //                         child: Row(
  //                           children: [
  //                             Container(
  //                               height: media.width * 0.04,
  //                               width: media.width * 0.04,
  //                               decoration: BoxDecoration(
  //                                 shape: BoxShape.circle,
  //                                 border: Border.all(
  //                                   color: isDarkTheme
  //                                       ? Colors.white
  //                                       : const Color(0xff222222),
  //                                   width: 1.2,
  //                                 ),
  //                               ),
  //                               alignment: Alignment.center,
  //                               child: (favName == 'Others')
  //                                   ? Container(
  //                                       height: media.width * 0.025,
  //                                       width: media.width * 0.025,
  //                                       decoration: BoxDecoration(
  //                                         shape: BoxShape.circle,
  //                                         color: isDarkTheme
  //                                             ? Colors.white
  //                                             : const Color(0xff222222),
  //                                       ),
  //                                     )
  //                                   : Container(),
  //                             ),
  //                             const SizedBox(width: 4),
  //                             Text(
  //                               languages[choosenLanguage]['text_others'],
  //                               style: GoogleFonts.notoKufiArabic(
  //                                 color: textColor,
  //                               ),
  //                             )
  //                           ],
  //                         ),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 const SizedBox(height: 8),
  //                 if (favName == 'Others')
  //                   Container(
  //                     padding: EdgeInsets.all(media.width * 0.025),
  //                     decoration: BoxDecoration(
  //                       borderRadius: BorderRadius.circular(12),
  //                       border: Border.all(color: borderLines, width: 1.2),
  //                     ),
  //                     child: TextField(
  //                       decoration: InputDecoration(
  //                         border: InputBorder.none,
  //                         hintText: languages[choosenLanguage]
  //                             ['text_enterfavname'],
  //                         hintStyle: GoogleFonts.notoSans(
  //                           fontSize: media.width * twelve,
  //                           color: hintColor,
  //                         ),
  //                       ),
  //                       style: GoogleFonts.notoSans(
  //                         fontSize: media.width * sixteen,
  //                         color: textColor,
  //                       ),
  //                       maxLines: 1,
  //                       onChanged: (val) => setState(() => favNameText = val),
  //                     ),
  //                   ),
  //                 const SizedBox(height: 20),
  //                 Button(
  //                   color: buttonColor,
  //                   textcolor: buttonText,
  //                   onTap: () async {
  //                     if (favName == 'Others' && favNameText != '') {
  //                       setState(() => _isLoading = true);
  //                       var val = await addFavLocation(
  //                         favLat,
  //                         favLng,
  //                         favSelectedAddress,
  //                         favNameText,
  //                       );
  //                       setState(() {
  //                         _isLoading = false;
  //                         if (val == true) {
  //                           favLat = '';
  //                           favLng = '';
  //                           favSelectedAddress = '';
  //                           favNameText = '';
  //                           favName = 'Home';
  //                           favAddressAdd = false;
  //                         } else if (val == 'logout') {
  //                           navigateLogout();
  //                         }
  //                       });
  //                     } else if (favName == 'Home' || favName == 'Work') {
  //                       setState(() => _isLoading = true);
  //                       var val = await addFavLocation(
  //                           favLat, favLng, favSelectedAddress, favName);
  //                       setState(() {
  //                         _isLoading = false;
  //                         if (val == true) {
  //                           favLat = '';
  //                           favLng = '';
  //                           favSelectedAddress = '';
  //                           favNameText = '';
  //                           favName = 'Home';
  //                           favAddressAdd = false;
  //                         } else if (val == 'logout') {
  //                           navigateLogout();
  //                         }
  //                       });
  //                     }
  //                   },
  //                   text: languages[choosenLanguage]['text_confirm'],
  //                 )
  //               ],
  //             ),
  //           )
  //         ],
  //       ),
  //     ),
  //   );
  // }

  Widget dropLocationNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }

  Widget dropLocationError(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  Text(
                    languages[choosenLanguage]['text_somethingwentwrong'],
                    textAlign: TextAlign.center,
                    style: GoogleFonts.notoKufiArabic(
                      fontSize: media.width * sixteen,
                      color: textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Button(
                    onTap: () async => setState(() => _error = false),
                    text: languages[choosenLanguage]['text_ok'],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class Debouncer {
  final int milliseconds;
  dynamic action;
  dynamic _timer;

  Debouncer({required this.milliseconds});

  run(VoidCallback action) {
    if (null != _timer) {
      _timer.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}
