import 'package:flutter/material.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/landing_view.dart';
import 'review_view.dart';

class InvoiceView extends StatefulWidget {
  const InvoiceView({Key? key}) : super(key: key);

  @override
  State<InvoiceView> createState() => _InvoiceViewState();
}

int payby = 0;

class _InvoiceViewState extends State<InvoiceView> {
  String _error = '';
  bool _isLoading = false;

  @override
  void initState() {
    if (driverReq['is_paid'] == 0) {
      payby = 0;
    }
    super.initState();
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(
        const Duration(seconds: 2),
        () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false,
          );
        },
      );
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(
        const Duration(seconds: 2),
        () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false,
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: ValueListenableBuilder(
            valueListenable: valueNotifierHome.value,
            builder: (context, value, child) {
              return Stack(
                children: [
                  if (driverReq.isNotEmpty)
                    Container(
                      padding: EdgeInsets.all(media.width * 0.05),
                      height: media.height * 1,
                      width: media.width * 1,
                      color: page,
                      child: (driverReq.isNotEmpty)
                          ? Column(
                              children: [
                                Expanded(
                                  child: SingleChildScrollView(
                                    physics: const BouncingScrollPhysics(),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        invoiceAppBar(media),
                                        const SizedBox(height: 20),
                                        driverDetails(media),
                                        const SizedBox(height: 16),
                                        tripDetails(media),
                                        const SizedBox(height: 24),
                                        tripAddressesDetails(media),
                                        const SizedBox(height: 24),
                                        if (driverReq['requestBill'] != null)
                                          fareDetails(media),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                if (_error != '') invoiceError(media),
                                const SizedBox(height: 4),
                                invoiceSummary(media),
                              ],
                            )
                          : Container(),
                    ),
                  if (_isLoading == true) const Positioned(child: Loading())
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget invoiceError(Size media) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        _error,
        style: GoogleFonts.notoKufiArabic(
          fontSize: media.width * fourteen,
          color: Colors.red,
        ),
        maxLines: 1,
      ),
    );
  }

  Widget tripAddressesDetails(Size media) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              height: media.width * 0.05,
              width: media.width * 0.05,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.green,
              ),
              child: Container(
                height: media.width * 0.025,
                width: media.width * 0.025,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: MyText(
                text: driverReq['pick_address'],
                size: media.width * twelve,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Column(
          children: tripStops
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    (i < tripStops.length - 1)
                        ? Container(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                Container(
                                  height: media.width * 0.05,
                                  width: media.width * 0.05,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.red.withOpacity(0.4),
                                  ),
                                  child: MyText(
                                    text: (i + 1).toString(),
                                    color: const Color(0xFFFF0000),
                                    fontweight: FontWeight.w600,
                                    size: media.width * twelve,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: MyText(
                                    text: tripStops[i]['address'],
                                    size: media.width * twelve,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Container(),
                  );
                },
              )
              .values
              .toList(),
        ),
        Row(
          children: [
            Container(
              height: media.width * 0.05,
              width: media.width * 0.05,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withOpacity(0.4),
              ),
              child: Icon(
                Icons.location_on,
                size: media.width * 0.03,
                color: const Color(0xFFFF0000),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: MyText(
                text: driverReq['drop_address'],
                size: media.width * twelve,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget invoiceAppBar(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_tripsummary'],
      size: media.width * sixteen,
      fontweight: FontWeight.bold,
    );
  }

  Widget driverDetails(Size media) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: media.width * 0.13,
          width: media.width * 0.13,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: NetworkImage(
                driverReq['userDetail']['data']['profile_picture'],
              ),
              fit: BoxFit.cover,
            ),
          ),
        ),
        const SizedBox(width: 16),
        MyText(
          text: driverReq['userDetail']['data']['name'],
          size: media.width * eighteen,
        )
      ],
    );
  }

  Widget tripDetails(Size media) {
    return Container(
      padding: EdgeInsets.all(media.width * 0.04),
      decoration: BoxDecoration(color: Colors.grey.withOpacity(0.1)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_reference'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: driverReq['request_number'],
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_rideType'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: (driverReq['is_rental'] == false)
                        ? languages[choosenLanguage]['text_regular']
                        : languages[choosenLanguage]['text_rental'],
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_distance'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: driverReq['total_distance'] + ' ' + driverReq['unit'],
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_duration'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: '${driverReq['total_time']} mins',
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget fareDetails(Size media) {
    return Column(
      children: [
        MyText(
          text: languages[choosenLanguage]['text_tripfare'],
          size: media.width * fourteen,
          fontweight: FontWeight.w700,
        ),
        const SizedBox(height: 16),
        if (driverReq['is_rental'] == true)
          Container(
            padding: EdgeInsets.only(bottom: media.width * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  languages[choosenLanguage]['text_ride_type'],
                  style: GoogleFonts.notoSans(
                      fontSize: media.width * fourteen, color: textColor),
                ),
                Text(
                  driverReq['rental_package_name'],
                  style: GoogleFonts.notoSans(
                      fontSize: media.width * fourteen, color: textColor),
                ),
              ],
            ),
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_baseprice'],
          (driverReq.isNotEmpty && driverReq['requestBill'] != null)
              ? driverReq['requestBill']['data']['base_price'].toString()
              : '',
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_distprice'],
          (driverReq.isNotEmpty && driverReq['requestBill'] != null)
              ? driverReq['requestBill']['data']['distance_price'].toString()
              : '',
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_timeprice'],
          (driverReq.isNotEmpty && driverReq['requestBill'] != null)
              ? driverReq['requestBill']['data']['time_price'].toString()
              : '',
        ),
        fareDetailsDivider(media),
        if (driverReq['requestBill'] != null &&
            driverReq['requestBill']['data']['cancellation_fee'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_cancelfee'],
                (driverReq.isNotEmpty && driverReq['requestBill'] != null)
                    ? driverReq['requestBill']['data']['cancellation_fee']
                        .toString()
                    : '',
              ),
              fareDetailsDivider(media),
            ],
          ),
        if (driverReq['requestBill'] != null &&
            driverReq['requestBill']['data']['airport_surge_fee'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_surge_fee'],
                (driverReq.isNotEmpty && driverReq['requestBill'] != null)
                    ? driverReq['requestBill']['data']['airport_surge_fee']
                        .toString()
                    : '',
              ),
              fareDetailsDivider(media),
            ],
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_waiting_price'] +
              ' (' +
              driverReq['requestBill']['data']['requested_currency_symbol'] +
              ' ' +
              driverReq['requestBill']['data']['waiting_charge_per_min']
                  .toString() +
              ' x ' +
              driverReq['requestBill']['data']['calculated_waiting_time']
                  .toString() +
              ' mins' +
              ')',
          driverReq['requestBill']['data']['waiting_charge'].toString(),
        ),
        fareDetailsDivider(media),
        if (driverReq['requestBill'] != null &&
            driverReq['requestBill']['data']['admin_commision'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_convfee'],
                driverReq['requestBill']['data']['admin_commision'].toString(),
              ),
              fareDetailsDivider(media),
            ],
          ),
        if (driverReq['requestBill'] != null &&
            driverReq['requestBill']['data']['promo_discount'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_discount'],
                driverReq['requestBill']['data']['promo_discount'].toString(),
                true,
              ),
              fareDetailsDivider(media),
            ],
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_taxes'],
          driverReq['requestBill']['data']['service_tax'].toString(),
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_totalfare'],
          driverReq['requestBill']['data']['total_amount'].toString(),
        ),
      ],
    );
  }

  Widget fareDetailsItem(Size media, String text, String amount,
      [bool isRed = false]) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        MyText(
          text: text,
          size: media.width * twelve,
          color: isRed ? Colors.red : null,
        ),
        MyText(
          text: driverReq['requestBill']['data']['requested_currency_symbol'] +
              ' ' +
              amount,
          size: media.width * twelve,
          color: isRed ? Colors.red : null,
        ),
      ],
    );
  }

  Widget fareDetailsDivider(Size media) {
    return Container(
      margin:
          EdgeInsets.only(top: media.width * 0.03, bottom: media.width * 0.03),
      height: 1.5,
      color: const Color(0xffE0E0E0),
    );
  }

  Widget invoiceSummary(Size media) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          (driverReq['payment_opt'] == '1')
              ? languages[choosenLanguage]['text_cash']
              : (driverReq['payment_opt'] == '2')
                  ? languages[choosenLanguage]['text_wallet']
                  : languages[choosenLanguage]['text_card'],
          style: GoogleFonts.notoKufiArabic(
            fontSize: media.width * sixteen,
            color: buttonColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 2),
        MyText(
          text:
              ' ${driverReq['requestBill']['data']['requested_currency_symbol']}',
          size: media.width * fourteen,
        ),
        MyText(
          text: ' ${driverReq['requestBill']['data']['total_amount']}',
          size: media.width * twenty,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
        if (driverReq['payment_opt'] == '0' && driverReq['is_paid'] == 0)
          Container(
            height: media.width * 0.12,
            padding: EdgeInsets.only(
              left: media.width * 0.03,
              right: media.width * 0.03,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(media.width * 0.08),
              color: borderLines,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  languages[choosenLanguage]['text_waitingforpayment'],
                  style: GoogleFonts.notoKufiArabic(
                      fontSize: media.width * fourteen,
                      fontWeight: FontWeight.w600),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
                SizedBox(width: media.width * 0.02),
                SizedBox(
                  height: media.width * 0.05,
                  width: media.width * 0.05,
                  child: const CircularProgressIndicator(),
                )
              ],
            ),
          )
        else
          Button(
            color: buttonColor,
            textcolor: buttonText,
            onTap: () async {
              if (driverReq['is_paid'] == 0) {
                setState(() {
                  _error = '';
                  _isLoading = true;
                });
                var val = await paymentReceived();
                if (val == 'logout') {
                  navigateLogout();
                } else if (val == 'success') {
                  setState(() => _isLoading = false);
                } else {
                  setState(() {
                    _isLoading = false;
                    _error = val.toString();
                  });
                }
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const ReviewView()),
                );
              }
            },
            text: (driverReq['is_paid'] == 0)
                ? languages[choosenLanguage]['text_payment_received']
                : languages[choosenLanguage]['text_confirm'],
          )
      ],
    );
  }
}
