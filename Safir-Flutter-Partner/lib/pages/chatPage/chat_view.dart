import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';

class ChatView extends StatefulWidget {
  const ChatView({Key? key}) : super(key: key);

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<ChatView> {
  //controller for chat text
  TextEditingController chatText = TextEditingController();

  //controller for scrolling chats
  ScrollController controller = ScrollController();
  bool _sendingMessage = false;

  @override
  void initState() {
    getMessages();
    super.initState();
  }

  getMessages() async {
    var val = await getCurrentMessages();
    if (val == 'logout') {
      navigateLogout();
    }
  }

  navigateLogout() {
    if (ownermodule == '1') {
      Future.delayed(
        const Duration(seconds: 2),
        () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LandingView()),
            (route) => false,
          );
        },
      );
    } else {
      ischeckownerordriver = 'driver';
      Future.delayed(
        const Duration(seconds: 2),
        () {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const LoginView()),
            (route) => false,
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      child: SafeArea(
        child: Material(
          child: Scaffold(
            body: ValueListenableBuilder(
              valueListenable: valueNotifierHome.value,
              builder: (context, value, child) {
                WidgetsBinding.instance.addPostFrameCallback(
                  (_) {
                    controller.animateTo(
                      controller.position.maxScrollExtent,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.ease,
                    );
                  },
                );
                //call for message seen
                // messageSeen(); // TODO
                return Directionality(
                  textDirection: (languageDirection == 'rtl')
                      ? TextDirection.rtl
                      : TextDirection.ltr,
                  child: Stack(
                    children: [
                      Container(
                        padding: EdgeInsets.all(media.width * 0.05),
                        height: media.height * 1,
                        width: media.width * 1,
                        color: page,
                        child: Column(
                          children: [
                            chatAppBar(media),
                            const SizedBox(height: 20),
                            chatMessages(media),
                            chatTextField(media),
                          ],
                        ),
                      ),
                      if (_sendingMessage) const Positioned(child: Loading())
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget chatAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            Navigator.pop(context, true);
          },
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: driverReq['userDetail']['data']['name'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget chatMessages(Size media) {
    return Expanded(
      child: SingleChildScrollView(
        controller: controller,
        child: Column(
          children: chatList
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    Container(
                      padding: const EdgeInsets.only(top: 8),
                      width: media.width * 0.9,
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: (chatList[i]['from_type'] == 2)
                                ? MainAxisAlignment.start
                                : MainAxisAlignment.end,
                            children: [
                              Card(
                                elevation: 5,
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxWidth: media.width * 0.65,
                                  ),
                                  padding: EdgeInsets.all(media.width * 0.03),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topLeft: chatList[i]['from_type'] == 2
                                          ? Radius.circular(media.width * 0.02)
                                          : const Radius.circular(0),
                                      topRight: chatList[i]['from_type'] == 2
                                          ? const Radius.circular(0)
                                          : Radius.circular(media.width * 0.02),
                                      bottomRight:
                                          Radius.circular(media.width * 0.02),
                                      bottomLeft:
                                          Radius.circular(media.width * 0.02),
                                    ),
                                    color: (chatList[i]['from_type'] == 2)
                                        ? buttonColor
                                        : const Color(0xffE7EDEF),
                                  ),
                                  child: MyText(
                                    text: chatList[i]['message'],
                                    size: media.width * fourteen,
                                    color: (chatList[i]['from_type'] == 2)
                                        ? textColor
                                        : Colors.black,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: (chatList[i]['from_type'] == 2)
                                ? MainAxisAlignment.start
                                : MainAxisAlignment.end,
                            children: [
                              MyText(
                                text: chatList[i]['converted_created_at'],
                                size: media.width * twelve,
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget chatTextField(Size media) {
    return Container(
      margin: EdgeInsets.only(top: media.width * 0.025),
      padding: EdgeInsets.symmetric(
        horizontal: media.width * 0.025,
        vertical: media.width * 0.01,
      ),
      width: media.width * 0.9,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: borderLines, width: 1.2),
        color: page,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: TextField(
              controller: chatText,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: languages[choosenLanguage]['text_entermessage'],
                hintStyle: GoogleFonts.notoKufiArabic(
                  color: textColor.withOpacity(0.4),
                  fontSize: media.width * twelve,
                ),
              ),
              style: GoogleFonts.notoKufiArabic(color: textColor),
              minLines: 1,
              maxLines: 4,
              onChanged: (val) {},
            ),
          ),
          InkWell(
            onTap: () async {
              FocusManager.instance.primaryFocus?.unfocus();
              setState(() => _sendingMessage = true);
              var val = await sendMessage(chatText.text);
              if (val == 'logout') {
                navigateLogout();
              }
              chatText.clear();
              setState(() => _sendingMessage = false);
            },
            child: Image.asset(
              AppAssets.send,
              fit: BoxFit.contain,
              width: media.width * 0.075,
              color: textColor,
            ),
          )
        ],
      ),
    );
  }
}
