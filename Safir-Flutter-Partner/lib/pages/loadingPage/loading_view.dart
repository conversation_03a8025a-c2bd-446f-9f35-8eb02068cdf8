import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:safir_driver/Core/utils/app_assets.dart';
import 'package:safir_driver/pages/language/languages_view.dart';
import 'package:safir_driver/pages/login/landing_view.dart';
import 'package:safir_driver/pages/login/login_view.dart';
import 'package:safir_driver/pages/onTripPage/map_page.dart';
import 'package:safir_driver/translation/translation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../styles/styles.dart';
import '../../functions/functions.dart';
import 'package:http/http.dart' as http;
import '../../widgets/widgets.dart';
import '../login/required_information_view.dart';
import '../noInternet/nointernet.dart';
import 'loading.dart';

class LoadingView extends StatefulWidget {
  const LoadingView({super.key});

  @override
  State<LoadingView> createState() => _LoadingViewState();
}

dynamic package;

class _LoadingViewState extends State<LoadingView> {
  String dot = '.';
  bool updateAvailable = false;
  dynamic _package;
  dynamic _version;
  bool _error = false;
  bool _isLoading = false;

  var demopage = TextEditingController();

  //navigate
  navigate() {
    if (userDetails['uploaded_document'] == true &&
        userDetails['approve'] == true) {
      //status approved

      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const Maps()),
          (route) => false);
    } else {
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (context) => const RequiredInformationView()));
    }
  }

  @override
  void initState() {
    getLanguageDone();
    getOwnermodule();
    super.initState();
  }

  getData() async {
    for (var i = 0; _error == true; i++) {
      await getLanguageDone();
    }
  }

//get language json and data saved in local (bearer token , choosen language) and find users current status
  getLanguageDone() async {
    _package = await PackageInfo.fromPlatform();
    try {
      if (platform == TargetPlatform.android) {
        _version = await FirebaseDatabase.instance
            .ref()
            .child('driver_android_version')
            .get();
      } else {
        _version = await FirebaseDatabase.instance
            .ref()
            .child('driver_ios_version')
            .get();
      }
      _error = false;
      if (_version.value != null) {
        var version = _version.value.toString().split('.');
        var package = _package.version.toString().split('.');

        for (var i = 0; i < version.length || i < package.length; i++) {
          if (i < version.length && i < package.length) {
            if (int.parse(package[i]) < int.parse(version[i])) {
              setState(() {
                updateAvailable = true;
              });
              break;
            } else if (int.parse(package[i]) > int.parse(version[i])) {
              setState(() {
                updateAvailable = false;
              });
              break;
            }
          } else if (i >= version.length && i < package.length) {
            setState(() {
              updateAvailable = false;
            });
            break;
          } else if (i < version.length && i >= package.length) {
            setState(() {
              updateAvailable = true;
            });
            break;
          }
        }
      }

      if (updateAvailable == false) {
        await getDetailsOfDevice();
        if (internet == true) {
          var val = await getLocalData();

          //if user is login and check waiting for approval status and send accordingly
          if (val == '3') {
            navigate();
          } else if (choosenLanguage == '') {
            // ignore: use_build_context_synchronously
            Navigator.pushReplacement(context,
                MaterialPageRoute(builder: (context) => const LanguagesView()));
          } else if (val == '2') {
            Future.delayed(const Duration(seconds: 2), () {
              if (ownermodule == '1') {
                Future.delayed(const Duration(seconds: 2), () {
                  Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const LandingView()));
                });
              } else {
                ischeckownerordriver = 'driver';
                Future.delayed(const Duration(seconds: 2), () {
                  Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const LoginView()));
                });
              }
            });
          } else {
            Future.delayed(const Duration(seconds: 2), () {
              //choose language page
              Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const LanguagesView()));
            });
          }
          setState(() {});
        }
      }
    } catch (e) {
      if (internet == true) {
        if (_error == false) {
          setState(() {
            _error = true;
          });
          getData();
        }
      } else {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;

    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          body: Stack(
            children: [
              loadingImage(media),
              if (updateAvailable == true) updateAvailablePopup(media),
              if (internet == false) loadingNoInternet(media),
              if (_isLoading == true && internet == true)
                const Positioned(top: 0, child: Loading()),
            ],
          ),
        ),
      ),
    );
  }

  Widget loadingImage(Size media) {
    return Container(
      color: page,
      height: media.height * 1,
      width: media.width * 1,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(media.width * 0.01),
            width: media.width * 0.74,
            height: media.width * 0.84,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AppAssets.logoWithoutBackground),
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget updateAvailablePopup(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: media.width * 0.9,
              padding: EdgeInsets.all(media.width * 0.05),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: media.width * 0.8,
                    child: MyText(
                      text: languages[choosenLanguage]
                              ?['text_update_available'] ??
                          'الإصدار الجديد من هذا التطبيق متاح الآن في المتجر، يرجى تحديث التطبيق للاستمرار في الاستخدام',
                      size: media.width * sixteen,
                      fontweight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Button(
                    onTap: () async {
                      if (platform == TargetPlatform.android) {
                        openBrowser(
                          'https://play.google.com/store/apps/details?id=${_package.packageName}',
                        );
                      } else {
                        setState(() => _isLoading = true);
                        var response = await http.get(
                          Uri.parse(
                              'http://itunes.apple.com/lookup?bundleId=${_package.packageName}'),
                        );
                        if (response.statusCode == 200) {
                          openBrowser(
                            jsonDecode(response.body)['results'][0]
                                ['trackViewUrl'],
                          );
                        }
                        setState(() => _isLoading = false);
                      }
                    },
                    text: languages[choosenLanguage]?['text_update'] ?? 'تحديث',
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget loadingNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() {
          internetTrue();
          getLanguageDone();
        }),
      ),
    );
  }
}
