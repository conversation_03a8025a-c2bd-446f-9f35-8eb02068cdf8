import 'package:flutter/material.dart';
import 'package:safir_driver/pages/onTripPage/map_page.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translation/translation.dart';
import '../../widgets/widgets.dart';
import '../noInternet/nointernet.dart';
import 'upload_docs_view.dart';

class DocsOnProcessView extends StatefulWidget {
  const DocsOnProcessView({Key? key}) : super(key: key);

  @override
  State<DocsOnProcessView> createState() => _DocsOnProcessViewState();
}

class _DocsOnProcessViewState extends State<DocsOnProcessView> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: ValueListenableBuilder(
            valueListenable: valueNotifierHome.value,
            builder: (context, value, child) {
              if (userDetails['approve'] == true) {
                Future.delayed(const Duration(milliseconds: 0), () {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(builder: (context) => const Maps()),
                    (route) => false,
                  );
                });
              }
              return Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    height: media.height * 1,
                    width: media.width * 1,
                    color: page,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        (userDetails['approve'] == false &&
                                userDetails['declined_reason'] == null)
                            ? approvalWaiting(media)
                            : (userDetails['approve'] == false &&
                                    userDetails['declined_reason'] != null)
                                ? accountBlocked(media)
                                : Container(),
                        if (userDetails['declined_reason'].toString() !=
                            'profile-info-updated')
                          editDocsButton(media),
                      ],
                    ),
                  ),
                  if (internet == false) docsOnProcessNoInternet(media),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget accountBlocked(Size media) {
    return Column(
      children: [
        Container(
          height: media.width * 0.4,
          width: media.width * 0.4,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: verifyPendingBck,
          ),
          alignment: Alignment.center,
          child: Container(
            height: media.width * 0.3,
            width: media.width * 0.3,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: verifyDeclined,
            ),
            child: Icon(
              Icons.access_time_rounded,
              color: buttonText,
              size: media.width * 0.1,
            ),
          ),
        ),
        const SizedBox(height: 20),
        MyText(
          text: languages[choosenLanguage]['text_account_blocked'],
          size: media.width * twenty,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 20),
        MyText(
          text: languages[choosenLanguage]['text_document_rejected'],
          size: media.width * sixteen,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        MyText(
          text: (userDetails['declined_reason'] != null)
              ? userDetails['declined_reason'].toString()
              : '',
          size: media.width * sixteen,
          textAlign: TextAlign.center,
          fontweight: FontWeight.w500,
        ),
      ],
    );
  }

  Widget approvalWaiting(Size media) {
    return Column(
      children: [
        Container(
          height: media.width * 0.4,
          width: media.width * 0.4,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: verifyPendingBck,
          ),
          alignment: Alignment.center,
          child: Container(
            height: media.width * 0.3,
            width: media.width * 0.3,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: verifyPending,
            ),
            child: Icon(
              Icons.access_time_rounded,
              color: buttonText,
              size: media.width * 0.1,
            ),
          ),
        ),
        const SizedBox(height: 20),
        MyText(
          text: languages[choosenLanguage]['text_approval_waiting'],
          size: media.width * twenty,
          fontweight: FontWeight.bold,
        ),
        const SizedBox(height: 20),
        MyText(
          text: languages[choosenLanguage]['text_send_approval'],
          size: media.width * sixteen,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget editDocsButton(Size media) {
    return Button(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => DocsView(fromPage: '1')),
        );
      },
      text: languages[choosenLanguage]['text_edit_docs'],
    );
  }

  Widget docsOnProcessNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() {
          internetTrue();
          getUserDetails();
        }),
      ),
    );
  }
}
