import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:safir_client/Core/databases/api/api_consumer.dart';
import 'package:safir_client/Core/databases/api/end_points.dart';

class DioConsumer extends ApiConsumer {
  final Dio dio;

  DioConsumer({required this.dio}) {
    dio.options.baseUrl = EndPoints.baserUrl;
  }

//!POST
  @override
  Future post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool isFormData = false,
  }) async {
    // if (isUserEntityExist()) {
    //   dio.options.headers['Authorization'] = 'Bearer ${getUserEntityToken()}';
    // }
    log('path: $path');
    log('data: $data');
    log('queryParameters: $queryParameters');
    var response = await dio.post(
      path,
      data: isFormData ? FormData.fromMap(data) : data,
      queryParameters: queryParameters,
    );
    log('response: ${response.data}');
    return response.data;
  }

//!GET
  @override
  Future get(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    // if (isUserEntityExist()) {
    //   dio.options.headers['Authorization'] = 'Bearer ${getUserEntityToken()}';
    // }
    log('path: $path');
    log('data: $data');
    log('queryParameters: $queryParameters');
    var res = await dio.get(path, data: data, queryParameters: queryParameters);
    log('response: ${res.data}');
    return res.data;
  }

//!DELETE
  @override
  Future delete(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    // if (isUserEntityExist()) {
    //   dio.options.headers['Authorization'] = 'Bearer ${getUserEntityToken()}';
    // }
    var res = await dio.delete(
      path,
      data: data,
      queryParameters: queryParameters,
    );
    return res.data;
  }

//!PATCH
  @override
  Future patch(String path,
      {dynamic data,
      Map<String, dynamic>? queryParameters,
      bool isFormData = false}) async {
    var res = await dio.patch(
      path,
      data: isFormData ? FormData.fromMap(data) : data,
      queryParameters: queryParameters,
    );
    return res.data;
  }
}
