import 'package:dio/dio.dart';

import 'package:get_it/get_it.dart';
import 'package:safir_client/Core/databases/api/dio_consumer.dart';
import 'package:safir_client/pages/Orders/Data/Repos/orders_repo_impl.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo_impl.dart';

final getIt = GetIt.instance;
void setupServiceLocator() {
  getIt.registerSingleton<Dio>(Dio());
  getIt.registerSingleton<DioConsumer>(DioConsumer(dio: getIt<Dio>()));

  getIt.registerSingleton<ServicesRepoImpl>(
    ServicesRepoImpl(apiConsumer: getIt<DioConsumer>()),
  );
  getIt.registerSingleton<OrdersRepoImpl>(
    OrdersRepoImpl(apiConsumer: getIt<DioConsumer>()),
  );
}
