import 'package:flutter/material.dart';
import 'package:safir_client/styles/styles.dart';

abstract class AppTextStyles {
  ////*******  Typography  *******////
  static TextStyle font24Bold = const TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
  );

  static TextStyle font24Medium = const TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );

  static TextStyle font24Regular = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w400,
    color: textColor,
  );

  static TextStyle font16Bold = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: textColor,
  );

   static TextStyle font16SemiBold = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: textColor,
  );

  static TextStyle font18Bold = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w700,
    color: textColor,
  );

  static TextStyle font20Bold = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w700,
    color: textColor,
  );

  static TextStyle font16Medium = const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static TextStyle font16Regular = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textColor,
  );

  static TextStyle font14Bold = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w700,
  );

  static TextStyle font14Medium = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static TextStyle font14Regular = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static TextStyle font12Bold = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static TextStyle font12Medium = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static TextStyle font12Regular = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );

  static TextStyle font8Regular = const TextStyle(
    fontSize: 8,
    fontWeight: FontWeight.w400,
  );

  ////*******  Extra  *******////
  static TextStyle font22Regular = const TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w400,
  );
  static TextStyle font22Medium = const TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );
  static TextStyle font18Medium = const TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );
  static TextStyle font10SemiBold = const TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w600,
  );

  static TextStyle font20SemiBold = const TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  //20 medium
  static TextStyle font20Medium = const TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  // 18 600
  static TextStyle font18SemiBold = const TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static TextStyle font13Regular = const TextStyle(
    fontSize: 13,
    fontWeight: FontWeight.w400,
  );

  static TextStyle font13Medium = const TextStyle(
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static TextStyle font11Medium = const TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
  );

  static TextStyle font10Light = const TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w300,
  );

  static TextStyle font10Regular = const TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w400,
  );
}
