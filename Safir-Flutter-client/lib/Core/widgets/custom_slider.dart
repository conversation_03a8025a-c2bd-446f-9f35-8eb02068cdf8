import 'package:flutter/material.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';

class CustomSlider extends StatelessWidget {
  const CustomSlider({super.key, required this.service});

  final CategoryModel service;

  @override
  Widget build(BuildContext context) {
    return CarouselView(
      itemExtent: MediaQuery.of(context).size.width - 32,
      itemSnapping: true,
      children: List.generate(
        service.items?.length ?? 0,
        (index) {
          return const SizedBox();
        },
      ),
    );
  }
}
