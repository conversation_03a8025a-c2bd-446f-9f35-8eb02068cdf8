import 'package:flutter/material.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';

class CustomListView extends StatelessWidget {
  const CustomListView({super.key, required this.service});

  final CategoryModel service;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      separatorBuilder: (_, __) => const SizedBox(width: 8),
      itemCount: service.items?.length ?? 0,
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) {
        return const SizedBox();
      },
    );
  }
}
