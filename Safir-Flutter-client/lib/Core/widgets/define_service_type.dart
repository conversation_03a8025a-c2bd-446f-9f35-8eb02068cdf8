import 'package:flutter/material.dart';
import 'package:safir_client/Core/widgets/custom_grid_view.dart';
import 'package:safir_client/Core/widgets/custom_list_view.dart';
import 'package:safir_client/Core/widgets/custom_slider.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';

class DefineServiceType extends StatelessWidget {
  const DefineServiceType({super.key, required this.service});

  final CategoryModel service;

  @override
  Widget build(BuildContext context) {
    if (service.type == 1) {
      return CustomListView(service: service);
    } else if (service.type == 2) {
      return CustomGridView(service: service);
    } else if (service.type == 3) {
      return CustomSlider(service: service);
    } else {
      return const SizedBox();
    }
  }
}
