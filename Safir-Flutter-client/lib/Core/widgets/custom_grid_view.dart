import 'package:flutter/material.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';

class CustomGridView extends StatelessWidget {
  const CustomGridView({super.key, required this.service});

  final CategoryModel service;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: service.items?.length ?? 0,
      itemBuilder: (context, index) {
        return const SizedBox();
      },
    );
  }
}
