import 'package:flutter/material.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/widgets/widgets.dart';

class NavMenu extends StatefulWidget {
  final dynamic onTap;
  final String text;
  final dynamic textColor;
  final String? image;
  final dynamic icon;

  const NavMenu({
    super.key,
    required this.onTap,
    required this.text,
    this.textColor,
    this.image,
    this.icon,
  });

  @override
  State<NavMenu> createState() => _NavMenuState();
}

class _NavMenuState extends State<NavMenu> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return InkWell(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.only(top: media.width * 0.05),
        child: Column(
          children: [
            Row(
              children: [
                widget.icon == null
                    ? Image.asset(
                        widget.image.toString(),
                        fit: BoxFit.contain,
                        width: media.width * 0.065,
                        color: textColor.withOpacity(0.5),
                      )
                    : Icon(
                        widget.icon,
                        size: media.width * 0.065,
                        color: textColor.withOpacity(0.5),
                      ),
                SizedBox(width: media.width * 0.025),
                Expanded(
                  child: ShowUp(
                    delay: 50,
                    child: MyText(
                      text: widget.text.toString(),
                      size: media.width * sixteen,
                      color: textColor.withOpacity(0.7),
                    ),
                  ),
                )
              ],
            ),
            Container(
              alignment: Alignment.centerRight,
              padding: EdgeInsets.only(
                top: media.width * 0.05,
                left: media.width * 0.09,
              ),
              child: Container(
                color: textColor.withOpacity(0.1),
                height: 1,
              ),
            )
          ],
        ),
      ),
    );
  }
}
