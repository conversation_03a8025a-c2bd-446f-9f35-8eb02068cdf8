import 'dart:async';

import 'package:flutter/material.dart';
import 'package:safir_client/functions/functions.dart';

class BannerImage extends StatefulWidget {
  const BannerImage({super.key});

  @override
  State<BannerImage> createState() => _BannerImageState();
}

class _BannerImageState extends State<BannerImage> {
  final PageController _pageController = PageController(initialPage: 0);
  int _currentPage = 0;
  Timer? timer;
  bool end = false;

  @override
  void initState() {
    super.initState();
    if (banners.length != 1) {
      timer = Timer.periodic(
        const Duration(seconds: 3),
        (Timer timer) {
          if (_currentPage == banners.length - 1) {
            end = true;
          } else if (_currentPage == 0) {
            end = false;
          }

          if (end == false) {
            _currentPage++;
          } else {
            _currentPage--;
          }

          _pageController.animateToPage(
            _currentPage,
            duration: const Duration(milliseconds: 1000),
            curve: Curves.easeInOut,
          );
        },
      );
    }
  }

  @override
  void dispose() {
    timer!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: (banners.length == 1)
          ? Image.network(
              banners[0]['image'],
              fit: BoxFit.fitWidth,
            )
          : PageView.builder(
              controller: _pageController,
              itemCount: banners.length,
              itemBuilder: (context, index) {
                return Image.network(
                  banners[index]['image'],
                  fit: BoxFit.fitWidth,
                );
              },
            ),
    );
  }
}
