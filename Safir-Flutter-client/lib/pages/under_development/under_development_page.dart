import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';

class UnderDevelopmentPage extends StatelessWidget {
  final String? featureName;
  final String? description;
  
  const UnderDevelopmentPage({
    super.key,
    this.featureName,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // Header with back button
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      if (featureName != null)
                        Expanded(
                          child: Text(
                            featureName!,
                            style: AppTextStyles.font20SemiBold.copyWith(
                              color: textColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                
                // Main content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Animation
                        Lottie.asset(
                          'assets/images/under-construction.json',
                          width: media.width * 0.6,
                          height: media.width * 0.6,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: media.width * 0.6,
                              height: media.width * 0.6,
                              decoration: BoxDecoration(
                                color: Colors.orange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.construction,
                                size: 80,
                                color: Colors.orange,
                              ),
                            );
                          },
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // Title
                        Text(
                          'قيد التطوير',
                          style: AppTextStyles.font28Bold.copyWith(
                            color: textColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Description
                        Text(
                          description ?? 'هذه الخدمة قيد التطوير حاليًا وستكون متاحة قريبًا',
                          style: AppTextStyles.font16Medium.copyWith(
                            color: textColor.withOpacity(0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // Features coming soon
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.upcoming,
                                size: 32,
                                color: const Color(0xff2778A0),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'قريبًا جدًا',
                                style: AppTextStyles.font18SemiBold.copyWith(
                                  color: const Color(0xff2778A0),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'نعمل بجد لتوفير أفضل تجربة لك',
                                style: AppTextStyles.font14Medium.copyWith(
                                  color: textColor.withOpacity(0.6),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Back button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xff2778A0),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'العودة للخلف',
                              style: AppTextStyles.font16SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
