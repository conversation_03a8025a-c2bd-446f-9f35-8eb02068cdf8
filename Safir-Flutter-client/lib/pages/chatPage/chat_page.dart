import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  TextEditingController chatText = TextEditingController();
  ScrollController controller = ScrollController();
  bool _sendingMessage = false;
  @override
  void initState() {
    //get messages
    getCurrentMessages();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      child: Safe<PERSON>rea(
        child: Material(
          child: Scaffold(
            body: ValueListenableBuilder(
              valueListenable: valueNotifierBook.value,
              builder: (context, value, child) {
                WidgetsBinding.instance.addPostFrameCallback(
                  (_) {
                    controller.animateTo(controller.position.maxScrollExtent,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.ease);
                  },
                );
                //call for message seen
                // messageSeen();

                return Directionality(
                  textDirection: (languageDirection == 'rtl')
                      ? TextDirection.rtl
                      : TextDirection.ltr,
                  child: Stack(
                    children: [
                      Container(
                        padding: EdgeInsets.all(media.width * 0.05),
                        height: media.height * 1,
                        width: media.width * 1,
                        color: page,
                        child: Column(
                          children: [
                            chatAppBar(media),
                            const SizedBox(height: 20),
                            chatMessages(media),
                            chatTextField(media),
                          ],
                        ),
                      ),
                      if (_sendingMessage) const Positioned(child: Loading())
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget chatAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            Navigator.pop(context, true);
          },
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        Column(
          children: [
            MyText(
              text: userRequestData['driverDetail']['data']['name'],
              size: 16,
              fontweight: FontWeight.bold,
            ),
            const SizedBox(height: 4),
            MyText(
              text: userRequestData['driverDetail']['data']['car_color'] +
                  ' ' +
                  userRequestData['driverDetail']['data']['car_make_name'] +
                  ' ' +
                  userRequestData['driverDetail']['data']['car_model_name'],
              size: media.width * fourteen,
              textAlign: TextAlign.center,
              maxLines: 1,
              color: const Color(0xff8A8A8A),
            ),
          ],
        ),
        const Spacer(),
      ],
    );
  }

  Widget chatMessages(Size media) {
    return Expanded(
      child: SingleChildScrollView(
        controller: controller,
        child: Column(
          children: chatList
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    Container(
                      padding: const EdgeInsets.only(top: 8),
                      width: media.width * 0.9,
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: (chatList[i]['from_type'] == 1)
                                ? MainAxisAlignment.start
                                : MainAxisAlignment.end,
                            children: [
                              Card(
                                elevation: 5,
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxWidth: media.width * 0.65,
                                  ),
                                  padding: EdgeInsets.all(media.width * 0.03),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topLeft: chatList[i]['from_type'] == 1
                                          ? Radius.circular(media.width * 0.02)
                                          : const Radius.circular(0),
                                      topRight: chatList[i]['from_type'] == 1
                                          ? const Radius.circular(0)
                                          : Radius.circular(media.width * 0.02),
                                      bottomRight:
                                          Radius.circular(media.width * 0.02),
                                      bottomLeft:
                                          Radius.circular(media.width * 0.02),
                                    ),
                                    color: (chatList[i]['from_type'] == 1)
                                        ? buttonColor
                                        : const Color(0xffE7EDEF),
                                  ),
                                  child: MyText(
                                    text: chatList[i]['message'],
                                    size: media.width * fourteen,
                                    color: (chatList[i]['from_type'] == 1)
                                        ? textColor
                                        : Colors.black,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: (chatList[i]['from_type'] == 1)
                                ? MainAxisAlignment.start
                                : MainAxisAlignment.end,
                            children: [
                              MyText(
                                text: chatList[i]['converted_created_at'],
                                size: media.width * twelve,
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget chatTextField(Size media) {
    return Container(
      margin: EdgeInsets.only(top: media.width * 0.025),
      padding: EdgeInsets.symmetric(
        horizontal: media.width * 0.025,
        vertical: media.width * 0.01,
      ),
      width: media.width * 0.9,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: borderLines, width: 1.2),
        color: page,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: TextField(
              controller: chatText,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: languages[choosenLanguage]['text_entermessage'],
                hintStyle: GoogleFonts.notoKufiArabic(
                  color: textColor.withOpacity(0.4),
                  fontSize: media.width * twelve,
                ),
              ),
              style: GoogleFonts.notoKufiArabic(color: textColor),
              minLines: 1,
              maxLines: 4,
              onChanged: (val) {},
            ),
          ),
          InkWell(
            onTap: () async {
              FocusManager.instance.primaryFocus?.unfocus();
              setState(() => _sendingMessage = true);
              await sendMessage(chatText.text);
              chatText.clear();
              setState(() => _sendingMessage = false);
            },
            child: Image.asset(
              AppAssets.send,
              fit: BoxFit.contain,
              width: media.width * 0.075,
              color: textColor,
            ),
          )
        ],
      ),
    );
  }
}
