import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';

// ignore: must_be_immutable
class NoInternet extends StatefulWidget {
  dynamic onTap;
  // ignore: use_key_in_widget_constructors
  NoInternet({required this.onTap});

  @override
  State<NoInternet> createState() => _NoInternetState();
}

class _NoInternetState extends State<NoInternet> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Container(
      height: media.height * 1,
      width: media.width * 1,
      decoration: BoxDecoration(
        image: customBackground,
        color: page,
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: media.width * 0.6,
            child: Image.asset(
              AppAssets.noInternet,
              fit: BoxFit.contain,
            ),
          ),
          const SizedBox(height: 24),
          MyText(
            text: (languages.isNotEmpty && choosenLanguage != '')
                ? languages[choosenLanguage]['text_nointernet']
                : 'No Internet Connection',
            size: media.width * twentyfour,
            fontweight: FontWeight.w600,
            color: textColor,
          ),
          const SizedBox(height: 24),
          MyText(
            text: (languages.isNotEmpty && choosenLanguage != '')
                ? languages[choosenLanguage]['text_nointernetdesc']
                : 'No Internet Connection\nPlease check your Internet connection and try again later',
            size: media.width * fourteen,
            color: hintColor,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          // Button(
          //   onTap: widget.onTap,
          //   text: languages[choosenLanguage]['text_back_home'],
          // )
        ],
      ),
    );
  }
}
