import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/pages/Drawer/nav_drawer.dart';
import 'package:safir_client/pages/Navigation%20Bar/manager/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:safir_client/pages/Navigation%20Bar/manager/language_cubit/language_cubit.dart';
import 'package:safir_client/pages/NavigatorPages/edit_profile.dart';
import 'package:safir_client/pages/NavigatorPages/settings.dart';
import 'package:safir_client/pages/NavigatorPages/walletpage.dart';
import 'package:safir_client/pages/Services/Presentation/views/categories_view.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/translations/translation.dart';

class MainHomeView extends StatefulWidget {
  const MainHomeView({super.key});

  @override
  State<MainHomeView> createState() => _MainHomeViewState();
}

class _MainHomeViewState extends State<MainHomeView> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<BottomNavBarCubit>(context).showBottomNavBar();
  }

  @override
  Widget build(BuildContext context) {
    PersistentTabController? controller =
        PersistentTabController(initialIndex: 0);
    return BlocBuilder<LanguageCubit, LanguageState>(
      builder: (context, state) {
        List<PersistentBottomNavBarItem> navBarsItems() {
          return [
            PersistentBottomNavBarItem(
              icon: Lottie.asset(
                'assets/images/home-animation.json',
                fit: BoxFit.cover,
                height: 50,
                width: 50,
              ),
              inactiveIcon: Lottie.asset(
                'assets/images/home-animation.json',
                fit: BoxFit.cover,
                height: 50,
                width: 50,
              ),
              title: languages[choosenLanguage]['text_home_title'],
              textStyle: AppTextStyles.font12Medium,
              activeColorPrimary: const Color(0xff2778A0),
              inactiveColorPrimary: Colors.white,
            ),
            PersistentBottomNavBarItem(
              icon: Lottie.asset(
                'assets/images/wallet-animation.json',
                fit: BoxFit.cover,
                height: 28,
                width: 28,
              ),
              inactiveIcon: Lottie.asset(
                'assets/images/wallet-animation.json',
                fit: BoxFit.cover,
                height: 28,
                width: 28,
              ),
              title: languages[choosenLanguage]['text_wallet'],
              textStyle: AppTextStyles.font12Medium,
              activeColorPrimary: const Color(0xff2778A0),
              inactiveColorPrimary: Colors.white,
            ),
            PersistentBottomNavBarItem(
              icon: Lottie.asset(
                'assets/images/profile-animation.json',
                fit: BoxFit.cover,
                height: 36,
                width: 36,
              ),
              inactiveIcon: Lottie.asset(
                'assets/images/profile-animation.json',
                fit: BoxFit.cover,
                height: 36,
                width: 36,
              ),
              title: languages[choosenLanguage]['text_profile'],
              textStyle: AppTextStyles.font12Medium,
              activeColorPrimary: const Color(0xff2778A0),
              inactiveColorPrimary: Colors.white,
            ),
            PersistentBottomNavBarItem(
              icon: Lottie.asset(
                'assets/images/settings-animation.json',
                fit: BoxFit.cover,
                height: 28,
                width: 28,
              ),
              inactiveIcon: Lottie.asset(
                'assets/images/settings-animation.json',
                fit: BoxFit.cover,
                height: 28,
                width: 28,
              ),
              title: languages[choosenLanguage]['text_settings'],
              textStyle: AppTextStyles.font12Medium,
              activeColorPrimary: const Color(0xff2778A0),
              inactiveColorPrimary: Colors.white,
            ),
            PersistentBottomNavBarItem(
              icon: Lottie.asset(
                'assets/images/more-animation.json',
                fit: BoxFit.cover,
                height: 40,
                width: 40,
              ),
              inactiveIcon: Lottie.asset(
                'assets/images/more-animation.json',
                fit: BoxFit.cover,
                height: 40,
                width: 40,
              ),
              title: languages[choosenLanguage]['text_more'],
              textStyle: AppTextStyles.font12Medium,
              activeColorPrimary: const Color(0xff2778A0),
              inactiveColorPrimary: Colors.white,
            ),
          ];
        }

        List<Widget> buildScreens() {
          return [
            const CategoriesView(),
            const WalletPage(),
            const EditProfile(),
            const SettingsPage(),
            const NavDrawer(),
          ];
        }

        return BlocBuilder<BottomNavBarCubit, BottomNavBarState>(
          builder: (_, __) {
            return Builder(
              builder: (context) {
                return Directionality(
                  textDirection: (languageDirection == 'rtl')
                      ? TextDirection.rtl
                      : TextDirection.ltr,
                  child: PersistentTabView(
                    context,
                    screens: buildScreens(),
                    items: navBarsItems(),
                    controller: controller,
                    bottomScreenMargin: 66,
                    onItemSelected: (index) => controller.jumpToTab(index),
                    resizeToAvoidBottomInset: true,
                    stateManagement: true,
                    backgroundColor: page,
                    isVisible:
                        BlocProvider.of<BottomNavBarCubit>(context).isVisible,
                    handleAndroidBackButtonPress: true,
                    animationSettings: const NavBarAnimationSettings(
                      screenTransitionAnimation:
                          ScreenTransitionAnimationSettings(
                        animateTabTransition: true,
                        curve: Curves.ease,
                        duration: Duration(milliseconds: 200),
                      ),
                      navBarItemAnimation: ItemAnimationSettings(
                        duration: Duration(milliseconds: 200),
                        curve: Curves.ease,
                      ),
                    ),
                    navBarHeight: 66,
                    padding: const EdgeInsets.only(
                        top: 8, bottom: 12, left: 4, right: 4),
                    confineToSafeArea: true,
                    hideNavigationBarWhenKeyboardAppears: true,
                    navBarStyle: NavBarStyle.style6,
                    decoration: NavBarDecoration(
                      border: Border.all(
                        color: const Color(0xFFFFFFE5).withOpacity(0.9),
                        width: 0.0,
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          page,
                          page.withOpacity(0.37),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}
