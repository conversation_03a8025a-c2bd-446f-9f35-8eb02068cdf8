import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

class BottomNavBarCubit extends Cubit<BottomNavBarState> {
  BottomNavBarCubit() : super(BottomNavBarInitial());

  bool isVisible = true;

  void changeVisibility(bool value) {
    emit(ChangeShowBottomNavBarLoading());
    isVisible = value;
    emit(ChangeShowBottomNavBarSuccess());
  }

  showBottomNavBar() {
    isVisible = true;
  }
}

sealed class BottomNavBarState extends Equatable {
  const BottomNavBarState();

  @override
  List<Object> get props => [];
}

final class BottomNavBarInitial extends BottomNavBarState {}

final class ChangeShowBottomNavBarLoading extends BottomNavBarState {}

final class ChangeShowBottomNavBarSuccess extends BottomNavBarState {}
