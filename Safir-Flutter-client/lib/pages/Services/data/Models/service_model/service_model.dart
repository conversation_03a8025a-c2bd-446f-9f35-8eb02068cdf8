import 'question.dart';

class ServiceModel {
  int? id;
  String? title;
  String? icon;
  int? specialistsNumber;
  List<Question>? questions;

  ServiceModel({
    this.id,
    this.title,
    this.icon,
    this.specialistsNumber,
    this.questions,
  });

  @override
  String toString() {
    return 'ServiceModel(id: $id, title: $title, icon: $icon, specialistsNumber: $specialistsNumber, questions: $questions)';
  }

  factory ServiceModel.fromJson(Map<String, dynamic> json) => ServiceModel(
        id: json['id'] as int?,
        title: json['title'] as String?,
        icon: json['icon'] as String?,
        specialistsNumber: json['specialistsNumber'] as int?,
        questions: (json['questions'] as List<dynamic>?)
            ?.map((e) => Question.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'icon': icon,
        'specialistsNumber': specialistsNumber,
        'questions': questions?.map((e) => e.to<PERSON>son()).toList(),
      };
}
