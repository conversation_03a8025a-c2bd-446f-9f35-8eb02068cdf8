import 'answer.dart';

class Question {
  int? id;
  String? question;
  bool? multipleAnswers;
  List<Answer>? answers;

  Question({this.id, this.question, this.multipleAnswers, this.answers});

  @override
  String toString() {
    return 'Question(id: $id, question: $question, multipleAnswers: $multipleAnswers, answers: $answers)';
  }

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json['id'] as int?,
        question: json['question'] as String?,
        multipleAnswers: json['multipleAnswers'] as bool?,
        answers: (json['answers'] as List<dynamic>?)
            ?.map((e) => Answer.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'question': question,
        'multipleAnswers': multipleAnswers,
        'answers': answers?.map((e) => e.toJson()).toList(),
      };
}
