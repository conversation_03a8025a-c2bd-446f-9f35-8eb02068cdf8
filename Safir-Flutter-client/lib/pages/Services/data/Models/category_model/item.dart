import 'data.dart';

class Item {
  int? id;
  String? image;
  int? action;
  Data? data;
  String? title;
  String? icon;

  Item({this.id, this.image, this.action, this.data, this.title, this.icon});

  @override
  String toString() {
    return 'Item(id: $id, image: $image, action: $action, data: $data, title: $title, icon: $icon)';
  }

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        id: json['id'] as int?,
        image: json['image'] as String?,
        action: json['action'] as int?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
        title: json['title'] as String?,
        icon: json['icon'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'image': image,
        'action': action,
        'data': data?.toJson(),
        'title': title,
        'icon': icon,
      };
}
