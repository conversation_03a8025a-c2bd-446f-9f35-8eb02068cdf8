class Data {
  int? id;
  bool? destination;

  Data({this.id, this.destination});

  @override
  String toString() => 'Data(id: $id, destination: $destination)';

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json['id'] as int?,
        destination: json['destination'] as bool?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'destination': destination,
      };
}
