import 'item.dart';

class CategoryModel {
  int? id;
  String? title;
  int? type;
  String? icon;
  List<Item>? items;

  CategoryModel({this.id, this.title, this.icon, this.items, this.type});

  @override
  String toString() {
    return 'CategoryModel(id: $id, title: $title, type:$type, icon: $icon, items: $items)';
  }

  factory CategoryModel.fromJson(Map<String, dynamic> json) => CategoryModel(
        id: json['id'] as int?,
        title: json['title'] as String?,
        type: json['type'] as int?,
        icon: json['icon'] as String?,
        items: (json['items'] as List<dynamic>?)
            ?.map((e) => Item.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'type': type,
        'icon': icon,
        'items': items?.map((e) => e.toJson()).toList(),
      };
}
