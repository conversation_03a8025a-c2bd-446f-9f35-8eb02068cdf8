import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:safir_client/Core/databases/api/api_consumer.dart';
import 'package:safir_client/Core/databases/api/end_points.dart';
import 'package:safir_client/Core/errors/exceptions.dart';
import 'package:safir_client/Core/errors/failure.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/service_model.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo.dart';

import '../../../../functions/functions.dart';

class ServicesRepoImpl implements ServicesRepo {
  final ApiConsumer apiConsumer;

  ServicesRepoImpl({required this.apiConsumer});

  @override
  Future<Either<Failure, List<CategoryModel>>> fetchCategories() async {
    try {
      var data = await apiConsumer.get('${EndPoints.categories}?lang=$choosenLanguage');
      List<CategoryModel> services = [];
      for (var item in data['data']) {
        services.add(CategoryModel.fromJson(item));
      }
      return right(services);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<ServiceModel>>> fetchServices() async {
    try {
      var data = await apiConsumer.get(EndPoints.services);
      List<ServiceModel> services = [];
      for (var item in data['data']) {
        services.add(ServiceModel.fromJson(item));
      }
      return right(services);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> requestService({
    required int serviceId,
    required List<dynamic> selections,
  }) async {
    try {
      var data = await apiConsumer.post(
        EndPoints.requestService,
        data: {
          'service_id': serviceId,
          'selections': selections,
        },
      );
      // return right(data['message']); // TODO: Uncomment this line
      return right('Service requested successfully');
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }
}
