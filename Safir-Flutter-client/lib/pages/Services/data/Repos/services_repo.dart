import 'package:dartz/dartz.dart';
import 'package:safir_client/Core/errors/failure.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/service_model.dart';

abstract class ServicesRepo {
  Future<Either<Failure, List<CategoryModel>>> fetchCategories();
  Future<Either<Failure, List<ServiceModel>>> fetchServices();
  Future<Either<Failure, String>> requestService({
    required int serviceId,
    required List<dynamic> selections,
  });
}
