import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/service_model.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo.dart';

part 'services_state.dart';

class ServicesCubit extends Cubit<ServicesState> {
  ServicesCubit({required this.servicesRepo}) : super(ServicesInitial());

  final ServicesRepo servicesRepo;

  Future<void> fetchServices() async {
    emit(FetchServicesLoadingState());
    final services = await servicesRepo.fetchServices();

    services.fold(
      (failure) =>
          emit(FetchServicesFailureState(errMessage: failure.toString())),
      (services) => emit(FetchServicesSuccessState(services: services)),
    );
  }
}
