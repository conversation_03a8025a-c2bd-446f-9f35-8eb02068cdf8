part of 'services_cubit.dart';

sealed class ServicesState extends Equatable {
  const ServicesState();

  @override
  List<Object> get props => [];
}

final class ServicesInitial extends ServicesState {}

final class FetchServicesLoadingState extends ServicesState {}

final class FetchServicesSuccessState extends ServicesState {
  final List<ServiceModel> services;

  const FetchServicesSuccessState({required this.services});

  @override
  List<Object> get props => [services];
}

final class FetchServicesFailureState extends ServicesState {
  final String errMessage;

  const FetchServicesFailureState({required this.errMessage});

  @override
  List<Object> get props => [errMessage];
}
