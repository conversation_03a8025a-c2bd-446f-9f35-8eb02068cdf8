import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo.dart';

part 'request_service_state.dart';

class RequestServiceCubit extends Cubit<RequestServiceState> {
  RequestServiceCubit({required this.servicesRepo, required this.length})
      : super(RequestServiceInitial()) {
    initSelections();
  }

  final ServicesRepo servicesRepo;
  final int length;

  late List<dynamic> selections;

  void initSelections() {
    selections = List.filled(length, null);
    printSelections();
  }

  Future<void> requestService({required int serviceId}) async {
    emit(RequestServiceLoading());
    final result = await servicesRepo.requestService(
      serviceId: serviceId,
      selections: selections,
    );

    result.fold(
      (failure) => emit(RequestServiceFailure(errMessage: failure.toString())),
      (message) => emit(RequestServiceSuccess(message: message)),
    );
  }

  void printSelections() {
    debugPrint(selections.toString());
  }

  bool isFilled() {
    for (var selection in selections) {
      if (selection == null) {
        return false;
      }
    }
    return true;
  }
}
