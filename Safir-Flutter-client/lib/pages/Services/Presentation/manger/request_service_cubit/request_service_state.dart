part of 'request_service_cubit.dart';

sealed class RequestServiceState extends Equatable {
  const RequestServiceState();

  @override
  List<Object> get props => [];
}

final class RequestServiceInitial extends RequestServiceState {}

final class RequestServiceLoading extends RequestServiceState {}

final class RequestServiceSuc<PERSON> extends RequestServiceState {
  final String message;

  const RequestServiceSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

final class RequestServiceFailure extends RequestServiceState {
  final String errMessage;

  const RequestServiceFailure({required this.errMessage});

  @override
  List<Object> get props => [errMessage];
}
