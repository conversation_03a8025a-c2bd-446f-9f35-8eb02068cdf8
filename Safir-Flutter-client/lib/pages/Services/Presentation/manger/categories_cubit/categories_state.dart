part of 'categories_cubit.dart';

@immutable
sealed class CategoriesState {}

final class CategoriesInitial extends CategoriesState {}

final class FetchCategoriesLoadingState extends CategoriesState {}

final class FetchCategoriesSuccessState extends CategoriesState {
  final List<CategoryModel> services;

  FetchCategoriesSuccessState({required this.services});
}

final class FetchCategoriesFailureState extends CategoriesState {
  final String errMessage;

  FetchCategoriesFailureState({required this.errMessage});
}
