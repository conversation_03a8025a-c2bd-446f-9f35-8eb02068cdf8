import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo.dart';

part 'categories_state.dart';

class CategoriesCubit extends Cubit<CategoriesState> {
  CategoriesCubit.categoriesCubit({required this.servicesRepo})
      : super(CategoriesInitial());

  final ServicesRepo servicesRepo;

  Future<void> fetchCategories() async {
    emit(FetchCategoriesLoadingState());
    final services = await servicesRepo.fetchCategories();

    services.fold(
      (failure) =>
          emit(FetchCategoriesFailureState(errMessage: failure.toString())),
      (services) => emit(FetchCategoriesSuccessState(services: services)),
    );
  }
}
