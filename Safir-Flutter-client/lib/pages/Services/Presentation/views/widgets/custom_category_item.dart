import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/item.dart';
import 'package:safir_client/pages/login/under_development_view.dart';
import 'package:safir_client/pages/onTripPage/map_page.dart';

class CustomCategoryItem extends StatelessWidget {
  const CustomCategoryItem({
    super.key,
    required this.serviceItem,
  });

  final Item serviceItem;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (serviceItem.action == null) {
          return;
        } else if (serviceItem.action == 1) {
          selectedServiceItem = serviceItem;
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: const Maps(),
            withNavBar: false,
          );
          return;
        } else if (serviceItem.action == 2) {
          // TODO : open url
          return;
        } else if (serviceItem.action == 3) {
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: const UnderDevelopmentView(),
            withNavBar: false,
          );
          return;
        }
      },
      child: CachedNetworkImage(
        imageUrl: serviceItem.image ?? '',
        errorWidget: (context, url, error) => const Icon(Icons.error),
        imageBuilder: (context, imageProvider) => ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 100,
            height: 150,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
