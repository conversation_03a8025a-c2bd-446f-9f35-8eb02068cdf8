import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/Core/widgets/custom_failure_widget.dart';
import 'package:safir_client/pages/Services/Presentation/manger/services_cubit/services_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/custom_service_section.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/shimmer_loading_service.dart';

class ServicesSection extends StatelessWidget {
  const ServicesSection({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            "خدمات سفير المنزليه",
            style: AppTextStyles.font24Regular,
          ),
        ),
        const SizedBox(height: 8),
        BlocBuilder<ServicesCubit, ServicesState>(
          builder: (context, state) {
            if (state is FetchServicesLoadingState) {
              return GridView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 1.45,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: 5,
                itemBuilder: (context, index) {
                  return const ShimmerLoadingService();
                },
              );
            } else if (state is FetchServicesFailureState) {
              return CustomFailureWidget(errMessage: state.errMessage);
            } else if (state is FetchServicesSuccessState) {
              return GridView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 1.45,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: state.services.length,
                itemBuilder: (context, index) {
                  return CustomServiceSection(
                    service: state.services[index],
                  );
                },
              );
            }
            return const SizedBox();
          },
        ),
      ],
    );
  }
}
