import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/pages/Services/Presentation/manger/categories_cubit/categories_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/manger/services_cubit/services_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/categories_section.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/services_section.dart';
import 'package:safir_client/pages/noInternet/nointernet.dart';

class CategoriesViewBody extends StatefulWidget {
  const CategoriesViewBody({super.key});

  @override
  State<CategoriesViewBody> createState() => _CategoriesViewBodyState();
}

class _CategoriesViewBodyState extends State<CategoriesViewBody> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: valueNotifierBook.value,
      builder: (context, value, child) => Stack(
        children: [
          RefreshIndicator(
            onRefresh: () async {
              context.read<CategoriesCubit>().fetchCategories();
              context.read<ServicesCubit>().fetchServices();
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Image.asset(AppAssets.logoWithName2, height: 370),
                  const SizedBox(height: 16),
                  const CategoriesSection(),
                  const SizedBox(height: 16),
                  const ServicesSection(),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
          if (!internet) servicesNoInternet(MediaQuery.of(context).size),
        ],
      ),
    );
  }

  Widget servicesNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}
