import 'package:flutter/material.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/pages/Services/data/Models/category_model/category_model.dart';
import 'package:carousel_slider/carousel_slider.dart';

import 'custom_category_item.dart';

class CustomCategorySection extends StatelessWidget {
  const CustomCategorySection({super.key, required this.service});

  final CategoryModel service;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            service.title ?? '',
            style: AppTextStyles.font24Regular,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          margin: const EdgeInsetsDirectional.only(start: 8),
          child: CarouselSlider.builder(
            itemCount: service.items?.length ?? 0,
            itemBuilder: (context, index, realIndex) {
              return CustomCategoryItem(
                serviceItem: service.items![index],
              );
            },
            options: CarouselOptions(
              padEnds: false,
              scrollPhysics: const BouncingScrollPhysics(),
              height: 150,
              viewportFraction: 0.285,
              autoPlay: true,
              enableInfiniteScroll: false,
              enlargeCenterPage: false,
              pauseAutoPlayOnTouch: true,
              scrollDirection: Axis.horizontal,
              reverse: false,
              autoPlayInterval: const Duration(seconds: 3),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              enlargeStrategy: CenterPageEnlargeStrategy.scale,
              initialPage: 0,
            ),
          ),
        ),
      ],
    );
  }
}
