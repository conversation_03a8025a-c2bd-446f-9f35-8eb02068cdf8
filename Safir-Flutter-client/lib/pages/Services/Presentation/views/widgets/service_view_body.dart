import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/Core/widgets/custom_loading_widget.dart';
import 'package:safir_client/pages/Orders/Presentation/Views/orders_view.dart';
import 'package:safir_client/pages/Services/Presentation/manger/request_service_cubit/request_service_cubit.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/answer.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/service_model.dart';
import 'package:safir_client/styles/styles.dart';

class ServiceViewBody extends StatefulWidget {
  const ServiceViewBody({super.key, required this.service});

  final ServiceModel service;

  @override
  State<ServiceViewBody> createState() => _ServiceViewBodyState();
}

class _ServiceViewBodyState extends State<ServiceViewBody> {
  int currentQuestion = 0;

  @override
  Widget build(BuildContext context) {
    RequestServiceCubit cubit = BlocProvider.of<RequestServiceCubit>(context);
    return Column(
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50),
                child: Image.network(
                  widget.service.icon ?? '',
                  fit: BoxFit.cover,
                  width: 20,
                  height: 20,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              widget.service.title ?? '',
              style: AppTextStyles.font16Medium.copyWith(
                color: textColor.withOpacity(0.5),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Align(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            widget.service.questions![currentQuestion].question ?? '',
            style: AppTextStyles.font24Medium.copyWith(
              color: textColor,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount:
                widget.service.questions![currentQuestion].answers!.length,
            itemBuilder: (context, index) {
              bool isMultiple =
                  widget.service.questions![currentQuestion].multipleAnswers!;
              Answer answer =
                  widget.service.questions![currentQuestion].answers![index];
              return GestureDetector(
                onTap: () {
                  if (isMultiple) {
                    setState(() {
                      if (cubit.selections[currentQuestion] == null) {
                        cubit.selections[currentQuestion] = [answer.id!];
                      } else {
                        if (cubit.selections[currentQuestion]
                            .contains(answer.id!)) {
                          cubit.selections[currentQuestion].remove(answer.id!);
                        } else {
                          cubit.selections[currentQuestion].add(answer.id!);
                        }
                      }
                    });
                  } else {
                    setState(
                        () => cubit.selections[currentQuestion] = [answer.id!]);
                  }
                  cubit.printSelections();
                },
                child: ListTile(
                  contentPadding: EdgeInsets.zero,
                  horizontalTitleGap: 0,
                  minVerticalPadding: 0,
                  dense: true,
                  title: Text(
                    answer.answer!,
                    style: AppTextStyles.font18Medium.copyWith(
                      color: textColor,
                    ),
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.all(2),
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: (cubit.selections[currentQuestion] != null &&
                                cubit.selections[currentQuestion]
                                    .contains(answer.id!))
                            ? buttonColor
                            : textColor.withOpacity(0.5),
                      ),
                    ),
                    child: (cubit.selections[currentQuestion] != null &&
                            cubit.selections[currentQuestion]
                                .contains(answer.id!))
                        ? Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: buttonColor,
                            ),
                          )
                        : null,
                  ),
                ),
              );
            },
          ),
        ),
        Row(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${currentQuestion + 1} من ${widget.service.questions!.length}',
                  style: AppTextStyles.font16Medium.copyWith(
                    color: textColor.withOpacity(0.5),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: List.generate(
                    widget.service.questions!.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: (index == currentQuestion)
                            ? buttonColor
                            : textColor.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const Expanded(child: SizedBox()),
            ElevatedButton(
              onPressed: () {
                if (currentQuestion > 0) {
                  setState(() => currentQuestion--);
                } else {
                  Navigator.pop(context);
                }
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                backgroundColor: textColor.withOpacity(0.5),
                padding: const EdgeInsets.all(8),
              ),
              child: const Icon(Icons.arrow_back, color: Colors.black),
            ),
            const SizedBox(width: 8),
            BlocConsumer<RequestServiceCubit, RequestServiceState>(
              listener: (context, state) {
                if (state is RequestServiceSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        state.message,
                        style: AppTextStyles.font16Medium.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: buttonColor,
                    ),
                  );
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const OrdersView(),
                    ),
                  );
                } else if (state is RequestServiceFailure) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        state.errMessage,
                        style: AppTextStyles.font16Medium.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is RequestServiceLoading) {
                  return const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 34),
                    child: CustomLoadingWidget(),
                  );
                }
                return ElevatedButton(
                  onPressed: () {
                    if (cubit.selections[currentQuestion] == null) return;
                    if (currentQuestion <
                        widget.service.questions!.length - 1) {
                      setState(() => currentQuestion++);
                    } else {
                      cubit.requestService(serviceId: widget.service.id!);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    backgroundColor: buttonColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 36,
                      vertical: 8,
                    ),
                  ),
                  child: Text(
                    'التالي',
                    style: AppTextStyles.font16Medium.copyWith(
                      color: textColor,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
