import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/widgets/custom_failure_widget.dart';
import 'package:safir_client/pages/Services/Presentation/manger/categories_cubit/categories_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/custom_category_section.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/shimmer_loading_section.dart';

class CategoriesSection extends StatelessWidget {
  const CategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CategoriesCubit, CategoriesState>(
      builder: (context, state) {
        if (state is FetchCategoriesLoadingState) {
          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (_, __) => const SizedBox(height: 16),
            itemCount: 6, // Display 6 shimmer items
            itemBuilder: (context, index) {
              return const ShimmerLoadingSection();
            },
          );
        } else if (state is FetchCategoriesFailureState) {
          return CustomFailureWidget(errMessage: state.errMessage);
        } else if (state is FetchCategoriesSuccessState) {
          return ListView.separated(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (_, __) => const SizedBox(height: 16),
            itemCount: state.services.length,
            itemBuilder: (context, index) {
              return CustomCategorySection(
                service: state.services[index],
              );
            },
          );
        }
        return const SizedBox();
      },
    );
  }
}
