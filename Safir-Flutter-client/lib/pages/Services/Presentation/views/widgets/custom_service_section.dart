import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/pages/Services/Presentation/views/service_view.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/service_model.dart';
import 'package:safir_client/pages/dummy_features/service_navigation_helper.dart';
import 'package:safir_client/styles/styles.dart';

class CustomServiceSection extends StatelessWidget {
  const CustomServiceSection({super.key, required this.service});

  final ServiceModel service;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (service.questions == null ||
            (service.questions != null && service.questions!.isEmpty)) {
          // Service is under development, navigate to appropriate dummy page
          ServiceNavigationHelper.navigateToService(context, service.title);
          return;
        }
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: ServiceView(service: service),
          withNavBar: false,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: page,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50),
                child: Image.network(
                  service.icon ?? '',
                  fit: BoxFit.cover,
                  width: 20,
                  height: 20,
                ),
              ),
            ),
            const SizedBox(height: 2),
            Text(
              service.title ?? '',
              style: AppTextStyles.font18SemiBold.copyWith(
                color: textColor,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              '${service.specialistsNumber} متخصصا',
              style: AppTextStyles.font16Medium.copyWith(
                color: textColor.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
