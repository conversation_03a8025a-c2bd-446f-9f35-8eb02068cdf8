import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/databases/api/dio_consumer.dart';
import 'package:safir_client/Core/services/service_locator.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/pages/Navigation%20Bar/manager/language_cubit/language_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/manger/categories_cubit/categories_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/manger/services_cubit/services_cubit.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo_impl.dart';
import 'package:safir_client/styles/styles.dart';

import 'widgets/categories_view_body.dart';

class CategoriesView extends StatefulWidget {
  const CategoriesView({super.key});

  @override
  State<CategoriesView> createState() => _CategoriesViewState();
}

class _CategoriesViewState extends State<CategoriesView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: BlocBuilder<LanguageCubit, LanguageState>(
            builder: (context, state) {
              return Directionality(
                textDirection: (languageDirection == 'rtl')
                    ? TextDirection.rtl
                    : TextDirection.ltr,
                child: MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => CategoriesCubit.categoriesCubit(
                        // servicesRepo: getIt.get<ServicesRepoImpl>(),
                        servicesRepo: ServicesRepoImpl(
                            apiConsumer: getIt.get<DioConsumer>()),
                      )..fetchCategories(),
                    ),
                    BlocProvider(
                      create: (context) => ServicesCubit(
                        servicesRepo: ServicesRepoImpl(
                            apiConsumer: getIt.get<DioConsumer>()),
                      )..fetchServices(),
                    ),
                  ],
                  child: const CategoriesViewBody(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
