import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/databases/api/dio_consumer.dart';
import 'package:safir_client/Core/services/service_locator.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/pages/Services/Presentation/manger/request_service_cubit/request_service_cubit.dart';
import 'package:safir_client/pages/Services/Presentation/views/widgets/service_view_body.dart';
import 'package:safir_client/pages/Services/data/Models/service_model/service_model.dart';
import 'package:safir_client/pages/Services/data/Repos/services_repo_impl.dart';
import 'package:safir_client/styles/styles.dart';

class ServiceView extends StatelessWidget {
  const ServiceView({super.key, required this.service});
  final ServiceModel service;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocProvider(
        create: (context) => RequestServiceCubit(
          servicesRepo: ServicesRepoImpl(
            apiConsumer: getIt.get<DioConsumer>(),
          ),
          length: service.questions!.length,
        ),
        child: Container(
          decoration: BoxDecoration(
            image: customBackground,
            color: page,
          ),
          child: SafeArea(
            child: Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.2),
                                  spreadRadius: 2,
                                )
                              ],
                              color: page,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.arrow_back_ios_new,
                              size: 20,
                              color: textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Expanded(child: ServiceViewBody(service: service)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
