import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:share_plus/share_plus.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../noInternet/noInternet.dart';

class ReferralPage extends StatefulWidget {
  const ReferralPage({super.key});

  @override
  State<ReferralPage> createState() => _ReferralPageState();
}

class _ReferralPageState extends State<ReferralPage> {
  bool _isLoading = true;
  bool _showToast = false;
  dynamic _package;
  // ignore: prefer_typing_uninitialized_variables
  var androidUrl;
  // ignore: prefer_typing_uninitialized_variables
  var iosUrl;

  @override
  void initState() {
    _getReferral();
    super.initState();
  }

  //get referral code
  _getReferral() async {
    await getReferral();
    _package = await PackageInfo.fromPlatform();
    androidUrl = 'android - '
        'https://play.google.com/store/apps/details?id=${_package.packageName}';
    iosUrl = 'ios - '
        'http://itunes.apple.com/lookup?bundleId=${_package.packageName}';
    setState(() => _isLoading = false);
  }

  //show toast for copied
  showToast() {
    setState(() => _showToast = true);
    Future.delayed(
      const Duration(seconds: 1),
      () => setState(() => _showToast = false),
    );
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: ValueListenableBuilder(
        valueListenable: valueNotifierHome.value,
        builder: (context, value, child) {
          return Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(media.width * 0.05),
                  height: media.height * 1,
                  width: media.width * 1,
                  decoration: BoxDecoration(
                    image: customBackground,
                    color: page,
                  ),
                  child: SafeArea(
                    child: (myReferralCode.isNotEmpty)
                        ? Column(
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    referralAppBar(media),
                                    const SizedBox(height: 20),
                                    referralImage(media),
                                    const SizedBox(height: 32),
                                    referralOfferSection(media),
                                    const SizedBox(height: 20),
                                    referralCodeSection(media),
                                  ],
                                ),
                              ),
                              referralButton(media),
                            ],
                          )
                        : Container(),
                  ),
                ),
                if (internet == false) referralNoInternet(media),
                if (_isLoading == true) const Positioned(child: Loading()),
                if (_showToast == true) referralShowToast(media),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget referralAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_enable_referal'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget referralImage(Size media) {
    return SizedBox(
      width: media.width * 0.9,
      height: media.height * 0.16,
      child: Image.asset(
        AppAssets.referral,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget referralOfferSection(Size media) {
    return Row(
      children: [
        MyText(
          text: myReferralCode['referral_comission_string'],
          size: media.width * sixteen,
          textAlign: TextAlign.center,
          fontweight: FontWeight.w600,
        ),
      ],
    );
  }

  Widget referralCodeSection(Size media) {
    return Container(
      width: media.width * 0.9,
      padding: EdgeInsets.all(media.width * 0.05),
      decoration: BoxDecoration(
        border: Border.all(
          color: borderLines,
          width: 1.2,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MyText(
            text: myReferralCode['refferal_code'],
            size: media.width * sixteen,
            fontweight: FontWeight.w600,
            color: textColor.withOpacity(0.5),
          ),
          InkWell(
            onTap: () {
              setState(
                () {
                  Clipboard.setData(
                    ClipboardData(
                      text: myReferralCode['refferal_code'],
                    ),
                  );
                },
              );
              showToast();
            },
            child: Icon(Icons.copy, color: textColor),
          )
        ],
      ),
    );
  }

  Widget referralButton(Size media) {
    return Container(
      padding: EdgeInsets.only(
        top: media.width * 0.05,
        bottom: media.width * 0.05,
      ),
      child: Button(
        onTap: () async {
          await Share.share(
            // ignore: prefer_interpolation_to_compose_strings
            languages[choosenLanguage]['text_invitation_1']
                    .toString()
                    .replaceAll('55', _package.appName) +
                ' ' +
                myReferralCode['refferal_code'] +
                ' ' +
                languages[choosenLanguage]['text_invitation_2'] +
                ' \n \n ' +
                androidUrl +
                '\n \n  ' +
                iosUrl,
          );
        },
        textcolor: buttonText,
        color: buttonColor,
        text: languages[choosenLanguage]['text_invite'],
      ),
    );
  }

  Widget referralShowToast(Size media) {
    return Positioned(
      bottom: media.height * 0.2,
      child: Container(
        padding: EdgeInsets.all(media.width * 0.025),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.transparent.withOpacity(0.6),
        ),
        child: MyText(
          text: languages[choosenLanguage]['text_code_copied'],
          size: media.width * twelve,
          color: topBar,
        ),
      ),
    );
  }

  Widget referralNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () {
          setState(
            () {
              internetTrue();
              _isLoading = true;
              getReferral();
            },
          );
        },
      ),
    );
  }
}
