import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import 'admin_chat_page.dart';
import 'faq.dart';

class SupportPage extends StatefulWidget {
  const SupportPage({super.key});

  @override
  State<SupportPage> createState() => _SupportPageState();
}

class _SupportPageState extends State<SupportPage> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              height: media.height * 1,
              width: media.width * 1,
              decoration: BoxDecoration(
                image: customBackground,
                color: page,
              ),
              child: Safe<PERSON>rea(
                child: Column(
                  children: [
                    supportAppBar(media),
                    const SizedBox(height: 20),
                    // supportAdminChatSection(media),
                    // const SizedBox(height: 10),
                    supportFAQSection(media),
                    const SizedBox(height: 10),
                    supportPrivacySection(media),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget supportAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_support'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget supportAdminChatSection(Size media) {
    return ValueListenableBuilder(
      valueListenable: valueNotifierChat.value,
      builder: (context, value, child) {
        return ShowUp(
          delay: 200,
          child: Material(
            elevation: 5,
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return const AdminChatPage();
                    },
                  ),
                );
              },
              child: Container(
                color: page,
                padding: EdgeInsets.all(media.width * 0.03),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.chat,
                          size: media.width * 0.07,
                          color: textColor.withOpacity(0.5),
                        ),
                        SizedBox(width: media.width * 0.025),
                        Expanded(
                          child: MyText(
                            text: languages[choosenLanguage]['text_chat_us'],
                            overflow: TextOverflow.ellipsis,
                            size: media.width * sixteen,
                            color: textColor.withOpacity(0.8),
                          ),
                        ),
                        Row(
                          children: [
                            (unSeenChatCount == '0')
                                ? Container()
                                : Container(
                                    height: 20,
                                    width: 20,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: buttonColor,
                                    ),
                                    child: Text(
                                      unSeenChatCount,
                                      style: GoogleFonts.notoSans(
                                          fontSize: media.width * fourteen,
                                          color: buttonText),
                                    ),
                                  ),
                            Icon(
                              Icons.arrow_right_rounded,
                              size: media.width * 0.05,
                              color: textColor.withOpacity(0.8),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget supportFAQSection(Size media) {
    return SubMenu(
      icon: Icons.warning_amber,
      text: languages[choosenLanguage]['text_faq'],
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const Faq(),
          ),
        );
      },
    );
  }

  Widget supportPrivacySection(Size media) {
    return SubMenu(
      onTap: () {
        openBrowser(''); // TODO: Add privacy policy link
      },
      text: languages[choosenLanguage]['text_privacy'],
      icon: Icons.privacy_tip_outlined,
    );
  }
}
