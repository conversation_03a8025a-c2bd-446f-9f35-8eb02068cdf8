import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/translations/translation.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';
import '../noInternet/nointernet.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  @override
  State<EditProfile> createState() => _EditProfileState();
}

dynamic imageFile;

class _EditProfileState extends State<EditProfile> {
  ImagePicker picker = ImagePicker();
  bool _isLoading = false;
  String _error = '';
  bool _pickImage = false;
  String _permission = '';
  TextEditingController name = TextEditingController();
  TextEditingController lastname = TextEditingController();
  TextEditingController email = TextEditingController();
  TextEditingController mobilenum = TextEditingController();
  bool islastname = false;

  //get gallery permission
  getGalleryPermission() async {
    dynamic status;
    if (platform == TargetPlatform.android) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        status = await Permission.storage.status;
        if (status != PermissionStatus.granted) {
          status = await Permission.storage.request();
        }

        /// use [Permissions.storage.status]
      } else {
        status = await Permission.photos.status;
        if (status != PermissionStatus.granted) {
          status = await Permission.photos.request();
        }
      }
    } else {
      status = await Permission.photos.status;
      if (status != PermissionStatus.granted) {
        status = await Permission.photos.request();
      }
    }
    return status;
  }

  bool isEdit = false;

  navigateLogout() {
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginView()),
        (route) => false,
      );
    });
  }

  //get camera permission
  getCameraPermission() async {
    var status = await Permission.camera.status;
    if (status != PermissionStatus.granted) {
      status = await Permission.camera.request();
    }
    return status;
  }

  //pick image from gallery
  pickImageFromGallery() async {
    var permission = await getGalleryPermission();
    if (permission == PermissionStatus.granted) {
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 50,
      );
      setState(() {
        proImageFile = pickedFile?.path;
        _pickImage = false;
      });
    } else {
      setState(() => _permission = 'noPhotos');
    }
  }

  //pick image from camera
  pickImageFromCamera() async {
    var permission = await getCameraPermission();
    if (permission == PermissionStatus.granted) {
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 50,
      );
      setState(() {
        proImageFile = pickedFile?.path;
        _pickImage = false;
      });
    } else {
      setState(() => _permission = 'noCamera');
    }
  }

  @override
  void initState() {
    _error = '';
    proImageFile = null;
    isEdit = false;
    name.text = userDetails['name'].toString().split(' ')[0];
    lastname.text = (userDetails['name'].toString().split(' ').length > 1)
        ? userDetails['name'].toString().split(' ')[1]
        : '';
    mobilenum.text = userDetails['mobile'];
    email.text = userDetails['email'];
    setState(() {});
    super.initState();
  }

  pop() {
    Navigator.pop(context, true);
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: Scaffold(
        body: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Container(
            decoration: BoxDecoration(
              color: page,
              image: customBackground,
            ),
            child: SafeArea(
              child: Stack(
                children: [
                  Container(
                    padding: EdgeInsets.all(media.width * 0.05),
                    child: Column(
                      children: [
                        profileAppBar(media),
                        const SizedBox(height: 20),
                        Expanded(
                          child: SingleChildScrollView(
                            child: profileDetails(media),
                          ),
                        ),
                        if (_error != '')
                          Container(
                            padding: EdgeInsets.only(
                              top: media.width * 0.02,
                            ),
                            child: MyText(
                              text: _error,
                              size: media.width * twelve,
                              color: Colors.red,
                            ),
                          ),
                        if (isEdit) editProfileButton(media),
                      ],
                    ),
                  ),
                  if (_pickImage == true) profilePickImagePopup(media),
                  if (_permission != '') profilePermissionPopup(media),
                  if (_isLoading == true) const Positioned(child: Loading()),
                  if (_error != '') profileError(media),
                  if (internet == false) profileNoInternet(media)
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget profileDetails(Size media) {
    return Column(
      children: [
        SizedBox(height: media.width * 0.05),
        InkWell(
          onTap: () {
            if (isEdit) {
              setState(() => _pickImage = true);
            }
          },
          child: Stack(
            children: [
              ShowUp(
                delay: 100,
                child: Container(
                  height: media.width * 0.25,
                  width: media.width * 0.25,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: page,
                    image: (proImageFile == null)
                        ? DecorationImage(
                            image: NetworkImage(
                              userDetails['profile_picture'],
                            ),
                            fit: BoxFit.cover,
                          )
                        : DecorationImage(
                            image: FileImage(File(proImageFile)),
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
              ),
              if (isEdit)
                Positioned(
                  right: media.width * 0.02,
                  bottom: media.width * 0.02,
                  child: Container(
                    height: media.width * 0.05,
                    width: media.width * 0.05,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xff898989),
                    ),
                    child: Icon(
                      Icons.edit,
                      color: topBar,
                      size: media.width * 0.04,
                    ),
                  ),
                ),
            ],
          ),
        ),
        SizedBox(height: media.width * 0.04),
        Row(
          children: [
            ProfileDetails(
              heading: languages[choosenLanguage]['text_name'],
              controller: name,
              width: media.width * 0.42,
              readyonly: (isEdit) ? false : true,
            ),
            SizedBox(width: media.width * 0.04),
            (lastname.text == '' && !isEdit)
                ? Container(
                    height: media.width * 0.13,
                    width: media.width * 0.4,
                    alignment: (languageDirection == 'rtl')
                        ? Alignment.centerRight
                        : Alignment.centerLeft,
                    decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: (isDarkTheme == true)
                                ? textColor.withOpacity(0.4)
                                : underline,
                          ),
                        ),
                        color: page),
                    padding: const EdgeInsets.only(
                      right: 5,
                    ),
                    child: MyText(
                      text: languages[choosenLanguage]['text_last_name'],
                      size: media.width * fourteen,
                      color: hintColor,
                    ),
                  )
                : ProfileDetails(
                    heading: '',
                    controller: lastname,
                    width: media.width * 0.42,
                    readyonly: (isEdit) ? false : true,
                  ),
          ],
        ),
        SizedBox(height: media.height * 0.02),
        if (!isEdit)
          ProfileDetails(
            heading: languages[choosenLanguage]['text_mob_num'],
            controller: mobilenum,
            readyonly: true,
          ),
        SizedBox(height: media.height * 0.02),
        ProfileDetails(
          heading: languages[choosenLanguage]['text_email'],
          controller: email,
          readyonly: (isEdit) ? false : true,
        ),
        SizedBox(height: media.width * 0.05),
      ],
    );
  }

  Widget editProfileButton(Size media) {
    return Button(
      onTap: () async {
        setState(() => _error = '');
        String pattern =
            r"^[A-Za-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[A-Za-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])?\.)+[A-Za-z0-9](?:[A-Za-z0-9-]*[A-Za-z0-9])*$";
        var remail = email.text.replaceAll(' ', '');
        RegExp regex = RegExp(pattern);
        if (regex.hasMatch(remail)) {
          setState(() => _isLoading = true);
          // ignore: prefer_typing_uninitialized_variables
          var nav;
          if (userDetails['email'] == remail) {
            // debugPrint('started');
            nav = await updateProfile(
              '${name.text} ${lastname.text}',
              remail,
              // userDetails['mobile']
            );
            if (nav != 'success') {
              _error = nav.toString();
            } else {
              // ignore: use_build_context_synchronously
              // Navigator.pop(context, true);
            }
          } else {
            var result = await validateEmail(remail);
            if (result == 'success') {
              nav = await updateProfile(
                '${name.text} ${lastname.text}',
                remail,
                // userDetails['mobile']
              );
              if (nav != 'success') {
                _error = nav.toString();
              } else {
                // ignore: use_build_context_synchronously
                // Navigator.pop(context, true);
              }
            } else {
              setState(() => _error = result);
            }

            setState(() => _isLoading = false);
          }
        } else {
          setState(() =>
              _error = languages[choosenLanguage]['text_email_validation']);
        }
      },
      text: languages[choosenLanguage]['text_confirm'],
    );
  }

  Widget profileError(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: media.width * 0.8,
                    child: MyText(
                      text: _error.toString(),
                      textAlign: TextAlign.center,
                      size: media.width * sixteen,
                      fontweight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: media.width * 0.05),
                  Button(
                    onTap: () async => setState(() => _error = ''),
                    text: languages[choosenLanguage]['text_ok'],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget profilePermissionPopup(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: media.width * 0.9,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        _permission = '';
                        _pickImage = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: page,
                      ),
                      child: Icon(
                        Icons.cancel_outlined,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(20),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2.0,
                    spreadRadius: 2.0,
                    color: Colors.black.withOpacity(0.2),
                  )
                ],
              ),
              child: Column(
                children: [
                  MyText(
                    text: (_permission == 'noPhotos')
                        ? languages[choosenLanguage]['text_open_photos_setting']
                        : languages[choosenLanguage]
                            ['text_open_camera_setting'],
                    size: 17,
                    fontweight: FontWeight.w600,
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () async => await openAppSettings(),
                        child: MyText(
                          text: languages[choosenLanguage]
                              ['text_open_settings'],
                          size: 17,
                          fontweight: FontWeight.w600,
                          color: buttonColor,
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          (_permission == 'noCamera')
                              ? pickImageFromCamera()
                              : pickImageFromGallery();
                          setState(() => _permission = '');
                        },
                        child: MyText(
                          text: languages[choosenLanguage]['text_done'],
                          size: 17,
                          fontweight: FontWeight.w600,
                          color: buttonColor,
                        ),
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget profilePickImagePopup(Size media) {
    return Positioned(
      bottom: 0,
      child: InkWell(
        onTap: () => setState(() => _pickImage = false),
        child: Container(
          height: media.height * 1,
          width: media.width * 1,
          color: Colors.transparent.withOpacity(0.6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  border: Border.all(
                    color: borderLines,
                    width: 1.2,
                  ),
                  color: page,
                ),
                child: Column(
                  children: [
                    Container(
                      height: 5,
                      width: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            InkWell(
                              onTap: () => pickImageFromCamera(),
                              child: Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: borderLines,
                                    width: 1.2,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.camera_alt_outlined,
                                  color: textColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            MyText(
                              text: languages[choosenLanguage]['text_camera'],
                              size: 11,
                              color: textColor.withOpacity(0.4),
                            )
                          ],
                        ),
                        Column(
                          children: [
                            InkWell(
                              onTap: () => pickImageFromGallery(),
                              child: Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: borderLines,
                                    width: 1.2,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.image_outlined,
                                  color: textColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            MyText(
                              text: languages[choosenLanguage]['text_gallery'],
                              size: media.width * ten,
                              color: textColor.withOpacity(0.4),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget profileNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }

  Widget profileAppBar(Size media) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (isEdit)
          InkWell(
            onTap: () {
              setState(() => isEdit = false);
            },
            child: Icon(Icons.arrow_back_ios, color: textColor),
          ),
        const Spacer(),
        MyText(
          textAlign: TextAlign.center,
          text: (!isEdit)
              ? languages[choosenLanguage]['text_personal_info']
              : languages[choosenLanguage]['text_editprofile'],
          size: 16,
          maxLines: 1,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
        if (!isEdit)
          InkWell(
            onTap: () => setState(() => isEdit = true),
            child: MyText(
              textAlign: TextAlign.end,
              maxLines: 1,
              text: languages[choosenLanguage]['text_edit'],
              color: buttonColor,
              size: 15,
              fontweight: FontWeight.w500,
            ),
          ),
      ],
    );
  }
}
