import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import '../../functions/functions.dart';
import 'dart:ui' as ui;
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';

class AdminChatPage extends StatefulWidget {
  const AdminChatPage({super.key});

  @override
  State<AdminChatPage> createState() => _AdminChatPageState();
}

String adminChatmessage = '';

class _AdminChatPageState extends State<AdminChatPage> {
  TextEditingController adminchatText = TextEditingController();
  ScrollController controller = ScrollController();
  bool _loading = false;

  @override
  void initState() {
    _loading = true;
    getmessage();
    super.initState();
  }

  getmessage() async {
    adminChatList.clear();
    if (chatid != null && unSeenChatCount != '0') {
      adminmessageseen();
    }
    var result = await getadminCurrentMessages();
    if (result == 'success') {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        unSeenChatCount = '0';
        if (chatid != null) {
          adminmessageseen();
        }
        // Navigator.pop(context, true);
        valueNotifierChat.incrementNotifier();
      },
      child: Scaffold(
        body: ValueListenableBuilder(
          valueListenable: valueNotifierChat.value,
          builder: (context, value, child) {
            WidgetsBinding.instance.addPostFrameCallback(
              (_) {
                controller.animateTo(
                  controller.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.ease,
                );
              },
            );
            //call for message seen
            // messageSeen();
            return Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? ui.TextDirection.rtl
                  : ui.TextDirection.ltr,
              child: Stack(
                children: [
                  Container(
                    padding: EdgeInsets.fromLTRB(
                      media.width * 0.05,
                      media.width * 0.05,
                      media.width * 0.05,
                      media.width * 0.05,
                    ),
                    height: media.height * 1,
                    width: media.width * 1,
                    decoration: BoxDecoration(
                      image: customBackground,
                      color: page,
                    ),
                    child: SafeArea(
                      child: Column(
                        children: [
                          adminChatAppBar(media),
                          const SizedBox(height: 20),
                          adminChatMessages(media),
                          adminChatTextField(media),
                        ],
                      ),
                    ),
                  ),
                  //loader
                  if (_loading == true)
                    const Positioned(top: 0, child: Loading())
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget adminChatAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            unSeenChatCount = '0';
            if (chatid != null && unSeenChatCount != '0') {
              adminmessageseen();
            }
            valueNotifierChat.incrementNotifier();
            Navigator.pop(context, true);
          },
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_admin_chat'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget adminChatMessages(Size media) {
    return Expanded(
      child: SingleChildScrollView(
        controller: controller,
        child: Column(
          children: adminChatList
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    Container(
                      padding: const EdgeInsets.only(top: 8),
                      width: media.width * 0.9,
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment:
                                (adminChatList[i]['from_id'].toString() ==
                                        userDetails['id'].toString())
                                    ? MainAxisAlignment.start
                                    : MainAxisAlignment.end,
                            children: [
                              Card(
                                elevation: 5,
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxWidth: media.width * 0.65,
                                  ),
                                  padding: EdgeInsets.all(media.width * 0.03),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topLeft: (adminChatList[i]['from_id']
                                                  .toString() ==
                                              userDetails['id'].toString())
                                          ? Radius.circular(media.width * 0.02)
                                          : const Radius.circular(0),
                                      topRight: (adminChatList[i]['from_id']
                                                  .toString() ==
                                              userDetails['id'].toString())
                                          ? const Radius.circular(0)
                                          : Radius.circular(media.width * 0.02),
                                      bottomRight:
                                          Radius.circular(media.width * 0.02),
                                      bottomLeft:
                                          Radius.circular(media.width * 0.02),
                                    ),
                                    color: (adminChatList[i]['from_id']
                                                .toString() ==
                                            userDetails['id'].toString())
                                        ? buttonColor
                                        : const Color(0xffE7EDEF),
                                  ),
                                  child: MyText(
                                    text: adminChatList[i]['message'],
                                    size: media.width * fourteen,
                                    color: textColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment:
                                (adminChatList[i]['from_id'].toString() ==
                                        userDetails['id'].toString())
                                    ? MainAxisAlignment.start
                                    : MainAxisAlignment.end,
                            children: [
                              MyText(
                                text: adminChatList[i]['user_timezone'],
                                size: media.width * ten,
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget adminChatTextField(Size media) {
    return Container(
      margin: EdgeInsets.only(top: media.width * 0.025),
      padding: EdgeInsets.fromLTRB(
        media.width * 0.025,
        media.width * 0.01,
        media.width * 0.025,
        media.width * 0.01,
      ),
      width: media.width * 0.9,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: borderLines, width: 1.2),
        color: page,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: TextField(
              controller: adminchatText,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: languages[choosenLanguage]['text_entermessage'],
                hintStyle: GoogleFonts.notoKufiArabic(
                  color: textColor.withOpacity(0.4),
                  fontSize: media.width * twelve,
                ),
              ),
              style: GoogleFonts.notoKufiArabic(color: textColor),
              minLines: 1,
              maxLines: 4,
              onChanged: (_) =>
                  setState(() => adminChatmessage = adminchatText.text),
            ),
          ),
          InkWell(
            onTap: () async {
              FocusManager.instance.primaryFocus?.unfocus();
              setState(() => _loading = true);
              adminchatText.clear();
              var val = await sendadminMessage(adminChatmessage);
              if (val == 'success') {
                setState(() => _loading = false);
              }
            },
            child: Image.asset(
              AppAssets.send,
              fit: BoxFit.contain,
              width: media.width * 0.075,
              color: textColor,
            ),
          )
        ],
      ),
    );
  }
}
