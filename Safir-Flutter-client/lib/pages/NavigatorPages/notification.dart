import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/pages/login/login_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  bool isLoading = true;
  bool error = false;
  dynamic notificationid;

  @override
  void initState() {
    getdata();
    super.initState();
  }

  getdata() async {
    var val = await getnotificationHistory();
    if (val == 'success') {
      isLoading = false;
    } else {
      isLoading = true;
    }
  }

  navigateLogout() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const LoginView()),
    );
  }

  bool showinfo = false;
  int? showinfovalue;
  bool showToastbool = false;

  showToast() async {
    setState(() => showToastbool = true);
    Future.delayed(
      const Duration(seconds: 1),
      () async => setState(() => showToastbool = false),
    );
  }

  // function to return sho load button or not
  bool showLoadButton() {
    if (notificationHistoryPage['pagination'] != null &&
        notificationHistoryPage['pagination']['current_page'] <
            notificationHistoryPage['pagination']['total_pages']) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: ValueListenableBuilder(
        valueListenable: valueNotifierHome.value,
        builder: (context, value, child) {
          return Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Stack(
              children: [
                Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  decoration: BoxDecoration(
                    image: customBackground,
                    color: page,
                  ),
                  padding: EdgeInsets.all(media.width * 0.05),
                  child: SafeArea(
                    child: Column(
                      children: [
                        notificationAppBar(media),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: Column(
                              children: [
                                (notificationHistory.isNotEmpty)
                                    ? Column(
                                        children: [
                                          notificationData(media),
                                          if (showLoadButton() == true)
                                            notificationLoadMoreButton(media),
                                        ],
                                      )
                                    : notificationEmptyData(media),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                if (showinfo == true) notificationShowInfo(media),
                if (error == true) notificationError(media),
                if (isLoading == true) const Positioned(child: Loading()),
                if (showToastbool == true) notificationShowToastBool(media)
              ],
            ),
          );
        },
      ),
    );
  }

  Widget notificationData(Size media) {
    return Column(
      children: notificationHistory
          .asMap()
          .map(
            (i, value) {
              return MapEntry(
                i,
                InkWell(
                  onTap: () {
                    setState(() {
                      showinfovalue = i;
                      showinfo = true;
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.only(
                        top: media.width * 0.02, bottom: media.width * 0.02),
                    width: media.width * 0.9,
                    padding: EdgeInsets.all(media.width * 0.025),
                    decoration: BoxDecoration(
                        border: Border.all(color: borderLines, width: 1.2),
                        borderRadius: BorderRadius.circular(12),
                        color: page),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                                height: media.width * 0.1067,
                                width: media.width * 0.1067,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: const Color(0xff000000).withOpacity(
                                    0.05,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: const Icon(Icons.notifications)),
                            SizedBox(
                              width: media.width * 0.025,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: media.width * 0.55,
                                  child: MyText(
                                    text: notificationHistory[i]['title']
                                        .toString(),
                                    size: media.width * fourteen,
                                    fontweight: FontWeight.w600,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(
                                  height: media.width * 0.01,
                                ),
                                SizedBox(
                                  width: media.width * 0.55,
                                  child: MyText(
                                    text: notificationHistory[i]['body']
                                        .toString(),
                                    size: media.width * twelve,
                                    color: hintColor,
                                  ),
                                ),
                                SizedBox(
                                  height: media.width * 0.01,
                                ),
                                SizedBox(
                                  width: media.width * 0.55,
                                  child: MyText(
                                    text: notificationHistory[i]
                                            ['converted_created_at']
                                        .toString(),
                                    size: media.width * twelve,
                                    fontweight: FontWeight.w600,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                    alignment: Alignment.centerRight,
                                    width: media.width * 0.15,
                                    child: IconButton(
                                      onPressed: () {
                                        setState(
                                          () {
                                            error = true;
                                            notificationid =
                                                notificationHistory[i]['id'];
                                          },
                                        );
                                      },
                                      icon: const Icon(Icons.delete_forever),
                                    ),
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                        SizedBox(height: media.width * 0.02),
                        if (notificationHistory[i]['image'] != null)
                          Image.network(
                            notificationHistory[i]['image'],
                            height: media.width * 0.1,
                            width: media.width * 0.8,
                            fit: BoxFit.contain,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            },
          )
          .values
          .toList(),
    );
  }

  Widget notificationLoadMoreButton(Size media) {
    return InkWell(
      onTap: () async {
        setState(() => isLoading = true);
        var val = await getNotificationPages(
          'page=${notificationHistoryPage['pagination']['current_page'] + 1}',
        );
        if (val == 'logout') {
          navigateLogout();
        }
        setState(() => isLoading = false);
      },
      child: Container(
        padding: EdgeInsets.all(media.width * 0.025),
        margin: EdgeInsets.only(bottom: media.width * 0.05),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: page,
          border: Border.all(color: borderLines, width: 1.2),
        ),
        child: Text(
          languages[choosenLanguage]['text_loadmore'],
          style: GoogleFonts.notoSans(
            fontSize: media.width * sixteen,
            color: textColor,
          ),
        ),
      ),
    );
  }

  Widget notificationEmptyData(Size media) {
    return SizedBox(
      height: media.height * 0.6,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: media.width * 0.2),
          Container(
            alignment: Alignment.center,
            height: media.width * 0.6,
            width: media.width * 0.6,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  (isDarkTheme) ? AppAssets.sosDark : AppAssets.sos,
                ),
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: media.width * 0.6,
            child: MyText(
              text: languages[choosenLanguage]['text_noDataFound'],
              textAlign: TextAlign.center,
              fontweight: FontWeight.w800,
              size: media.width * sixteen,
            ),
          ),
        ],
      ),
    );
  }

  Widget notificationAppBar(Size media) {
    return Row(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.arrow_back_ios, color: textColor),
        ),
        const Spacer(),
        MyText(
          text: languages[choosenLanguage]['text_notification'],
          size: 16,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
      ],
    );
  }

  Widget notificationShowInfo(Size media) {
    return Positioned(
        top: 0,
        child: Container(
          height: media.height * 1,
          width: media.width * 1,
          color: Colors.transparent.withOpacity(0.6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: media.width * 0.9,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        height: media.height * 0.1,
                        width: media.width * 0.1,
                        decoration:
                            BoxDecoration(shape: BoxShape.circle, color: page),
                        child: InkWell(
                            onTap: () {
                              setState(() {
                                showinfo = false;
                                showinfovalue = null;
                              });
                            },
                            child: const Icon(Icons.cancel_outlined))),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.all(media.width * 0.05),
                width: media.width * 0.9,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12), color: page),
                child: Column(
                  children: [
                    MyText(
                      text: notificationHistory[showinfovalue!]['title']
                          .toString(),
                      size: media.width * sixteen,
                      fontweight: FontWeight.w600,
                    ),
                    SizedBox(
                      height: media.width * 0.05,
                    ),
                    MyText(
                      text: notificationHistory[showinfovalue!]['body']
                          .toString(),
                      size: media.width * fourteen,
                      color: hintColor,
                    ),
                    SizedBox(
                      height: media.width * 0.05,
                    ),
                    if (notificationHistory[showinfovalue!]['image'] != null)
                      Image.network(
                        notificationHistory[showinfovalue!]['image'],
                        height: media.width * 0.4,
                        width: media.width * 0.4,
                        fit: BoxFit.contain,
                      )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget notificationError(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12), color: page),
              child: Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]
                        ['text_delete_notification'],
                    size: media.width * sixteen,
                    fontweight: FontWeight.w600,
                  ),
                  SizedBox(
                    height: media.width * 0.05,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Button(
                          onTap: () async {
                            setState(() {
                              error = false;
                              notificationid = null;
                            });
                          },
                          text: languages[choosenLanguage]['text_no']),
                      SizedBox(
                        width: media.width * 0.05,
                      ),
                      Button(
                          onTap: () async {
                            setState(() {
                              isLoading = true;
                            });
                            var result =
                                await deleteNotification(notificationid);
                            if (result == 'success') {
                              setState(() {
                                getdata();
                                error = false;
                                isLoading = false;
                                showToast();
                              });
                            }
                          },
                          text: languages[choosenLanguage]['text_yes']),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget notificationShowToastBool(Size media) {
    return Positioned(
      bottom: media.height * 0.2,
      left: media.width * 0.2,
      right: media.width * 0.2,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(media.width * 0.025),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.transparent.withOpacity(0.6)),
        child: MyText(
          text: languages[choosenLanguage]['text_notification_deleted'],
          size: media.width * twelve,
          color: topBar,
        ),
      ),
    );
  }
}
