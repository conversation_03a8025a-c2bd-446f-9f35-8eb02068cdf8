import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/pages/Navigation%20Bar/Presentation/Views/main_home_view.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';

class Referral extends StatefulWidget {
  const Referral({super.key});

  @override
  State<Referral> createState() => _ReferralState();
}

dynamic referralCode;

class _ReferralState extends State<Referral> {
  bool _loading = false;
  String _error = '';
  TextEditingController controller = TextEditingController();

  @override
  void initState() {
    referralCode = '';
    super.initState();
  }

  navigate() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const MainHomeView()),
    );
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;

    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              height: media.height * 1,
              width: media.width * 1,
              decoration: BoxDecoration(
                image: customBackground,
                color: page,
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    const SizedBox(height: 40),
                    SizedBox(
                      width: media.width * 1,
                      child: MyText(
                        text: languages[choosenLanguage]['text_apply_referral'],
                        size: media.width * twenty,
                        fontweight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    referralCodeInputField(media),
                    if (_error != '') referralCodeError(media),
                    const SizedBox(height: 40),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        referralCodeSkipButton(media),
                        referralCodeApplyButton(media)
                      ],
                    )
                  ],
                ),
              ),
            ),
            if (_loading == true) const Positioned(child: Loading())
          ],
        ),
      ),
    );
  }

  Widget referralCodeInputField(Size media) {
    return InputField(
      text: languages[choosenLanguage]['text_enter_referral'],
      textController: controller,
      onTap: (val) => setState(() => referralCode = controller.text),
      color: (_error == '') ? textColor : Colors.red,
      underline: textColor,
    );
  }

  Widget referralCodeError(Size media) {
    return Container(
      margin: EdgeInsets.only(top: media.height * 0.02),
      child: MyText(
        text: _error,
        size: media.width * sixteen,
        color: Colors.red,
      ),
    );
  }

  Widget referralCodeSkipButton(Size media) {
    return Button(
      onTap: () async {
        setState(() => _loading = true);
        FocusManager.instance.primaryFocus?.unfocus();
        _error = '';
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const MainHomeView()),
        );
        setState(() => _loading = false);
      },
      text: languages[choosenLanguage]['text_skip'],
    );
  }

  Widget referralCodeApplyButton(Size media) {
    return Button(
      onTap: () async {
        if (controller.text.isNotEmpty) {
          FocusManager.instance.primaryFocus?.unfocus();
          setState(() {
            _error = '';
            _loading = true;
          });

          var result = await updateReferral();
          if (result == 'true') {
            navigate();
          } else {
            setState(() {
              _error = languages[choosenLanguage]['text_referral_code'];
            });
          }
          setState(() => _loading = false);
        }
      },
      text: languages[choosenLanguage]['text_apply'],
      color: (controller.text.isNotEmpty) ? buttonColor : Colors.grey,
    );
  }
}
