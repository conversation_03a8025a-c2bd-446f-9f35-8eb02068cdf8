import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:safir_client/Core/databases/api/api_consumer.dart';
import 'package:safir_client/Core/databases/api/end_points.dart';
import 'package:safir_client/Core/errors/exceptions.dart';
import 'package:safir_client/Core/errors/failure.dart';
import 'package:safir_client/pages/Orders/Data/Models/order_model/actions.dart';
import 'package:safir_client/pages/Orders/Data/Models/order_model/list_action.dart';
import 'package:safir_client/pages/Orders/Data/Models/order_model/order_model.dart';
import 'package:safir_client/pages/Orders/Data/Repos/orders_repo.dart';

class OrdersRepoImpl implements OrdersRepo {
  final ApiConsumer apiConsumer;

  OrdersRepoImpl({required this.apiConsumer});

  @override
  Future<Either<Failure, List<OrderModel>>> fetchOrders() async {
    try {
      var data = await apiConsumer.get(EndPoints.orders);
      List<OrderModel> orders = [];
      for (var item in data['data']) {
        orders.add(OrderModel.fromJson(item));
      }
      return right(orders);
    } catch (e) {
      OrderModel order = OrderModel(
        id: 1,
        name: "Fady Shehata",
        image: "https://www.w3schools.com/w3images/avatar2.png",
        status: "Pending",
        title: "التركيبات الكهربائية",
        description: "تركيب مصابيح ومفاتيح",
        price: 200,
        actions: Actions(
          listActions: [
            ListAction(
              id: 1,
              title: "قبول الطلب",
              textColor: "#FFFFFF",
              backgroundColor: "#4CAF50",
            ),
            ListAction(
              id: 2,
              title: "رفض الطلب",
              textColor: "#FFFFFF",
              backgroundColor: "#f44336",
            ),
          ],
          url: "https://www.w3schools.com/w3images/avatar2.png",
        ),
      );
      return Right([order, order, order, order, order]);
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }
}
