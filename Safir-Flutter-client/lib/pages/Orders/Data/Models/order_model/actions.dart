import 'list_action.dart';

class Actions {
  List<ListAction>? listActions;
  String? url;

  Actions({this.listActions, this.url});

  @override
  String toString() => 'Actions(listActions: $listActions, url: $url)';

  factory Actions.fromJson(Map<String, dynamic> json) => Actions(
        listActions: (json['list_actions'] as List<dynamic>?)
            ?.map((e) => ListAction.fromJson(e as Map<String, dynamic>))
            .toList(),
        url: json['url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'list_actions': listActions?.map((e) => e.toJson()).toList(),
        'url': url,
      };
}
