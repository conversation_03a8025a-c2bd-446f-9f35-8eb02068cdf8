import 'actions.dart';

class OrderModel {
  int? id;
  String? name;
  String? image;
  String? title;
  String? description;
  int? price;
  String? status;
  Actions? actions;

  OrderModel({
    this.id,
    this.name,
    this.image,
    this.title,
    this.description,
    this.price,
    this.status,
    this.actions,
  });

  @override
  String toString() {
    return 'OrderModel(id: $id, name: $name, image: $image, title: $title, description: $description, price: $price, status: $status, actions: $actions)';
  }

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        id: json['id'] as int?,
        name: json['name'] as String?,
        image: json['image'] as String?,
        title: json['title'] as String?,
        description: json['description'] as String?,
        price: json['price'] as int?,
        status: json['status'] as String?,
        actions: json['actions'] == null
            ? null
            : Actions.fromJson(json['actions'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'image': image,
        'title': title,
        'description': description,
        'price': price,
        'status': status,
        'actions': actions?.toJson(),
      };
}
