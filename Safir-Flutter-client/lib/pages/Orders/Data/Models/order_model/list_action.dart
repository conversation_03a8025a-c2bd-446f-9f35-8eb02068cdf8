class ListAction {
  int? id;
  String? title;
  String? textColor;
  String? backgroundColor;

  ListAction({this.id, this.title, this.textColor, this.backgroundColor});

  @override
  String toString() {
    return 'ListAction(id: $id, title: $title, textColor: $textColor, backgroundColor: $backgroundColor)';
  }

  factory ListAction.fromJson(Map<String, dynamic> json) => ListAction(
        id: json['id'] as int?,
        title: json['title'] as String?,
        textColor: json['text_color'] as String?,
        backgroundColor: json['background_color'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'text_color': textColor,
        'background_color': backgroundColor,
      };
}
