import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:safir_client/pages/Orders/Data/Models/order_model/order_model.dart';
import 'package:safir_client/pages/Orders/Data/Repos/orders_repo.dart';

part 'orders_state.dart';

class OrdersCubit extends Cubit<OrdersState> {
  OrdersCubit({required this.ordersRepo}) : super(OrdersInitial());

  final OrdersRepo ordersRepo;

  Future<void> fetchOrders() async {
    emit(FetchOrdersLoadingState());
    final orders = await ordersRepo.fetchOrders();

    orders.fold(
      (failure) =>
          emit(FetchOrdersFailureState(errMessage: failure.toString())),
      (orders) => emit(FetchOrdersSuccessState(orders: orders)),
    );
  }
}
