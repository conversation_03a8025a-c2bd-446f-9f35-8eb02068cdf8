part of 'orders_cubit.dart';

sealed class OrdersState extends Equatable {
  const OrdersState();

  @override
  List<Object> get props => [];
}

final class OrdersInitial extends OrdersState {}

final class FetchOrdersLoadingState extends OrdersState {}

final class FetchOrdersSuccessState extends OrdersState {
  final List<OrderModel> orders;

  const FetchOrdersSuccessState({required this.orders});

  @override
  List<Object> get props => [orders];
}

final class FetchOrdersFailureState extends OrdersState {
  final String errMessage;

  const FetchOrdersFailureState({required this.errMessage});

  @override
  List<Object> get props => [errMessage];
}
