import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/databases/api/dio_consumer.dart';
import 'package:safir_client/Core/services/service_locator.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/widgets/custom_failure_widget.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/pages/Orders/Data/Repos/orders_repo_impl.dart';
import 'package:safir_client/pages/Orders/Presentation/Views/Widgets/order_item.dart';
import 'package:safir_client/pages/Orders/Presentation/Views/Widgets/shimmer_loading_order.dart';
import 'package:safir_client/pages/Orders/Presentation/manager/orders_cubit/orders_cubit.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/widgets/widgets.dart';

class OrdersView extends StatelessWidget {
  const OrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: BlocProvider(
              create: (context) => OrdersCubit(
                ordersRepo: OrdersRepoImpl(
                  apiConsumer: getIt.get<DioConsumer>(),
                ),
              )..fetchOrders(),
              child: const OrdersViewBody(),
            ),
          ),
        ),
      ),
    );
  }
}

class OrdersViewBody extends StatelessWidget {
  const OrdersViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: Icon(Icons.arrow_back_ios, color: textColor),
              ),
              const Spacer(),
              const MyText(
                text: 'طلباتي',
                size: 16,
                fontweight: FontWeight.bold,
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BlocBuilder<OrdersCubit, OrdersState>(
              builder: (context, state) {
                if (state is FetchOrdersLoadingState) {
                  return ListView.separated(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    separatorBuilder: (_, __) => const SizedBox(height: 16),
                    itemCount: 5,
                    shrinkWrap: true,
                    itemBuilder: (_, __) => const ShimmerLoadingOrder(),
                  );
                } else if (state is FetchOrdersFailureState) {
                  return CustomFailureWidget(errMessage: state.errMessage);
                } else if (state is FetchOrdersSuccessState) {
                  return ListView.separated(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    separatorBuilder: (_, __) => const SizedBox(height: 16),
                    itemCount: state.orders.length,
                    itemBuilder: (context, index) {
                      return OrderItem(order: state.orders[index]);
                    },
                  );
                }
                return Container();
              },
            ),
          )
        ],
      ),
    );
  }
}
