import 'package:flutter/material.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/pages/Orders/Data/Models/order_model/order_model.dart';
import 'package:safir_client/styles/styles.dart';

class OrderItem extends StatelessWidget {
  const OrderItem({super.key, required this.order});

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: hintColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundImage: NetworkImage(order.image ?? ''),
              ),
              const SizedBox(width: 16),
              Text(
                order.name ?? '',
                style: AppTextStyles.font18Bold,
              ),
              const Spacer(),
              Text(
                '${order.price} EGP',
                style: AppTextStyles.font16SemiBold,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            order.title ?? '',
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 4),
          Text(
            order.description ?? '',
            style: AppTextStyles.font16Regular,
          ),
        ],
      ),
    );
  }
}
