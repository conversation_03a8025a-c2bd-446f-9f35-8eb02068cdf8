import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/translations/translation.dart';
import 'package:safir_client/widgets/widgets.dart';

class AirView extends StatelessWidget {
  const AirView({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Container(
          decoration: BoxDecoration(
            image: customBackground,
            color: page,
          ),
          child: Center(
            child: MyText(
              text: languages[choosenLanguage]['text_under_development'],
              size: media.width * fourteen,
              fontweight: FontWeight.bold,
              color: buttonText,
            ),
          ),
        ),
      ),
    );
  }
}
