import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import '../../styles/styles.dart';
import '../../functions/functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:math' as math;
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../noInternet/nointernet.dart';
import '../onTripPage/invoice.dart';
import '../onTripPage/map_page.dart';
import 'login_view.dart';
import 'fill_user_info_view.dart';

class OtpView extends StatefulWidget {
  final dynamic from;

  const OtpView({super.key, this.from});

  @override
  State<OtpView> createState() => _OtpViewState();
}

String otpNumber = ''; //otp number

class _OtpViewState extends State<OtpView> with TickerProviderStateMixin {
  final _pinPutController2 = TextEditingController();
  dynamic aController;
  bool _resend = false;
  String _error = '';

  String get timerString {
    Duration duration = aController.duration * aController.value;
    return '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
  }

  @override
  void initState() {
    aController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 60),
    );
    aController.reverse(
      from: aController.value == 0.0 ? 60.0 : aController.value,
    );
    otpFalse();
    super.initState();
  }

  @override
  void dispose() {
    _error = '';
    aController.dispose();
    super.dispose();
  }

//navigate
  navigate(verify) {
    if (verify == true) {
      if (userRequestData.isNotEmpty && userRequestData['is_completed'] == 1) {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const Invoice()),
            (route) => false);
      } else {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const Maps()),
            (route) => false);
      }
    } else if (verify == false) {
      if (isverifyemail == true) {
        currentPage = 3;
        valueNotifierLogin.incrementNotifier();
      } else {
        setState(() {
          currentPage = 2;
        });
        valueNotifierLogin.incrementNotifier();
      }
    } else {
      _error = verify.toString();
    }
    loginLoading = false;
    valueNotifierLogin.incrementNotifier();
  }

  otpFalse() async {
    if (phoneAuthCheck == false) {
      if (isverifyemail == false) {
        _pinPutController2.text = '123456';
        otpNumber = _pinPutController2.text;
        normallogin();
      } else {
        emaillogin();
      }
    }
  }

  normallogin() async {
    values = 0;

    var verify = await verifyUser(phnumber);
    navigate(verify);
  }

  emaillogin() async {
    values = 1;

    var verify = await verifyUser(phnumber);
    // var register = await registerUser();
    if (verify == false) {
      _pinPutController2.text = '123456';
      otpNumber = _pinPutController2.text;
      //referral page
      navigate(verify);
    } else {
      setState(() {
        _pinPutController2.text = '';
        _error = languages[choosenLanguage]['text_mobile_already_taken'];
      });
    }
  }

//auto verify otp

  verifyOtp() async {
    FocusManager.instance.primaryFocus?.unfocus();
    try {
      // Sign the user in (or link) with the credential
      await FirebaseAuth.instance.signInWithCredential(credentials);

      var verify = await verifyUser(phnumber);
      credentials = null;
      navigate(verify);
    } on FirebaseAuthException catch (error) {
      if (error.code == 'invalid-verification-code') {
        setState(() {
          _pinPutController2.clear();
          _error = languages[choosenLanguage]['text_otp_error'];
        });
      }
    }
  }

  showToast() {
    setState(() => showtoast = true);
    Future.delayed(const Duration(seconds: 4), () {
      setState(() => showtoast = false);
    });
  }

  bool showtoast = false;

  Map<String, String> separateSignAndNumber(String input) {
    RegExp regex = RegExp(
        r'([+-]?)(\d+)'); // Regular expression to match optional sign followed by digits

    // Use the firstMatch method to find the match
    Match? match = regex.firstMatch(input);

    // Create a map to hold the separated parts
    Map<String, String> result = {
      'sign': '',
      'number': '',
    };

    // Check if there's a match
    if (match != null) {
      result['sign'] = match.group(1)!; // Extract the sign
      result['number'] = match.group(2)!; // Extract the number
    }

    return result;
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: AnimatedBuilder(
                  animation: aController,
                  builder: (context, child) {
                    if (timerString == "0:00") {
                      _resend = true;
                    }
                    return SingleChildScrollView(
                      child: Column(
                        children: [
                          otpTimer(media),
                          const SizedBox(height: 20),
                          (isfromomobile == true)
                              ? enterPhoneOtpTitle(media)
                              : enterEmailOtpTitle(media),
                          const SizedBox(height: 20),
                          otpPinput(media),
                          const SizedBox(height: 20),
                          resendOtp(media),
                        ],
                      ),
                    );
                  },
                ),
              ),
              if (_error != '') otpError(media),
              (isfromomobile == true)
                  ? phoneVerifyButton(media)
                  : emailVerifyButton(media),
              const SizedBox(height: 25)
            ],
          ),
          if (internet == false) otpNoInternet(),
          if (showtoast == true) otpErrorToast(media),
        ],
      ),
    );
  }

  Widget enterEmailOtpTitle(Size media) {
    return SizedBox(
      child: MyText(
        // ignore: prefer_interpolation_to_compose_strings
        text: languages[choosenLanguage]['text_enter_otp_at'] + email,
        size: media.width * twenty,
        fontweight: FontWeight.bold,
      ),
    );
  }

  Widget enterPhoneOtpTitle(Size media) {
    Map<String, String> signAndNumber =
        separateSignAndNumber(countries[phcode]['dial_code']);
    return SizedBox(
      child: Column(
        children: [
          MyText(
            // ignore: prefer_interpolation_to_compose_strings
            text: languages[choosenLanguage]['text_enter_otp_at'],
            size: media.width * twenty,
            fontweight: FontWeight.bold,
          ),
          Directionality(
            textDirection: TextDirection.ltr,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                MyText(
                  text: signAndNumber['sign'],
                  size: media.width * twenty,
                  fontweight: FontWeight.bold,
                ),
                MyText(
                  text: '${signAndNumber['number']} $phnumber',
                  size: media.width * twenty,
                  fontweight: FontWeight.bold,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget otpTimer(Size media) {
    return Container(
      width: media.width * 0.9,
      alignment: Alignment.center,
      child: SizedBox(
        height: 55,
        width: 55,
        child: CustomPaint(
          // ignore: sort_child_properties_last
          child: Center(
            child: MyText(
              text: timerString,
              size: media.width * fourteen,
              fontweight: FontWeight.bold,
            ),
          ),
          painter: CustomTimerPainter(
            animation: aController,
            backgroundColor: buttonColor,
            color: const Color(0xffEDF0F4),
          ),
        ),
      ),
    );
  }

  Widget otpPinput(Size media) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Pinput(
        length: 6,
        onChanged: (_) => otpNumber = _pinPutController2.text,
        controller: _pinPutController2,
        defaultPinTheme: PinTheme(
          width: media.width * 0.15,
          height: media.width * 0.15,
          textStyle: GoogleFonts.notoKufiArabic(
            fontSize: media.width * twenty,
            fontWeight: FontWeight.w700,
            color: textColor,
          ),
          decoration: BoxDecoration(
            color: hintColor,
          ),
        ),
      ),
    );
  }

  Widget resendOtp(Size media) {
    return GestureDetector(
      onTap: () async {
        if (timerString == "0:00") {
          loginLoading = true;
          valueNotifierLogin.incrementNotifier();
          if (isfromomobile == true) {
            phoneAuthCheck = true;

            phoneAuth(countries[phcode]['dial_code'] + phnumber);
            aController.reverse(
              from: aController.value == 0.0 ? 60.0 : aController.value,
            );
            setState(
              () {
                _error = '';
                _pinPutController2.text = '';
                _resend = false;

                loginLoading = false;
              },
            );
          } else {
            phoneAuthCheck = true;

            await sendOTPtoEmail(email);
            aController.reverse(
              from: aController.value == 0.0 ? 60.0 : aController.value,
            );

            setState(() {
              _error = '';
              _pinPutController2.text = '';
              _resend = false;

              loginLoading = false;
            });
          }
          // var register = await registerUser();
        }
        loginLoading = false;
        valueNotifierLogin.incrementNotifier();
      },
      child: MyText(
        text: languages[choosenLanguage]['text_resend_otp'],
        size: media.width * sixteen,
        color: (_resend == false) ? buttonColor.withOpacity(0.4) : buttonColor,
      ),
    );
  }

  Widget otpErrorToast(Size media) {
    return Positioned(
      bottom: media.width * 0.1,
      left: media.width * 0.06,
      right: media.width * 0.06,
      child: Container(
        padding: EdgeInsets.all(media.width * 0.04),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              blurRadius: 2.0,
              spreadRadius: 2.0,
              color: Colors.black.withOpacity(0.2),
            )
          ],
          color: verifyDeclined,
        ),
        child: MyText(
          text: _error,
          size: media.width * fourteen,
          color: textColor,
          fontweight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget otpError(Size media) {
    return Column(
      children: [
        SizedBox(
          width: media.width * 0.9,
          child: MyText(
            text: _error,
            color: Colors.red,
            size: media.width * fourteen,
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: media.width * 0.025)
      ],
    );
  }

  Widget phoneVerifyButton(Size media) {
    return Container(
      alignment: Alignment.center,
      child: Button(
        color: buttonColor,
        textcolor: buttonText,
        onTap: () async {
          if (_pinPutController2.length == 6) {
            setState(() => _error = '');
            loginLoading = true;

            valueNotifierLogin.incrementNotifier();
            if (isCheckFireBaseOTP == false) {
              var val = await validateSmsOtp(phnumber, _pinPutController2.text);
              if (val == 'success') {
                values = 0;
                var verify = await verifyUser(phnumber);
                navigate(verify);
              } else {
                setState(() {
                  _pinPutController2.clear();
                  otpNumber = '';
                  _error = languages[choosenLanguage]['text_otp_error'];
                });
              }
            } else {
              //firebase code send false
              if (phoneAuthCheck == false) {
                values = 0;
                var verify = await verifyUser(phnumber);
                navigate(verify);
              } else {
                // firebase code send true
                try {
                  PhoneAuthCredential credential = PhoneAuthProvider.credential(
                      verificationId: verId, smsCode: otpNumber);

                  // Sign the user in (or link) with the credential
                  await FirebaseAuth.instance.signInWithCredential(credential);
                  values = 0;
                  var verify = await verifyUser(phnumber);
                  navigate(verify);
                } on FirebaseAuthException catch (error) {
                  if (error.code == 'invalid-verification-code') {
                    setState(
                      () {
                        _pinPutController2.clear();
                        otpNumber = '';
                        _error = languages[choosenLanguage]['text_otp_error'];
                      },
                    );
                  }
                }
              }
            }
            loginLoading = false;
            valueNotifierLogin.incrementNotifier();
          }
        },
        text: languages[choosenLanguage]['text_verify'],
      ),
    );
  }

  Widget emailVerifyButton(Size media) {
    return Container(
      alignment: Alignment.center,
      child: Button(
        onTap: () async {
          if (_pinPutController2.length == 6) {
            setState(() => _error = '');
            loginLoading = true;
            valueNotifierLogin.incrementNotifier();
            var result = await emailVerify(email, otpNumber);

            if (result == 'success') {
              isfromomobile = false;
              _error = '';
              var verify = await verifyUser(email);
              values = 1;
              navigate(verify);
            } else {
              setState(
                () {
                  _pinPutController2.clear();
                  otpNumber = '';
                  _error = languages[choosenLanguage]['text_otp_error'];
                },
              );
            }
          }
          loginLoading = false;
          valueNotifierLogin.incrementNotifier();
        },
        text: languages[choosenLanguage]['text_verify'],
      ),
    );
  }

  Widget otpNoInternet() {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}

class CustomTimerPainter extends CustomPainter {
  CustomTimerPainter({
    required this.animation,
    required this.backgroundColor,
    required this.color,
  }) : super(repaint: animation);

  final Animation<double> animation;
  final Color backgroundColor, color;

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = backgroundColor
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.butt
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(size.center(Offset.zero), size.width / 2.0, paint);
    paint.color = color;
    double progress = (1.0 - animation.value) * 2 * math.pi;
    canvas.drawArc(Offset.zero & size, math.pi * 1.5, -progress, false, paint);
  }

  @override
  bool shouldRepaint(CustomTimerPainter oldDelegate) {
    return animation.value != oldDelegate.animation.value ||
        color != oldDelegate.color ||
        backgroundColor != oldDelegate.backgroundColor;
  }
}
