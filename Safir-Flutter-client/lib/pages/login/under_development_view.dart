import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/translations/translation.dart';
import 'package:safir_client/widgets/widgets.dart';

class UnderDevelopmentView extends StatelessWidget {
  const UnderDevelopmentView({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Container(
          decoration: BoxDecoration(
            image: customBackground,
            color: page,
          ),
          child: SafeArea(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: InkWell(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                blurRadius: 2,
                                color: Colors.black.withOpacity(0.2),
                                spreadRadius: 2,
                              )
                            ],
                            color: page,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                MyText(
                  text: languages[choosenLanguage]['text_under_development'],
                  size: media.width * sixteen,
                  fontweight: FontWeight.bold,
                  color: buttonText,
                ),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
