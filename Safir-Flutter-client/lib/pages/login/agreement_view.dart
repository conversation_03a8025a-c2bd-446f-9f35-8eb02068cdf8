import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../referralcode/referral_code.dart';
import 'login_view.dart';

class AgreementView extends StatefulWidget {
  const AgreementView({super.key});

  @override
  State<AgreementView> createState() => _AgreementViewState();
}

class _AgreementViewState extends State<AgreementView> {
  //navigate
  navigate() {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const Referral()),
      (route) => false,
    );
  }

  bool ischeck = false;

  // ignore: unused_field
  String _error = '';

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;

    return Material(
      color: Colors.transparent,
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    Container(
                      width: media.width * 0.9,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      child: MyText(
                        text: languages[choosenLanguage]['text_accept_head'],
                        size: media.width * eighteen,
                        fontweight: FontWeight.bold,
                      ),
                    ),
                    agreementImage(media),
                    const SizedBox(height: 20),
                    agreementDescription(media),
                    agreeSection(media),
                  ],
                ),
              ),
            ),
            if (_error != '') agreementError(media),
            (ischeck == true)
                ? confirmEnableButton(media)
                : confirmDisableButton(media),
          ],
        ),
      ),
    );
  }

  Widget agreementDescription(Size media) {
    return SizedBox(
      width: media.width * 0.9,
      child: RichText(
        text: TextSpan(
          style: GoogleFonts.notoKufiArabic(
            color: textColor,
            fontSize: media.width * fourteen,
          ),
          children: [
            TextSpan(
              text: languages[choosenLanguage]['text_agree_text1'],
            ),
            TextSpan(
              text: languages[choosenLanguage]['text_terms_of_use'],
              style: GoogleFonts.notoKufiArabic(
                color: buttonColor,
                fontSize: media.width * fourteen,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  openBrowser('your terms and condition url here');
                },
            ),
            TextSpan(
              text: languages[choosenLanguage]['text_agree_text2'],
            ),
            TextSpan(
              text: languages[choosenLanguage]['text_privacy'],
              style: GoogleFonts.notoKufiArabic(
                color: buttonColor,
                fontSize: media.width * fourteen,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  openBrowser('your privacy policy url here');
                },
            ),
          ],
        ),
      ),
    );
  }

  Widget confirmEnableButton(Size media) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Button(
        color: buttonColor,
        textcolor: buttonText,
        onTap: () async {
          loginLoading = true;
          _error = '';
          valueNotifierLogin.incrementNotifier();
          var register = await registerUser();
          if (register == 'true') {
            //referral page
            navigate();
          } else {
            setState(() => _error = register.toString());
          }
          loginLoading = false;
          valueNotifierLogin.incrementNotifier();
        },
        text: languages[choosenLanguage]['text_next'],
      ),
    );
  }

  Widget confirmDisableButton(Size media) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Button(
        onTap: () async {},
        text: languages[choosenLanguage]['text_next'],
        color: Colors.grey,
        textcolor: textColor.withOpacity(0.5),
      ),
    );
  }

  Widget agreeSection(Size media) {
    return Container(
      padding: const EdgeInsets.only(top: 15, bottom: 15),
      child: Row(
        children: [
          SizedBox(width: media.width * 0.025),
          MyText(
            text: languages[choosenLanguage]['text_iagree'],
            size: media.width * sixteen,
          ),
          const SizedBox(width: 16),
          InkWell(
            onTap: () => setState(() => ischeck = !ischeck),
            child: Container(
              height: media.width * 0.05,
              width: media.width * 0.05,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(
                  color: buttonColor,
                  width: 2,
                ),
              ),
              child: ischeck == false
                  ? null
                  : Icon(
                      Icons.done,
                      size: media.width * 0.04,
                      color: buttonColor,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget agreementError(Size media) {
    return SizedBox(
      width: media.width * 0.9,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          MyText(
            text: _error,
            size: media.width * fourteen,
            color: verifyDeclined,
          ),
        ],
      ),
    );
  }

  Widget agreementImage(Size media) {
    return Container(
      height: media.width * 0.416,
      width: media.width * 0.416,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppAssets.privacy),
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
