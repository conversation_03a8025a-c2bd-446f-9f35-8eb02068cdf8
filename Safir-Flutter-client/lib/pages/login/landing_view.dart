import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/pages/onTripPage/map_page.dart';
import 'package:safir_client/translations/translation.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../widgets/widgets.dart';
import 'air_view.dart';
import 'sea_view.dart';

class LandingView extends StatefulWidget {
  const LandingView({super.key});

  @override
  State<LandingView> createState() => _LandingViewState();
}

class _LandingViewState extends State<LandingView> {
  bool colorbutton = false;

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return Material(
      child: Directionality(
        textDirection: (languageDirection == 'rtl')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Container(
          decoration: BoxDecoration(
            image: customBackground,
            color: page,
          ),
          child: Column(
            children: [
              Expanded(child: landingImage(media)),
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 60),
                    child: Column(
                      children: [
                        loginLandButton(media),
                        const SizedBox(height: 30),
                        loginSeaButton(media),
                        const SizedBox(height: 30),
                        loginAirButton(media),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget landingImage(Size media) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppAssets.logoWithLights),
        ),
      ),
    );
  }

  Widget loginAirButton(Size media) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AirView()),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(media.width * thirty),
          color: buttonColor,
          boxShadow: [
            BoxShadow(
              color: buttonColor,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(
              text: languages[choosenLanguage]['text_air'],
              size: media.width * twentyfour,
              fontweight: FontWeight.w500,
              color: buttonText,
            ),
            // const SizedBox(width: 20),
            ImageIcon(
              const AssetImage(AppAssets.airIcon),
              color: buttonText,
              size: media.width * fourty,
            ),
          ],
        ),
      ),
    );
  }

  Widget loginLandButton(Size media) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const Maps()),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(media.width * thirty),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: buttonColor,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(
              text: languages[choosenLanguage]['text_land'],
              size: media.width * twentyfour,
              fontweight: FontWeight.w500,
              color: theme,
            ),
            ImageIcon(
              const AssetImage(AppAssets.landIcon),
              color: theme,
              size: media.width * fourty,
            )
          ],
        ),
      ),
    );
  }

  Widget loginSeaButton(Size media) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SeaView()),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(media.width * thirty),
          color: buttonColor,
          boxShadow: [
            BoxShadow(
              color: buttonColor,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(
              text: languages[choosenLanguage]['text_sea'],
              size: media.width * twentyfour,
              fontweight: FontWeight.w500,
              color: buttonText,
            ),
            ImageIcon(
              const AssetImage(AppAssets.seaIcon),
              color: buttonText,
              size: media.width * fourty,
            ),
          ],
        ),
      ),
    );
  }
}
