import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/pages/under_development/under_development_page.dart';

class BookingServicePage extends StatelessWidget {
  const BookingServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'خدمة الحجوزات',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service banner
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.teal,
                                Colors.teal.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.event_available,
                                size: 60,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'خدمة الحجوزات الذكية',
                                style: AppTextStyles.font24Bold.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'احجز مواعيدك بسهولة وبدون عناء',
                                style: AppTextStyles.font16Medium.copyWith(
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Booking types
                        Text(
                          'أنواع الحجوزات المتاحة:',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        _buildBookingTypeCard(
                          icon: Icons.medical_services,
                          title: 'المواعيد الطبية',
                          description: 'احجز موعدك مع أفضل الأطباء',
                          color: Colors.red,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildBookingTypeCard(
                          icon: Icons.restaurant,
                          title: 'المطاعم',
                          description: 'احجز طاولتك في أفضل المطاعم',
                          color: Colors.orange,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildBookingTypeCard(
                          icon: Icons.fitness_center,
                          title: 'الصالات الرياضية',
                          description: 'احجز جلستك التدريبية',
                          color: Colors.green,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildBookingTypeCard(
                          icon: Icons.content_cut,
                          title: 'صالونات التجميل',
                          description: 'احجز موعدك للعناية والتجميل',
                          color: Colors.pink,
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Features
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مميزات الخدمة:',
                                style: AppTextStyles.font18SemiBold.copyWith(
                                  color: textColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildFeatureRow(
                                icon: Icons.schedule,
                                text: 'حجز فوري ومؤكد',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.notifications,
                                text: 'تذكير قبل الموعد',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.cancel,
                                text: 'إلغاء مجاني',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.star_rate,
                                text: 'تقييم الخدمات',
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // CTA Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const UnderDevelopmentPage(
                                    featureName: 'خدمة الحجوزات',
                                    description: 'نعمل على تطوير نظام حجوزات متطور لتسهيل حجز مواعيدك',
                                  ),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.teal,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'احجز الآن',
                              style: AppTextStyles.font18SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildBookingTypeCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: AppTextStyles.font14Medium.copyWith(
                    color: textColor.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: textColor.withOpacity(0.3),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureRow({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.teal,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.font14Medium.copyWith(
              color: textColor.withOpacity(0.8),
            ),
          ),
        ),
      ],
    );
  }
}
