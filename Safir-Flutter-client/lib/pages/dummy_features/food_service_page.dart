import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/pages/under_development/under_development_page.dart';

class FoodServicePage extends StatelessWidget {
  const FoodServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'خدمات الطعام',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service banner
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.deepOrange,
                                Colors.deepOrange.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.restaurant,
                                size: 60,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'خدمات الطعام والمطاعم',
                                style: AppTextStyles.font24Bold.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'اطلب طعامك المفضل من أفضل المطاعم',
                                style: AppTextStyles.font16Medium.copyWith(
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Restaurant categories
                        Text(
                          'أنواع المطاعم المتاحة:',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 1.2,
                          children: [
                            _buildRestaurantCard(
                              icon: Icons.local_pizza,
                              title: 'البيتزا',
                              color: Colors.red,
                            ),
                            _buildRestaurantCard(
                              icon: Icons.rice_bowl,
                              title: 'الطعام العربي',
                              color: Colors.brown,
                            ),
                            _buildRestaurantCard(
                              icon: Icons.fastfood,
                              title: 'الوجبات السريعة',
                              color: Colors.orange,
                            ),
                            _buildRestaurantCard(
                              icon: Icons.local_cafe,
                              title: 'المقاهي',
                              color: Colors.amber,
                            ),
                            _buildRestaurantCard(
                              icon: Icons.cake,
                              title: 'الحلويات',
                              color: Colors.pink,
                            ),
                            _buildRestaurantCard(
                              icon: Icons.local_dining,
                              title: 'المأكولات البحرية',
                              color: Colors.blue,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Features
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مميزات الخدمة:',
                                style: AppTextStyles.font18SemiBold.copyWith(
                                  color: textColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildFeatureRow(
                                icon: Icons.delivery_dining,
                                text: 'توصيل سريع خلال 30 دقيقة',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.track_changes,
                                text: 'تتبع الطلب مباشرة',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.local_offer,
                                text: 'عروض وخصومات يومية',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.star_rate,
                                text: 'تقييم المطاعم والوجبات',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.payment,
                                text: 'دفع آمن ومتعدد الطرق',
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // CTA Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const UnderDevelopmentPage(
                                    featureName: 'خدمات الطعام',
                                    description: 'نعمل على تطوير منصة طعام شاملة تضم أفضل المطاعم والمقاهي',
                                  ),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.deepOrange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'اطلب الآن',
                              style: AppTextStyles.font18SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildRestaurantCard({
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 32,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: AppTextStyles.font16SemiBold.copyWith(
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureRow({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.deepOrange,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.font14Medium.copyWith(
              color: textColor.withOpacity(0.8),
            ),
          ),
        ),
      ],
    );
  }
}
