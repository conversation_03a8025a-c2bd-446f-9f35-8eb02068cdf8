import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/pages/under_development/under_development_page.dart';

class BusTicketsPage extends StatelessWidget {
  const BusTicketsPage({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'حجز تذاكر الباص',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service banner
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xff2778A0),
                                const Color(0xff2778A0).withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: Icon(
                                  Icons.directions_bus,
                                  size: 40,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'حجز تذاكر الباص',
                                style: AppTextStyles.font24Bold.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'احجز رحلتك بسهولة وأمان',
                                style: AppTextStyles.font16Medium.copyWith(
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Popular routes
                        Text(
                          'الوجهات الشائعة:',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        _buildRouteCard(
                          from: 'الرياض',
                          to: 'جدة',
                          duration: '10 ساعات',
                          price: '150 ريال',
                          color: Colors.blue,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildRouteCard(
                          from: 'الرياض',
                          to: 'الدمام',
                          duration: '4 ساعات',
                          price: '80 ريال',
                          color: Colors.green,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildRouteCard(
                          from: 'جدة',
                          to: 'مكة المكرمة',
                          duration: '1.5 ساعة',
                          price: '25 ريال',
                          color: Colors.orange,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildRouteCard(
                          from: 'الرياض',
                          to: 'المدينة المنورة',
                          duration: '6 ساعات',
                          price: '120 ريال',
                          color: Colors.purple,
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Features
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مميزات الخدمة:',
                                style: AppTextStyles.font18SemiBold.copyWith(
                                  color: textColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildFeatureRow(
                                icon: Icons.confirmation_number,
                                text: 'حجز فوري ومؤكد',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.payment,
                                text: 'دفع آمن ومتعدد الطرق',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.schedule,
                                text: 'مواعيد متعددة يوميًا',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.airline_seat_recline_normal,
                                text: 'مقاعد مريحة ومكيفة',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.cancel,
                                text: 'إلغاء مجاني قبل 24 ساعة',
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // CTA Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const UnderDevelopmentPage(
                                    featureName: 'حجز تذاكر الباص',
                                    description: 'نعمل على تطوير نظام حجز تذاكر الباص لتوفير أسهل طريقة للسفر بين المدن',
                                  ),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xff2778A0),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'احجز تذكرتك الآن',
                              style: AppTextStyles.font18SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildRouteCard({
    required String from,
    required String to,
    required String duration,
    required String price,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.route,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      from,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: textColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.arrow_back,
                      size: 16,
                      color: textColor.withOpacity(0.5),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      to,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      duration,
                      style: AppTextStyles.font14Medium.copyWith(
                        color: textColor.withOpacity(0.6),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      price,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: color,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureRow({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: const Color(0xff2778A0),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.font14Medium.copyWith(
              color: textColor.withOpacity(0.8),
            ),
          ),
        ),
      ],
    );
  }
}
