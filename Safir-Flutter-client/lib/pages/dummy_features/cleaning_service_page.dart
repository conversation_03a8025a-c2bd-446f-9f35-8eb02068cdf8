import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/pages/under_development/under_development_page.dart';

class CleaningServicePage extends StatelessWidget {
  const CleaningServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'خدمات التنظيف',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service banner
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xff2778A0),
                                const Color(0xff2778A0).withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: Icon(
                                  Icons.cleaning_services,
                                  size: 40,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'خدمات التنظيف المتخصصة',
                                style: AppTextStyles.font24Bold.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '12 متخصصًا متاحًا لخدمتك',
                                style: AppTextStyles.font16Medium.copyWith(
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Service types
                        Text(
                          'أنواع خدمات التنظيف:',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 1.2,
                          children: [
                            _buildServiceCard(
                              icon: Icons.home_outlined,
                              title: 'تنظيف المنازل',
                              color: Colors.blue,
                            ),
                            _buildServiceCard(
                              icon: Icons.business,
                              title: 'تنظيف المكاتب',
                              color: Colors.green,
                            ),
                            _buildServiceCard(
                              icon: Icons.local_laundry_service,
                              title: 'تنظيف السجاد',
                              color: Colors.purple,
                            ),
                            _buildServiceCard(
                              icon: Icons.window,
                              title: 'تنظيف النوافذ',
                              color: Colors.orange,
                            ),
                            _buildServiceCard(
                              icon: Icons.kitchen,
                              title: 'تنظيف المطابخ',
                              color: Colors.red,
                            ),
                            _buildServiceCard(
                              icon: Icons.bathtub,
                              title: 'تنظيف الحمامات',
                              color: Colors.teal,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Features
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مميزات خدماتنا:',
                                style: AppTextStyles.font18SemiBold.copyWith(
                                  color: textColor,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildFeatureRow(
                                icon: Icons.verified_user,
                                text: 'عمالة مدربة ومؤهلة',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.eco,
                                text: 'مواد تنظيف صديقة للبيئة',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.schedule,
                                text: 'مواعيد مرنة تناسب جدولك',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.security,
                                text: 'ضمان الجودة والأمان',
                              ),
                              const SizedBox(height: 12),
                              _buildFeatureRow(
                                icon: Icons.price_check,
                                text: 'أسعار تنافسية ومناسبة',
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // CTA Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const UnderDevelopmentPage(
                                    featureName: 'خدمات التنظيف',
                                    description: 'نعمل على تطوير منصة تنظيف شاملة مع 12 متخصصًا لتوفير أفضل خدمات التنظيف',
                                  ),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xff2778A0),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'احجز خدمة التنظيف',
                              style: AppTextStyles.font18SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildServiceCard({
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 32,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: AppTextStyles.font16SemiBold.copyWith(
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureRow({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: const Color(0xff2778A0),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.font14Medium.copyWith(
              color: textColor.withOpacity(0.8),
            ),
          ),
        ),
      ],
    );
  }
}
