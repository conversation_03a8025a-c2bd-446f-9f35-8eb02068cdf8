import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/Core/styles/app_text_styles.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/pages/under_development/under_development_page.dart';

class ShoppingServicePage extends StatelessWidget {
  const ShoppingServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: customBackground,
          color: page,
        ),
        child: SafeArea(
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'خدمة التسوق',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Service banner
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.purple,
                                Colors.purple.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.shopping_bag,
                                size: 60,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'خدمة التسوق الذكي',
                                style: AppTextStyles.font24Bold.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'تسوق بذكاء واحصل على أفضل العروض',
                                style: AppTextStyles.font16Medium.copyWith(
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Categories
                        Text(
                          'الفئات المتاحة قريبًا:',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 1.2,
                          children: [
                            _buildCategoryCard(
                              icon: Icons.local_grocery_store,
                              title: 'البقالة',
                              color: Colors.green,
                            ),
                            _buildCategoryCard(
                              icon: Icons.checkroom,
                              title: 'الملابس',
                              color: Colors.pink,
                            ),
                            _buildCategoryCard(
                              icon: Icons.phone_android,
                              title: 'الإلكترونيات',
                              color: Colors.blue,
                            ),
                            _buildCategoryCard(
                              icon: Icons.home,
                              title: 'المنزل',
                              color: Colors.orange,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Features
                        Text(
                          'المميزات:',
                          style: AppTextStyles.font20SemiBold.copyWith(
                            color: textColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        _buildFeatureCard(
                          icon: Icons.local_offer,
                          title: 'عروض حصرية',
                          description: 'احصل على أفضل العروض والخصومات',
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildFeatureCard(
                          icon: Icons.compare,
                          title: 'مقارنة الأسعار',
                          description: 'قارن الأسعار واختر الأنسب لك',
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildFeatureCard(
                          icon: Icons.favorite,
                          title: 'قائمة الأمنيات',
                          description: 'احفظ المنتجات المفضلة لديك',
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // CTA Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const UnderDevelopmentPage(
                                    featureName: 'خدمة التسوق',
                                    description: 'نعمل على تطوير منصة تسوق متكاملة لتوفير أفضل تجربة تسوق لك',
                                  ),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'ابدأ التسوق',
                              style: AppTextStyles.font18SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildCategoryCard({
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 32,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: AppTextStyles.font16SemiBold.copyWith(
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: Colors.purple,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: AppTextStyles.font14Medium.copyWith(
                    color: textColor.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
