import 'package:flutter/material.dart';
import 'package:safir_client/pages/dummy_features/delivery_service_page.dart';
import 'package:safir_client/pages/dummy_features/shopping_service_page.dart';
import 'package:safir_client/pages/dummy_features/booking_service_page.dart';
import 'package:safir_client/pages/under_development/under_development_page.dart';

class ServiceNavigationHelper {
  static void navigateToService(BuildContext context, String? serviceTitle) {
    if (serviceTitle == null) {
      _navigateToUnderDevelopment(context, 'خدمة غير محددة');
      return;
    }

    // Convert to lowercase for easier matching
    String title = serviceTitle.toLowerCase();

    // Navigation based on service title
    if (title.contains('توصيل') || title.contains('delivery')) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const DeliveryServicePage()),
      );
    } else if (title.contains('تسوق') || title.contains('shopping') || title.contains('متجر')) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const ShoppingServicePage()),
      );
    } else if (title.contains('حجز') || title.contains('booking') || title.contains('موعد')) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const BookingServicePage()),
      );
    } else if (title.contains('طبي') || title.contains('medical') || title.contains('صحة')) {
      _navigateToUnderDevelopment(
        context,
        'الخدمات الطبية',
        'نعمل على تطوير منصة طبية متكاملة لتوفير أفضل الخدمات الصحية',
      );
    } else if (title.contains('تعليم') || title.contains('education') || title.contains('دروس')) {
      _navigateToUnderDevelopment(
        context,
        'الخدمات التعليمية',
        'نعمل على تطوير منصة تعليمية شاملة لجميع المراحل الدراسية',
      );
    } else if (title.contains('سيارة') || title.contains('car') || title.contains('نقل')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات السيارات',
        'نعمل على تطوير خدمات شاملة للسيارات والنقل',
      );
    } else if (title.contains('منزل') || title.contains('home') || title.contains('صيانة')) {
      _navigateToUnderDevelopment(
        context,
        'الخدمات المنزلية',
        'نعمل على تطوير خدمات منزلية متكاملة لراحتك',
      );
    } else if (title.contains('تجميل') || title.contains('beauty') || title.contains('صالون')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات التجميل',
        'نعمل على تطوير منصة شاملة لخدمات التجميل والعناية',
      );
    } else if (title.contains('رياضة') || title.contains('fitness') || title.contains('جيم')) {
      _navigateToUnderDevelopment(
        context,
        'الخدمات الرياضية',
        'نعمل على تطوير منصة رياضية متكاملة لجميع الأنشطة',
      );
    } else if (title.contains('طعام') || title.contains('food') || title.contains('مطعم')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات الطعام',
        'نعمل على تطوير منصة طعام شاملة لأفضل المطاعم',
      );
    } else if (title.contains('تنظيف') || title.contains('cleaning')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات التنظيف',
        'نعمل على تطوير خدمات تنظيف احترافية لمنزلك ومكتبك',
      );
    } else if (title.contains('إصلاح') || title.contains('repair') || title.contains('تصليح')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات الإصلاح',
        'نعمل على تطوير خدمات إصلاح شاملة لجميع احتياجاتك',
      );
    } else if (title.contains('استشارة') || title.contains('consultation') || title.contains('مشورة')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات الاستشارات',
        'نعمل على تطوير منصة استشارات متخصصة في جميع المجالات',
      );
    } else if (title.contains('قانون') || title.contains('legal') || title.contains('محامي')) {
      _navigateToUnderDevelopment(
        context,
        'الخدمات القانونية',
        'نعمل على تطوير منصة قانونية لتوفير أفضل الاستشارات القانونية',
      );
    } else if (title.contains('مال') || title.contains('finance') || title.contains('بنك')) {
      _navigateToUnderDevelopment(
        context,
        'الخدمات المالية',
        'نعمل على تطوير خدمات مالية آمنة ومتطورة',
      );
    } else if (title.contains('عقار') || title.contains('real estate') || title.contains('بيت')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات العقارات',
        'نعمل على تطوير منصة عقارية شاملة لجميع احتياجاتك السكنية',
      );
    } else if (title.contains('سفر') || title.contains('travel') || title.contains('سياحة')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات السفر والسياحة',
        'نعمل على تطوير منصة سفر متكاملة لأفضل تجربة سياحية',
      );
    } else if (title.contains('حيوان') || title.contains('pet') || title.contains('بيطري')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات الحيوانات الأليفة',
        'نعمل على تطوير خدمات شاملة للعناية بالحيوانات الأليفة',
      );
    } else if (title.contains('تصوير') || title.contains('photography') || title.contains('فيديو')) {
      _navigateToUnderDevelopment(
        context,
        'خدمات التصوير',
        'نعمل على تطوير منصة تصوير احترافية لجميع المناسبات',
      );
    } else if (title.contains('حفل') || title.contains('event') || title.contains('مناسبة')) {
      _navigateToUnderDevelopment(
        context,
        'تنظيم الفعاليات',
        'نعمل على تطوير خدمات تنظيم الفعاليات والمناسبات الخاصة',
      );
    } else {
      // Default case for unknown services
      _navigateToUnderDevelopment(context, serviceTitle);
    }
  }

  static void _navigateToUnderDevelopment(
    BuildContext context,
    String featureName, [
    String? description,
  ]) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UnderDevelopmentPage(
          featureName: featureName,
          description: description,
        ),
      ),
    );
  }

  // Helper method to check if a service has questions (is fully developed)
  static bool isServiceFullyDeveloped(List<dynamic>? questions) {
    return questions != null && questions.isNotEmpty;
  }

  // Helper method to get service status
  static String getServiceStatus(List<dynamic>? questions) {
    return isServiceFullyDeveloped(questions) ? 'متاح' : 'قيد التطوير';
  }

  // Helper method to get service status color
  static Color getServiceStatusColor(List<dynamic>? questions) {
    return isServiceFullyDeveloped(questions) 
        ? Colors.green 
        : Colors.orange;
  }
}
