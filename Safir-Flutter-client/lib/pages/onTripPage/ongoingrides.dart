import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/pages/loadingPage/loading.dart';
import 'package:safir_client/pages/onTripPage/booking_confirmation.dart';
import 'package:safir_client/pages/onTripPage/map_page.dart';
import 'package:safir_client/styles/styles.dart';
import 'package:safir_client/translations/translation.dart';
import 'package:safir_client/widgets/widgets.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../functions/functions.dart';

class OnGoingRides extends StatefulWidget {
  const OnGoingRides({super.key});

  @override
  State<OnGoingRides> createState() => _OnGoingRidesState();
}

class _OnGoingRidesState extends State<OnGoingRides> {
  bool _isLoading = true;
  final List _tripStops = [];
  @override
  void initState() {
    getHistoryData();
    super.initState();
  }

  navigate() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => BookingConfirmation(),
      ),
    );
  }

  naviagteridewithoutdestini() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => BookingConfirmation(type: 2),
      ),
    );
  }

  naviagterental() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => BookingConfirmation(type: 1),
      ),
    );
  }

  getHistoryData() async {
    setState(() {
      _isLoading = true;
      myHistoryPage.clear();
      myHistory.clear();
    });
    historyFiltter = 'on_trip=1';
    var val = await getHistory();
    if (val == 'success' && myHistory.isNotEmpty) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;

    return SafeArea(
      child: Material(
        child: RefreshIndicator(
          color: Colors.blue,
          onRefresh: () async {
            setState(() {
              _isLoading = true;
              myHistoryPage.clear();
              myHistory.clear();
            });
            historyFiltter = 'on_trip=1';
            var val = await getHistory();
            if (val == 'success' && myHistory.isNotEmpty) {
              setState(() => _isLoading = false);
            }
          },
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Container(
              height: media.height * 1,
              width: media.width * 1,
              color: myHistory.isEmpty ? page : page.withOpacity(0.9),
              child: Stack(
                children: [
                  Column(
                    children: [
                      onGoingRidesAppBar(media),
                      Expanded(
                        child: SizedBox(
                          width: media.width * 1,
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            child: Column(
                              children: [
                                (myHistory.isNotEmpty)
                                    ? onGoingRidesData(media)
                                    : (_isLoading == false)
                                        ? onGoingRidesEmptyData(media)
                                        : Container()
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (_isLoading) const Positioned(child: Loading())
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget onGoingRidesData(Size media) {
    return Column(
      children: myHistory
          .asMap()
          .map(
            (i, value) {
              return MapEntry(
                i,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () async {
                        setState(() => _isLoading = true);
                        addressList.clear();
                        // selectedHistory = i;
                        addressList.add(AddressList(
                          id: '1',
                          type: 'pickup',
                          address: myHistory[i]['pick_address'],
                          pickup: true,
                          latlng: LatLng(myHistory[i]['pick_lat'],
                              myHistory[i]['pick_lng']),
                        ));
                        if (_tripStops.isNotEmpty) {
                          for (var i = 0; i < _tripStops.length; i++) {
                            addressList.add(
                              AddressList(
                                id: _tripStops[i]['id'].toString(),
                                type: 'drop',
                                address: _tripStops[i]['address'],
                                latlng: LatLng(_tripStops[i]['latitude'],
                                    _tripStops[i]['longitude']),
                                pickup: false,
                              ),
                            );
                          }
                        }

                        if (myHistory[i]['drop_address'] != null &&
                            _tripStops.isEmpty) {
                          addressList.add(
                            AddressList(
                              id: '2',
                              type: 'drop',
                              pickup: false,
                              address: myHistory[i]['drop_address'],
                              latlng: LatLng(
                                myHistory[i]['drop_lat'],
                                myHistory[i]['drop_lng'],
                              ),
                            ),
                          );
                        }

                        ismulitipleride = true;

                        var val = await getUserDetails(id: myHistory[i]['id']);

                        //login page
                        if (val == true) {
                          setState(() => _isLoading = false);
                          if (myHistory[i]['is_rental'] == true) {
                            naviagterental();
                          } else if (myHistory[i]['is_rental'] == false &&
                              myHistory[i]['drop_address'] == null) {
                            naviagteridewithoutdestini();
                          } else {
                            navigate();
                          }
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(
                          top: media.width * 0.025,
                          bottom: media.width * 0.05,
                          left: media.width * 0.03,
                          right: media.width * 0.03,
                        ),
                        padding: EdgeInsets.all(media.width * 0.025),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: page,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                MyText(
                                  text: myHistory[i]['request_number'],
                                  size: media.width * fourteen,
                                  fontweight: FontWeight.w600,
                                  color: textColor,
                                ),
                                MyText(
                                  textAlign: TextAlign.end,
                                  text: 'Otp : ${myHistory[i]['ride_otp']}',
                                  size: media.width * twelve,
                                  fontweight: FontWeight.w600,
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                MyText(
                                  text: (myHistory[i]['accepted_at'] != null &&
                                          myHistory[i]['is_driver_arrived'] ==
                                              0)
                                      ? 'Accepted'
                                      : (myHistory[i]['is_driver_arrived'] ==
                                                  1 &&
                                              myHistory[i]['is_trip_start'] ==
                                                  0)
                                          ? 'Arrived'
                                          : (myHistory[i]['is_completed'] == 1)
                                              ? 'Completed'
                                              : 'Trip Started',
                                  color: (myHistory[i]['accepted_at'] != null &&
                                          myHistory[i]['is_driver_arrived'] ==
                                              0)
                                      ? Colors.yellow
                                      : (myHistory[i]['is_driver_arrived'] ==
                                                  1 &&
                                              myHistory[i]['is_trip_start'] ==
                                                  0)
                                          ? Colors.orange
                                          : online,
                                  size: media.width * fourteen,
                                ),
                                MyText(
                                  text: myHistory[i]['accepted_at'],
                                  size: media.width * twelve,
                                  fontweight: FontWeight.w600,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            const MySeparator(),
                            const SizedBox(height: 16),
                            Container(
                              padding: EdgeInsets.all(media.width * 0.02),
                              decoration: BoxDecoration(
                                color: hintColor.withOpacity(0.1),
                                borderRadius:
                                    BorderRadius.circular(media.width * 0.02),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    height: media.width * 0.13,
                                    width: media.width * 0.13,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                        image: NetworkImage(
                                          myHistory[i]['driverDetail']['data']
                                              ['profile_picture'],
                                        ),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        MyText(
                                          text: myHistory[i]['driverDetail']
                                              ['data']['name'],
                                          size: media.width * eighteen,
                                          fontweight: FontWeight.w600,
                                        ),
                                        Row(
                                          children: [
                                            Image.network(
                                              myHistory[i]['vehicle_type_image']
                                                  .toString(),
                                              width: media.width * 0.1,
                                            ),
                                            const SizedBox(width: 12),
                                            MyText(
                                              text: myHistory[i]
                                                  ['vehicle_type_name'],
                                              size: media.width * twelve,
                                              fontweight: FontWeight.w600,
                                              color: textColor,
                                            ),
                                            Container(
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 5,
                                              ),
                                              height: media.width * 0.05,
                                              width: 1,
                                              color: hintColor,
                                            ),
                                            MyText(
                                              text: myHistory[i]['driverDetail']
                                                      ['data']['car_number']
                                                  .toString(),
                                              size: media.width * twelve,
                                              fontweight: FontWeight.w600,
                                              maxLines: 1,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: media.width * 0.05,
                                  width: media.width * 0.05,
                                  alignment: Alignment.center,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.green,
                                  ),
                                  child: Container(
                                    height: media.width * 0.025,
                                    width: media.width * 0.025,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: MyText(
                                    text: myHistory[i]['pick_address'],
                                    size: media.width * twelve,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Column(
                              children: _tripStops
                                  .asMap()
                                  .map(
                                    (i, value) {
                                      return MapEntry(
                                        i,
                                        (i < _tripStops.length - 1)
                                            ? Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 8.0),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      height:
                                                          media.width * 0.05,
                                                      width: media.width * 0.05,
                                                      alignment:
                                                          Alignment.center,
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        color: Colors.red
                                                            .withOpacity(0.4),
                                                      ),
                                                      child: MyText(
                                                        text:
                                                            (i + 1).toString(),
                                                        color: const Color(
                                                            0xFFFF0000),
                                                        fontweight:
                                                            FontWeight.w600,
                                                        size: media.width *
                                                            twelve,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Expanded(
                                                      child: MyText(
                                                        text: _tripStops[i]
                                                            ['address'],
                                                        size: media.width *
                                                            twelve,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : Container(),
                                      );
                                    },
                                  )
                                  .values
                                  .toList(),
                            ),
                            if (myHistory[i]['drop_address'] != null)
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    height: media.width * 0.05,
                                    width: media.width * 0.05,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.red.withOpacity(0.4),
                                    ),
                                    child: Icon(
                                      Icons.location_on,
                                      size: media.width * 0.03,
                                      color: const Color(0xFFFF0000),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: MyText(
                                      text: myHistory[i]['drop_address'],
                                      size: media.width * twelve,
                                    ),
                                  ),
                                ],
                              ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                MyText(
                                  text: (myHistory[i]['is_rental'] == true)
                                      ? languages[choosenLanguage]
                                          ['text_rental']
                                      : 'normal',
                                  size: media.width * fourteen,
                                  color: textColor.withOpacity(0.5),
                                ),
                                const SizedBox(width: 4),
                                Container(
                                  height: media.width * 0.05,
                                  width: 2,
                                  color: textColor,
                                ),
                                const SizedBox(width: 4),
                                if (myHistory[i]['drop_address'] != null)
                                  Row(
                                    children: [
                                      showPaymentImage(
                                        media,
                                        myHistory[i]['payment_type']
                                            .toString()
                                            .split(',')
                                            .toList()[i],
                                      ),
                                      const SizedBox(width: 4),
                                      MyText(
                                        text:
                                            '${userDetails['currency_symbol']} ${myHistory[i]['request_eta_amount'].toString()}',
                                        size: media.width * fourteen,
                                        fontweight: FontWeight.w600,
                                        maxLines: 1,
                                      )
                                    ],
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
          .values
          .toList(),
    );
  }

  Widget showPaymentImage(Size media, String paymentType) {
    return SizedBox(
      width: media.width * 0.06,
      child: (paymentType == 'cash')
          ? Image.asset(
              AppAssets.cash,
              fit: BoxFit.contain,
            )
          : (paymentType == 'wallet')
              ? Image.asset(
                  AppAssets.wallet,
                  fit: BoxFit.contain,
                )
              : (paymentType == 'card')
                  ? Image.asset(
                      AppAssets.card,
                      fit: BoxFit.contain,
                    )
                  : (paymentType == 'upi')
                      ? Image.asset(
                          AppAssets.upi,
                          fit: BoxFit.contain,
                        )
                      : Container(),
    );
  }

  Widget onGoingRidesEmptyData(Size media) {
    return Container(
      alignment: Alignment.center,
      height: media.height * 0.7,
      width: media.width * 0.7,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppAssets.noOrder),
          fit: BoxFit.contain,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 12),
        child: MyText(
          text: languages[choosenLanguage]['text_noorder'],
          textAlign: TextAlign.center,
          fontweight: FontWeight.w800,
          size: media.width * sixteen,
        ),
      ),
    );
  }

  Widget onGoingRidesAppBar(Size media) {
    return Container(
      color: page,
      padding: EdgeInsets.symmetric(
        horizontal: media.width * 0.05,
        vertical: media.width * 0.03,
      ),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Icon(Icons.arrow_back_ios, color: textColor),
          ),
          const Spacer(),
          MyText(
            text: languages[choosenLanguage]['text_ongoing_rides'],
            size: 16,
            fontweight: FontWeight.bold,
          ),
          const Spacer(),
        ],
      ),
    );
  }
}
