import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/widgets/custom_close_button.dart';
import 'package:geolocator/geolocator.dart' as geolocs;
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:uuid/uuid.dart';
import 'package:permission_handler/permission_handler.dart' as perm;

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../NavigatorPages/pickcontacts.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';
import '../noInternet/noInternet.dart';
import 'booking_confirmation.dart';
import 'map_page.dart';
import 'package:flutter_map/flutter_map.dart' as fm;
// ignore: depend_on_referenced_packages
import 'package:latlong2/latlong.dart' as fmlt;

// ignore: must_be_immutable
class DropLocation extends StatefulWidget {
  dynamic from;
  DropLocation({super.key, this.from});

  @override
  State<DropLocation> createState() => _DropLocationState();
}

class _DropLocationState extends State<DropLocation>
    with WidgetsBindingObserver {
  GoogleMapController? _controller;
  final fm.MapController _fmController = fm.MapController();
  late PermissionStatus permission;
  Location location = Location();
  String _state = '';
  // ignore: unused_field
  dynamic _lastCenter;
  bool _isLoading = false;
  String sessionToken = const Uuid().v4();
  LatLng _center = const LatLng(41.4219057, -102.0840772);
  LatLng _centerLocation = const LatLng(41.4219057, -102.0840772);
  TextEditingController search = TextEditingController();
  String favNameText = '';
  bool _locationDenied = false;
  bool favAddressAdd = false;
  bool _getDropDetails = false;
  TextEditingController buyerName = TextEditingController();
  TextEditingController buyerNumber = TextEditingController();
  TextEditingController instructions = TextEditingController();
  final _debouncer = Debouncer(milliseconds: 1000);
  bool useMyDetails = false;
  bool useMyAddress = false;

  void _onMapCreated(GoogleMapController controller) {
    setState(() {
      _controller = controller;
      _controller?.setMapStyle(mapStyle);
    });
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    dropAddressConfirmation = '';
    useMyDetails = false;

    getLocs();
    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (_controller != null) {
        _controller?.setMapStyle(mapStyle);
      }
      if (locationAllowed == true) {
        if (positionStream == null || positionStream!.isPaused) {
          positionStreamData();
        }
      }
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    _controller = null;

    super.dispose();
  }

  getLocs() async {
    permission = await location.hasPermission();

    if (permission == PermissionStatus.denied ||
        permission == PermissionStatus.deniedForever) {
      setState(() {
        _state = '3';
        _isLoading = false;
      });
    } else if (permission == PermissionStatus.granted ||
        permission == PermissionStatus.grantedLimited) {
      var locs = await geolocs.Geolocator.getLastKnownPosition();
      if (addressList.length != 2 && widget.from == null) {
        if (locs != null) {
          setState(() {
            _center = LatLng(double.parse(locs.latitude.toString()),
                double.parse(locs.longitude.toString()));
            _centerLocation = LatLng(double.parse(locs.latitude.toString()),
                double.parse(locs.longitude.toString()));
          });
        } else {
          var loc = await geolocs.Geolocator.getCurrentPosition(
              desiredAccuracy: geolocs.LocationAccuracy.low);
          setState(() {
            _center = LatLng(double.parse(loc.latitude.toString()),
                double.parse(loc.longitude.toString()));
            _centerLocation = LatLng(double.parse(loc.latitude.toString()),
                double.parse(loc.longitude.toString()));
          });
        }
        setState(() {
          _center = addressList[0].latlng;
          dropAddressConfirmation = addressList[0].address;
        });
      } else if (widget.from != null && widget.from != 'add stop') {
        setState(() {
          _center = addressList[int.parse(widget.from) - 1].latlng;
          _centerLocation = addressList[int.parse(widget.from) - 1].latlng;
          dropAddressConfirmation =
              addressList[int.parse(widget.from) - 1].address;
        });
      } else if (widget.from == 'add stop') {
        if (locs != null) {
          setState(() {
            _center = LatLng(double.parse(locs.latitude.toString()),
                double.parse(locs.longitude.toString()));
            _centerLocation = LatLng(double.parse(locs.latitude.toString()),
                double.parse(locs.longitude.toString()));
          });
        } else {
          var loc = await geolocs.Geolocator.getCurrentPosition(
              desiredAccuracy: geolocs.LocationAccuracy.low);
          setState(() {
            _center = LatLng(double.parse(loc.latitude.toString()),
                double.parse(loc.longitude.toString()));
            _centerLocation = LatLng(double.parse(loc.latitude.toString()),
                double.parse(loc.longitude.toString()));
          });
        }
        setState(() {
          _center = addressList.firstWhere((e) => e.type == 'pickup').latlng;
          _centerLocation =
              addressList.firstWhere((e) => e.type == 'pickup').latlng;
          dropAddressConfirmation = addressList
              .firstWhere((element) => element.type == 'pickup')
              .address;

          useMyAddress = true;
        });
      } else {
        setState(() {
          _center = addressList.firstWhere((e) => e.type == 'drop').latlng;
          _centerLocation =
              addressList.firstWhere((e) => e.type == 'drop').latlng;
          if (addressList.length >= 2) {
            dropAddressConfirmation = addressList
                .firstWhere((element) => element.type == 'drop')
                .address;
          }
          useMyAddress = true;
        });
      }

      setState(() {
        _state = '3';
        _isLoading = false;
      });
    }
  }

  navigateLogout() {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const LoginView()),
      (route) => false,
    );
  }

  popFunction() {
    if (_getDropDetails == true) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return PopScope(
      canPop: popFunction(),
      onPopInvoked: (did) {
        if (_getDropDetails) {
          setState(() => _getDropDetails = false);
        }
      },
      child: SafeArea(
        child: Material(
          child: ValueListenableBuilder(
            valueListenable: valueNotifierHome.value,
            builder: (context, value, child) {
              return Directionality(
                textDirection: (languageDirection == 'rtl')
                    ? TextDirection.rtl
                    : TextDirection.ltr,
                child: Container(
                  height: media.height * 1,
                  width: media.width * 1,
                  color: page,
                  child: Stack(
                    children: [
                      SizedBox(
                        height: media.height * 1,
                        width: media.width * 1,
                        child: (_state == '3')
                            ? (mapType == 'google')
                                ? GoogleMap(
                                    onMapCreated: _onMapCreated,
                                    initialCameraPosition: CameraPosition(
                                      target: _center,
                                      zoom: 14.0,
                                    ),
                                    onCameraMove: (CameraPosition position) {
                                      //pick current location
                                      // setState(() {
                                      _centerLocation = position.target;
                                      // });
                                    },
                                    onCameraIdle: () async {
                                      if (useMyAddress == false) {
                                        var val = await geoCoding(
                                            _centerLocation.latitude,
                                            _centerLocation.longitude);
                                        setState(() {
                                          _center = _centerLocation;
                                          _lastCenter = _centerLocation;
                                          dropAddressConfirmation = val;
                                        });
                                      }
                                      if (useMyAddress == true) {
                                        setState(() {
                                          useMyAddress = false;
                                        });
                                      }
                                    },
                                    minMaxZoomPreference:
                                        const MinMaxZoomPreference(8.0, 20.0),
                                    myLocationButtonEnabled: false,
                                    buildingsEnabled: false,
                                    zoomControlsEnabled: false,
                                    myLocationEnabled: true,
                                  )
                                : fm.FlutterMap(
                                    mapController: _fmController,
                                    options: fm.MapOptions(
                                        onMapEvent: (v) async {
                                          if (v.source ==
                                                  fm.MapEventSource
                                                      .nonRotatedSizeChange &&
                                              addressList.isEmpty) {
                                            _centerLocation = LatLng(
                                                v.camera.center.latitude,
                                                v.camera.center.longitude);
                                            setState(() {});

                                            var val = await geoCoding(
                                                _centerLocation.latitude,
                                                _centerLocation.longitude);
                                            if (val != '') {
                                              setState(() {
                                                _center = _centerLocation;
                                                _lastCenter = _centerLocation;
                                                dropAddressConfirmation = val;
                                              });
                                            }
                                          }
                                          if (v.source ==
                                              fm.MapEventSource.dragEnd) {
                                            _centerLocation = LatLng(
                                                v.camera.center.latitude,
                                                v.camera.center.longitude);
                                            // setState(() {

                                            // });

                                            var val = await geoCoding(
                                                _centerLocation.latitude,
                                                _centerLocation.longitude);
                                            if (val != '') {
                                              setState(() {
                                                _center = _centerLocation;
                                                _lastCenter = _centerLocation;
                                                dropAddressConfirmation = val;
                                              });
                                            }
                                          }
                                        },
                                        onPositionChanged: (p, l) async {
                                          if (l == false) {
                                            _centerLocation = LatLng(
                                                p.center!.latitude,
                                                p.center!.longitude);
                                            setState(() {});

                                            var val = await geoCoding(
                                                _centerLocation.latitude,
                                                _centerLocation.longitude);
                                            if (val != '') {
                                              setState(() {
                                                if (addressList
                                                    .where((element) =>
                                                        element.type ==
                                                        'pickup')
                                                    .isNotEmpty) {
                                                  var add = addressList
                                                      .firstWhere((element) =>
                                                          element.type ==
                                                          'pickup');
                                                  add.address = val;
                                                  add.latlng = LatLng(
                                                      _centerLocation.latitude,
                                                      _centerLocation
                                                          .longitude);
                                                } else {
                                                  addressList.add(AddressList(
                                                    id: '1',
                                                    type: 'pickup',
                                                    address: val,
                                                    pickup: true,
                                                    latlng: LatLng(
                                                        _centerLocation
                                                            .latitude,
                                                        _centerLocation
                                                            .longitude),
                                                  ));
                                                }
                                              });
                                              _lastCenter = _centerLocation;
                                            }
                                          }
                                        },
                                        // ignore: deprecated_member_use
                                        interactiveFlags:
                                            ~fm.InteractiveFlag.doubleTapZoom,
                                        initialCenter: fmlt.LatLng(
                                            center.latitude, center.longitude),
                                        initialZoom: 16,
                                        onTap: (P, L) {
                                          setState(() {});
                                        }),
                                    children: [
                                      fm.TileLayer(
                                        // minZoom: 10,
                                        urlTemplate:
                                            'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                        userAgentPackageName: 'com.example.app',
                                      ),
                                      const fm.RichAttributionWidget(
                                        attributions: [],
                                      ),
                                    ],
                                  )
                            : (_state == '2')
                                ? locationPermissionAllTime(media)
                                : Container(),
                      ),
                      dropLocationMarker(media),
                      Positioned(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                        child: (_getDropDetails == false)
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  dropMyLocation(media),
                                  const SizedBox(height: 40),
                                  dropLocationFooter(media),
                                ],
                              )
                            : userInfo(media),
                      ),
                      dropLocationSearch(media),
                      if (favAddressAdd) addFavoriteAddress(media),
                      if (_locationDenied) dropLocationDenied(media),
                      if (_isLoading) const Positioned(child: Loading()),
                      if (internet == false) dropLocationNoInternet(media)
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget userInfo(Size media) {
    return Container(
      height: media.height * 1,
      color: Colors.transparent.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            color: page,
            width: media.width * 1,
            padding: EdgeInsets.all(media.width * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: media.width * 0.9,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        (widget.from != '1')
                            ? languages[choosenLanguage]['text_give_buyerdata']
                            : languages[choosenLanguage]['text_give_userdata'],
                        style: GoogleFonts.notoKufiArabic(
                          color: textColor,
                          fontSize: media.width * sixteen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          var nav = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const PickContact(from: '2'),
                            ),
                          );
                          if (nav) {
                            setState(() {
                              buyerName.text = pickedName;
                              buyerNumber.text = pickedNumber;
                            });
                          }
                        },
                        child: Icon(
                          Icons.contact_page_rounded,
                          color: textColor,
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                InkWell(
                  onTap: () {
                    setState(
                      () {
                        useMyDetails = !useMyDetails;
                        if (useMyDetails == true) {
                          buyerName.text = userDetails['name'].toString();
                          buyerNumber.text = userDetails['mobile'].toString();
                        } else {
                          buyerName.text = '';
                          buyerNumber.text = '';
                        }
                      },
                    );
                  },
                  child: Row(
                    children: [
                      Container(
                        alignment: Alignment.center,
                        height: media.width * 0.05,
                        width: media.width * 0.05,
                        decoration: BoxDecoration(
                          border: Border.all(color: textColor),
                        ),
                        child: useMyDetails
                            ? Icon(
                                Icons.done,
                                size: media.width * 0.04,
                                color: textColor,
                              )
                            : Container(),
                      ),
                      const SizedBox(width: 8),
                      MyText(
                        text: languages[choosenLanguage]
                            ['text_use_name_mobile'],
                        size: media.width * twelve,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: EdgeInsets.fromLTRB(
                    media.width * 0.03,
                    (languageDirection == 'rtl') ? media.width * 0.04 : 0,
                    media.width * 0.03,
                    media.width * 0.01,
                  ),
                  height: media.width * 0.1,
                  width: media.width * 0.9,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(media.width * 0.02),
                    color: page,
                  ),
                  child: TextField(
                    onChanged: (val) {
                      setState(() {});
                    },
                    controller: buyerName,
                    readOnly: (useMyDetails) ? true : false,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: languages[choosenLanguage]['text_name'],
                      hintStyle: GoogleFonts.notoKufiArabic(
                        color: textColor.withOpacity(0.3),
                        fontSize: media.width * twelve,
                      ),
                    ),
                    textAlignVertical: TextAlignVertical.center,
                    style: GoogleFonts.notoKufiArabic(
                      color: textColor,
                      fontSize: media.width * twelve,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: EdgeInsets.fromLTRB(
                    media.width * 0.03,
                    (languageDirection == 'rtl') ? media.width * 0.04 : 0,
                    media.width * 0.03,
                    media.width * 0.01,
                  ),
                  height: media.width * 0.1,
                  width: media.width * 0.9,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(media.width * 0.02),
                    color: page,
                  ),
                  child: TextField(
                    onChanged: (val) {
                      setState(() {});
                    },
                    controller: buyerNumber,
                    keyboardType: TextInputType.number,
                    readOnly: (useMyDetails) ? true : false,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      counterText: '',
                      hintText: languages[choosenLanguage]['text_givenumber'],
                      hintStyle: GoogleFonts.notoKufiArabic(
                        color: textColor.withOpacity(0.3),
                        fontSize: media.width * twelve,
                      ),
                    ),
                    textAlignVertical: TextAlignVertical.center,
                    style: GoogleFonts.notoKufiArabic(
                      color: textColor,
                      fontSize: media.width * twelve,
                    ),
                    maxLength: 20,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: EdgeInsets.fromLTRB(
                    media.width * 0.03,
                    (languageDirection == 'rtl') ? media.width * 0.04 : 0,
                    media.width * 0.03,
                    media.width * 0.01,
                  ),
                  width: media.width * 0.9,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(media.width * 0.02),
                    color: page,
                  ),
                  child: TextField(
                    controller: instructions,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      counterText: '',
                      hintText: languages[choosenLanguage]['text_instructions'],
                      hintStyle: GoogleFonts.notoKufiArabic(
                          color: textColor.withOpacity(0.3),
                          fontSize: media.width * twelve),
                    ),
                    textAlignVertical: TextAlignVertical.center,
                    style: GoogleFonts.notoKufiArabic(
                      color: textColor,
                      fontSize: media.width * twelve,
                    ),
                    maxLines: 4,
                    minLines: 2,
                  ),
                ),
                const SizedBox(height: 16),
                Button(
                  onTap: () async {
                    if (widget.from != null && widget.from != 'add stop') {
                      addressList[int.parse(widget.from) - 1].address =
                          dropAddressConfirmation;
                      addressList[int.parse(widget.from) - 1].latlng = _center;
                    } else if (widget.from == 'add stop' &&
                        buyerName.text.isNotEmpty &&
                        buyerNumber.text.isNotEmpty) {
                      var address = addressList[addressList.length - 1].address;
                      var type = addressList[addressList.length - 1].type;

                      var pickup = addressList[addressList.length - 1].pickup;
                      var id = addressList[addressList.length - 1].id;
                      var latlng = addressList[addressList.length - 1].latlng;

                      addressList[addressList.length - 1].id =
                          (addressList.length + 1).toString();
                      addressList[addressList.length - 1].type = 'drop';
                      addressList[addressList.length - 1].address =
                          dropAddressConfirmation;
                      addressList[addressList.length - 1].latlng = _center;

                      addressList[addressList.length - 1].pickup = false;

                      addressList.add(
                        AddressList(
                          id: id,
                          type: type,
                          address: address,
                          latlng: latlng,
                          pickup: pickup,
                        ),
                      );
                    } else if (widget.from == null) {
                      if (buyerName.text.isNotEmpty &&
                          buyerNumber.text.isNotEmpty) {}
                    }

                    if (addressList.length >= 2 &&
                        buyerName.text.isNotEmpty &&
                        buyerNumber.text.isNotEmpty &&
                        widget.from == null) {
                      ismulitipleride = false;

                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => BookingConfirmation(),
                        ),
                      );
                    } else if (addressList.length >= 2 &&
                        buyerName.text.isNotEmpty &&
                        buyerNumber.text.isNotEmpty &&
                        widget.from != null) {
                      Navigator.pop(context, true);
                    } else if (addressList.length == 1 &&
                        widget.from == '1' &&
                        buyerName.text.isNotEmpty &&
                        buyerNumber.text.isNotEmpty &&
                        widget.from != null) {
                      Navigator.pop(context, true);
                    }
                  },
                  text: languages[choosenLanguage]['text_confirm'],
                  color:
                      (buyerName.text.isNotEmpty && buyerNumber.text.isNotEmpty)
                          ? buttonColor
                          : Colors.grey,
                  borcolor:
                      (buyerName.text.isNotEmpty && buyerNumber.text.isNotEmpty)
                          ? buttonColor
                          : Colors.grey,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget locationPermissionAllTime(Size media) {
    return Container(
      height: media.height * 1,
      width: media.width * 1,
      alignment: Alignment.center,
      child: Container(
        padding: EdgeInsets.all(media.width * 0.05),
        width: media.width * 0.9,
        height: 160,
        decoration: BoxDecoration(
          color: page,
          boxShadow: [
            BoxShadow(
              blurRadius: 5,
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
            )
          ],
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languages[choosenLanguage]['text_loc_permission'],
              style: GoogleFonts.notoKufiArabic(
                fontSize: media.width * sixteen,
                color: textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () async {
                  setState(() => _state = '');
                  await location.requestPermission();
                  getLocs();
                },
                child: Text(
                  languages[choosenLanguage]['text_ok'],
                  style: GoogleFonts.notoKufiArabic(
                    fontWeight: FontWeight.bold,
                    fontSize: media.width * twenty,
                    color: buttonColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget dropLocationFooter(Size media) {
    return Container(
      decoration: BoxDecoration(
        color: page,
        image: customBackground,
      ),
      width: media.width * 1,
      padding: EdgeInsets.all(media.width * 0.05),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: media.width * 0.03,
              vertical: media.width * 0.01,
            ),
            height: media.width * 0.1,
            width: media.width * 0.9,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey,
                width: 1.5,
              ),
              borderRadius: BorderRadius.circular(media.width * 0.02),
              // color: page,
            ),
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Container(
                  height: media.width * 0.04,
                  width: media.width * 0.04,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xffFF0000).withOpacity(0.3),
                  ),
                  child: Container(
                    height: media.width * 0.02,
                    width: media.width * 0.02,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xffFF0000),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: (dropAddressConfirmation == '')
                      ? Text(
                          languages[choosenLanguage]['text_pickdroplocation'],
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: media.width * twelve,
                            color: hintColor,
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              width: media.width * 0.7,
                              child: Text(
                                dropAddressConfirmation,
                                style: GoogleFonts.notoSans(
                                  fontSize: media.width * twelve,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (favAddress.length < 4)
                              InkWell(
                                onTap: () async {
                                  if (favAddress
                                      .where((element) =>
                                          element['pick_address'] ==
                                          dropAddressConfirmation)
                                      .isEmpty) {
                                    setState(
                                      () {
                                        favSelectedAddress =
                                            dropAddressConfirmation;
                                        favLat = _center.latitude;
                                        favLng = _center.longitude;
                                        favAddressAdd = true;
                                      },
                                    );
                                  }
                                },
                                child: Icon(
                                  Icons.favorite_outline,
                                  size: media.width * 0.05,
                                  color: favAddress
                                          .where((element) =>
                                              element['pick_address'] ==
                                              dropAddressConfirmation)
                                          .isEmpty
                                      ? (isDarkTheme == true)
                                          ? Colors.white
                                          : textColor
                                      : buttonColor,
                                ),
                              ),
                          ],
                        ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Button(
            color: buttonColor,
            textcolor: buttonText,
            onTap: () async {
              if (dropAddressConfirmation != '') {
                //remove in envato
                if (widget.from == null) {
                  if (addressList
                      .where((element) => element.type == 'drop')
                      .isEmpty) {
                    addressList.add(AddressList(
                        id: (addressList.length + 1).toString(),
                        type: 'drop',
                        address: dropAddressConfirmation,
                        latlng: _center,
                        pickup: false));
                  } else {
                    addressList
                        .firstWhere((element) => element.type == 'drop')
                        .address = dropAddressConfirmation;
                    addressList
                        .firstWhere((element) => element.type == 'drop')
                        .latlng = _center;
                  }
                } else if (widget.from != null) {
                  if (widget.from != null && widget.from != 'add stop') {
                    addressList[int.parse(widget.from) - 1].address =
                        dropAddressConfirmation;
                    addressList[int.parse(widget.from) - 1].latlng = _center;
                  } else if (widget.from == 'add stop') {
                    var address = addressList[addressList.length - 1].address;
                    var type = addressList[addressList.length - 1].type;

                    var pickup = addressList[addressList.length - 1].pickup;
                    var id = addressList[addressList.length - 1].id;
                    var latlng = addressList[addressList.length - 1].latlng;

                    addressList[addressList.length - 1].id =
                        (addressList.length + 1).toString();
                    addressList[addressList.length - 1].type = 'drop';
                    addressList[addressList.length - 1].address =
                        dropAddressConfirmation;
                    addressList[addressList.length - 1].latlng = _center;

                    addressList[addressList.length - 1].pickup = false;

                    addressList.add(AddressList(
                        id: id,
                        type: type,
                        address: address,
                        latlng: latlng,
                        pickup: pickup));
                  }
                  Navigator.pop(context, true);
                }
                if (addressList.length >= 2 && widget.from == null) {
                  ismulitipleride = false;

                  var val = await Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => BookingConfirmation()));
                  if (val) {
                    setState(() {});
                  }
                }
                if (addressList.length >= 2 && widget.from == null) {
                  ismulitipleride = false;
                  // ignore: use_build_context_synchronously
                  var val = await Navigator.pushReplacement(
                    // ignore: use_build_context_synchronously
                    context,
                    MaterialPageRoute(
                      builder: (context) => BookingConfirmation(),
                    ),
                  );
                  if (val) {
                    setState(() {});
                  }
                }
              }
            },
            text: languages[choosenLanguage]['text_confirm'],
          )
        ],
      ),
    );
  }

  Widget dropMyLocation(Size media) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: InkWell(
        onTap: () async {
          if (locationAllowed == true) {
            if (currentLocation != null) {
              _controller?.animateCamera(
                CameraUpdate.newLatLngZoom(currentLocation, 18.0),
              );
              center = currentLocation;
            } else {
              _controller?.animateCamera(
                CameraUpdate.newLatLngZoom(center, 18.0),
              );
            }
          } else {
            if (serviceEnabled == true) {
              setState(() => _locationDenied = true);
            } else {
              // await location.requestService();
              await geolocs.Geolocator.getCurrentPosition(
                  desiredAccuracy: geolocs.LocationAccuracy.low);
              if (await geolocs.GeolocatorPlatform.instance
                  .isLocationServiceEnabled()) {
                setState(() => _locationDenied = true);
              }
            }
          }
        },
        child: Container(
          height: media.width * 0.1,
          width: media.width * 0.1,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                blurRadius: 2,
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 2,
              )
            ],
            color: page,
            borderRadius: BorderRadius.circular(media.width * 0.02),
          ),
          child: Icon(Icons.my_location_sharp, color: textColor),
        ),
      ),
    );
  }

  Widget dropLocationSearch(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        padding: EdgeInsets.fromLTRB(
          media.width * 0.05,
          12.5,
          media.width * 0.05,
          0,
        ),
        width: media.width * 1,
        height: (addAutoFill.isNotEmpty) ? media.height * 1 : null,
        // color: (addAutoFill.isEmpty) ? Colors.transparent : page,
        decoration: BoxDecoration(
          color: (addAutoFill.isEmpty) ? Colors.transparent : page,
          image: (addAutoFill.isEmpty) ? null : customBackground,
        ),
        child: Column(
          children: [
            dropLocationAppBar(media),
            const SizedBox(height: 20),
            if (addAutoFill.isNotEmpty) addressesAutoFillData(media),
          ],
        ),
      ),
    );
  }

  Widget dropLocationDenied(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () => setState(() => _locationDenied = false),
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2.0,
                    spreadRadius: 2.0,
                    color: Colors.black.withOpacity(0.2),
                  )
                ],
              ),
              child: Column(
                children: [
                  SizedBox(
                    width: media.width * 0.8,
                    child: Text(
                      languages[choosenLanguage]['text_open_loc_settings'],
                      style: GoogleFonts.notoKufiArabic(
                        fontSize: media.width * sixteen,
                        color: textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(height: media.width * 0.05),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () async => await perm.openAppSettings(),
                        child: Text(
                          languages[choosenLanguage]['text_open_settings'],
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: media.width * sixteen,
                            color: buttonColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          setState(() {
                            _locationDenied = false;
                            _isLoading = true;
                          });
                          getLocs();
                        },
                        child: Text(
                          languages[choosenLanguage]['text_done'],
                          style: GoogleFonts.notoKufiArabic(
                            fontSize: media.width * sixteen,
                            color: buttonColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget addressesAutoFillData(Size media) {
    return Container(
      height: media.height * 0.45,
      padding: EdgeInsets.all(media.width * 0.02),
      width: media.width * 0.9,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(media.width * 0.05),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: addAutoFill
              .asMap()
              .map(
                (i, value) {
                  return MapEntry(
                    i,
                    (i < 7)
                        ? Container(
                            padding: EdgeInsets.symmetric(
                              vertical: media.width * 0.04,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () async {
                                    // ignore: prefer_typing_uninitialized_variables
                                    var val;
                                    val = await geoCodingForLatLng(
                                      addAutoFill[i]['fullText'],
                                    );
                                    setState(
                                      () {
                                        useMyAddress = true;
                                        _center = (addAutoFill[i]
                                                    ['description'] !=
                                                null)
                                            ? val
                                            : LatLng(
                                                double.parse(addAutoFill[i]
                                                        ['lat']
                                                    .toString()),
                                                double.parse(addAutoFill[i]
                                                        ['lon']
                                                    .toString()),
                                              );
                                        dropAddressConfirmation =
                                            (addAutoFill[i]['description'] !=
                                                    null)
                                                ? addAutoFill[i]['description']
                                                : addAutoFill[i]
                                                    ['display_name'];
                                        if (mapType == 'google') {
                                          _controller?.moveCamera(
                                            CameraUpdate.newLatLngZoom(
                                                _center, 14.0),
                                          );
                                        } else {
                                          _fmController.move(
                                            fmlt.LatLng(_center.latitude,
                                                center.longitude),
                                            14,
                                          );
                                        }
                                      },
                                    );
                                    FocusManager.instance.primaryFocus
                                        ?.unfocus();
                                    addAutoFill.clear();
                                    search.text = '';
                                  },
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      (addAutoFill[i]['description'] != null)
                                          ? addAutoFill[i]['description']
                                          : addAutoFill[i]['display_name'],
                                      style: GoogleFonts.notoSans(
                                        fontSize: media.width * twelve,
                                        color: textColor,
                                      ),
                                      maxLines: 2,
                                    ),
                                  ),
                                ),
                                Container(
                                  height: media.width * 0.1,
                                  width: media.width * 0.1,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.grey[200],
                                  ),
                                  child: const Icon(Icons.access_time),
                                ),
                              ],
                            ),
                          )
                        : Container(),
                  );
                },
              )
              .values
              .toList(),
        ),
      ),
    );
  }

  Widget dropLocationAppBar(Size media) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          // onTap: () {
          //   if (addAutoFill.isEmpty) {
          //     Navigator.pop(context);
          //   } else {
          //     setState(() {
          //       addAutoFill.clear();
          //       search.text = '';
          //     });
          //   }
          // },
          onTap: () {
            if (_getDropDetails == false) {
              Navigator.pop(context);
            } else {
              setState(() => _getDropDetails = false);
            }
          },
          child: Container(
            height: media.width * 0.1,
            width: media.width * 0.1,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 2,
                )
              ],
              color: page,
            ),
            alignment: Alignment.center,
            child: Icon(Icons.arrow_back, color: textColor),
          ),
        ),
        Container(
          height: media.width * 0.1,
          width: media.width * 0.75,
          padding: EdgeInsets.symmetric(horizontal: media.width * 0.05),
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 2,
                blurRadius: 2,
              )
            ],
            color: page,
            borderRadius: BorderRadius.circular(media.width * 0.05),
          ),
          child: TextField(
            controller: search,
            autofocus: (widget.from == 'add stop') ? true : false,
            decoration: InputDecoration(
              contentPadding: (languageDirection == 'rtl')
                  ? EdgeInsets.only(bottom: media.width * 0.03)
                  : EdgeInsets.only(bottom: media.width * 0.042),
              border: InputBorder.none,
              hintText: languages[choosenLanguage]['text_4lettersforautofill'],
              hintStyle: GoogleFonts.notoKufiArabic(
                fontSize: media.width * twelve,
                color: textColor.withOpacity(0.4),
              ),
            ),
            style: GoogleFonts.notoKufiArabic(color: textColor),
            maxLines: 1,
            onChanged: (val) {
              _debouncer.run(
                () {
                  if (val.length >= 3) {
                    if (storedAutoAddress
                        .where((element) => element['description']
                            .toString()
                            .toLowerCase()
                            .contains(val.toLowerCase()))
                        .isNotEmpty) {
                      addAutoFill.removeWhere((element) =>
                          element['description']
                              .toString()
                              .toLowerCase()
                              .contains(val.toLowerCase()) ==
                          false);
                      storedAutoAddress
                          .where((element) => element['description']
                              .toString()
                              .toLowerCase()
                              .contains(val.toLowerCase()))
                          .forEach((element) {
                        addAutoFill.add(element);
                      });
                      valueNotifierHome.incrementNotifier();
                    } else {
                      getAutocomplete(
                        val,
                        sessionToken,
                        _center.latitude,
                        _center.longitude,
                      );
                    }
                  } else if (val.isEmpty) {
                    setState(() => addAutoFill.clear());
                  }
                },
              );
            },
          ),
        )
      ],
    );
  }

  Widget dropLocationMarker(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        alignment: Alignment.center,
        child: Column(
          children: [
            SizedBox(
              height: (media.height / 2) - media.width * 0.08,
            ),
            Image.asset(
              AppAssets.dropMarker,
              width: media.width * 0.07,
              height: media.width * 0.08,
            ),
          ],
        ),
      ),
    );
  }

  Widget addFavoriteAddress(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomCloseButton(
              media: media,
              onTap: () {
                setState(
                  () {
                    favName = '';
                    favAddressAdd = false;
                  },
                );
              },
            ),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  Text(
                    languages[choosenLanguage]['text_saveaddressas'],
                    style: GoogleFonts.notoKufiArabic(
                      fontSize: media.width * sixteen,
                      color: textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    favSelectedAddress,
                    style: GoogleFonts.notoKufiArabic(
                      fontSize: media.width * twelve,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          setState(() => favName = 'Home');
                        },
                        child: Container(
                          padding: EdgeInsets.all(media.width * 0.01),
                          child: Row(
                            children: [
                              Container(
                                height: media.width * 0.04,
                                width: media.width * 0.04,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isDarkTheme
                                        ? Colors.white
                                        : const Color(0xff222222),
                                    width: 1.2,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: (favName == 'Home')
                                    ? Container(
                                        height: media.width * 0.025,
                                        width: media.width * 0.025,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: isDarkTheme
                                              ? Colors.white
                                              : const Color(0xff222222),
                                        ),
                                      )
                                    : Container(),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                languages[choosenLanguage]['text_home'],
                                style: GoogleFonts.notoKufiArabic(
                                  color: textColor,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          setState(() => favName = 'Work');
                        },
                        child: Container(
                          padding: EdgeInsets.all(media.width * 0.01),
                          child: Row(
                            children: [
                              Container(
                                height: media.width * 0.04,
                                width: media.width * 0.04,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isDarkTheme
                                        ? Colors.white
                                        : const Color(0xff222222),
                                    width: 1.2,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: (favName == 'Work')
                                    ? Container(
                                        height: media.width * 0.025,
                                        width: media.width * 0.025,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: isDarkTheme
                                              ? Colors.white
                                              : const Color(0xff222222),
                                        ),
                                      )
                                    : Container(),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                languages[choosenLanguage]['text_work'],
                                style: GoogleFonts.notoKufiArabic(
                                  color: textColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          setState(() => favName = 'Others');
                        },
                        child: Container(
                          padding: EdgeInsets.all(media.width * 0.01),
                          child: Row(
                            children: [
                              Container(
                                height: media.width * 0.04,
                                width: media.width * 0.04,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isDarkTheme
                                        ? Colors.white
                                        : const Color(0xff222222),
                                    width: 1.2,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: (favName == 'Others')
                                    ? Container(
                                        height: media.width * 0.025,
                                        width: media.width * 0.025,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: isDarkTheme
                                              ? Colors.white
                                              : const Color(0xff222222),
                                        ),
                                      )
                                    : Container(),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                languages[choosenLanguage]['text_others'],
                                style: GoogleFonts.notoKufiArabic(
                                  color: textColor,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (favName == 'Others')
                    Container(
                      padding: EdgeInsets.all(media.width * 0.025),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: borderLines, width: 1.2),
                      ),
                      child: TextField(
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: languages[choosenLanguage]
                              ['text_enterfavname'],
                          hintStyle: GoogleFonts.notoSans(
                            fontSize: media.width * twelve,
                            color: hintColor,
                          ),
                        ),
                        style: GoogleFonts.notoSans(
                          fontSize: media.width * sixteen,
                          color: textColor,
                        ),
                        maxLines: 1,
                        onChanged: (val) => setState(() => favNameText = val),
                      ),
                    ),
                  const SizedBox(height: 20),
                  Button(
                    color: buttonColor,
                    textcolor: buttonText,
                    onTap: () async {
                      if (favName == 'Others' && favNameText != '') {
                        setState(() => _isLoading = true);
                        var val = await addFavLocation(
                          favLat,
                          favLng,
                          favSelectedAddress,
                          favNameText,
                        );
                        setState(() {
                          _isLoading = false;
                          if (val == true) {
                            favLat = '';
                            favLng = '';
                            favSelectedAddress = '';
                            favNameText = '';
                            favName = 'Home';
                            favAddressAdd = false;
                          } else if (val == 'logout') {
                            navigateLogout();
                          }
                        });
                      } else if (favName == 'Home' || favName == 'Work') {
                        setState(() => _isLoading = true);
                        var val = await addFavLocation(
                            favLat, favLng, favSelectedAddress, favName);
                        setState(() {
                          _isLoading = false;
                          if (val == true) {
                            favLat = '';
                            favLng = '';
                            favSelectedAddress = '';
                            favNameText = '';
                            favName = 'Home';
                            favAddressAdd = false;
                          } else if (val == 'logout') {
                            navigateLogout();
                          }
                        });
                      }
                    },
                    text: languages[choosenLanguage]['text_confirm'],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget dropLocationNoInternet(Size media) {
    return Positioned(
      top: 0,
      child: NoInternet(
        onTap: () => setState(() => internetTrue()),
      ),
    );
  }
}
