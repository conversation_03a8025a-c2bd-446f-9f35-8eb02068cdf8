import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';
import 'map_page.dart';

class Review extends StatefulWidget {
  const Review({super.key});

  @override
  State<Review> createState() => _ReviewState();
}

double review = 0.0;
String feedback = '';

class _ReviewState extends State<Review> {
  bool _loading = false;

  @override
  void initState() {
    review = 0.0;
    super.initState();
  }

  navigateLogout() {
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginView()),
        (route) => false);
  }

  //navigate
  navigate() {
    dropStopList.clear();
    addressList.clear();
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const Maps()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: ValueListenableBuilder(
          valueListenable: valueNotifierHome.value,
          builder: (context, value, child) {
            return Directionality(
              textDirection: (languageDirection == 'rtl')
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              child: Stack(
                children: [
                  Container(
                    height: media.height * 1,
                    width: media.width * 1,
                    padding: EdgeInsets.all(media.width * 0.05),
                    color: page,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 20),
                        driverDetails(media),
                        const SizedBox(height: 20),
                        reviewStars(media),
                        const SizedBox(height: 38),
                        reviewFeedbackField(media),
                        const Spacer(),
                        reviewButton(media),
                      ],
                    ),
                  ),
                  if (_loading == true) const Positioned(child: Loading())
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget reviewStars(Size media) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          InkWell(
            onTap: () => setState(() => review = 1.0),
            child: Icon(
              Icons.star,
              size: media.width * 0.08,
              color: (review >= 1) ? buttonColor : Colors.grey,
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => setState(() => review = 2.0),
            child: Icon(
              Icons.star,
              size: media.width * 0.08,
              color: (review >= 2) ? buttonColor : Colors.grey,
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => setState(() => review = 3.0),
            child: Icon(
              Icons.star,
              size: media.width * 0.08,
              color: (review >= 3) ? buttonColor : Colors.grey,
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => setState(() => review = 4.0),
            child: Icon(
              Icons.star,
              size: media.width * 0.08,
              color: (review >= 4) ? buttonColor : Colors.grey,
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => setState(() => review = 5.0),
            child: Icon(
              Icons.star,
              size: media.width * 0.08,
              color: (review == 5) ? buttonColor : Colors.grey,
            ),
          )
        ],
      ),
    );
  }

  Widget reviewFeedbackField(Size media) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      width: media.width * 0.9,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.5,
          color:
              isDarkTheme == true ? Colors.grey : Colors.grey.withOpacity(0.1),
        ),
      ),
      child: TextField(
        maxLines: 4,
        onChanged: (val) {
          setState(() => feedback = val);
        },
        style: GoogleFonts.notoKufiArabic(color: textColor),
        decoration: InputDecoration(
          hintText: languages[choosenLanguage]['text_feedback'],
          hintStyle: GoogleFonts.notoKufiArabic(
            color: isDarkTheme == true
                ? textColor.withOpacity(0.4)
                : Colors.grey.withOpacity(0.6),
          ),
          border: InputBorder.none,
        ),
      ),
    );
  }

  Widget reviewButton(Size media) {
    return Button(
      onTap: () async {
        if (review >= 1.0) {
          setState(() => _loading = true);
          var result = await userRating();
          if (result == true) {
            navigate();
            _loading = false;
          } else if (result == 'logout') {
            navigateLogout();
          } else {
            setState(() => _loading = false);
          }
        }
      },
      text: languages[choosenLanguage]['text_submit'],
      textcolor: (review >= 1.0) ? buttonText : Colors.black,
      color: (review >= 1.0) ? buttonColor : Colors.grey,
    );
  }

  Widget driverDetails(Size media) {
    return Column(
      children: [
        if (userRequestData.isNotEmpty)
          Container(
            height: media.width * 0.25,
            width: media.width * 0.25,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: NetworkImage(
                  userRequestData['driverDetail']['data']['profile_picture'],
                ),
                fit: BoxFit.cover,
              ),
            ),
          ),
        const SizedBox(height: 20),
        MyText(
          text: (userRequestData.isNotEmpty)
              ? userRequestData['driverDetail']['data']['name']
              : '',
          size: media.width * sixteen,
        ),
      ],
    );
  }
}
