{"project_info": {"project_number": "543510566260", "firebase_url": "https://safir-50f13-default-rtdb.firebaseio.com", "project_id": "safir-50f13", "storage_bucket": "safir-50f13.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:543510566260:android:d596a5eaf920665a2f4821", "android_client_info": {"package_name": "com.safir.app"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBoDwWehnKGhCOEHIh4sgxaPAxDSpAETMs"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:543510566260:android:4037d406ea81c3e82f4821", "android_client_info": {"package_name": "com.safir.holding.app"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBoDwWehnKGhCOEHIh4sgxaPAxDSpAETMs"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:543510566260:android:0960f761e862cb512f4821", "android_client_info": {"package_name": "com.safir.partner"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBoDwWehnKGhCOEHIh4sgxaPAxDSpAETMs"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}